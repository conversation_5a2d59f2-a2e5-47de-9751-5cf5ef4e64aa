{"permissions": {"allow": ["<PERSON><PERSON>(./test_runner.py --client-only)", "<PERSON><PERSON>(./test_runner.py --core-only)", "Bash(alembic downgrade:*)", "Bash(alembic revision:*)", "Bash(alembic stamp:*)", "Bash(alembic upgrade:*)", "<PERSON><PERSON>(cat:*)", "Bash(cd /Users/<USER>/Code/KAPI/landing && npm install)", "Bash(cd:*)", "<PERSON><PERSON>(chmod:*)", "Bash(cp:*)", "<PERSON><PERSON>(echo:*)", "Bash(find:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(grep:*)", "<PERSON><PERSON>(kapi:*)", "Bash(ln:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(node:*)", "Bash(npm install:*)", "Bash(npm rebuild:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "Bash(npm run lint)", "Bash(npm run lint:*)", "Bash(npm run start:*)", "Bash(npm run tsc:*)", "Bash(npm run typecheck:*)", "Bash(npm test)", "Bash(npm test:*)", "Bash(npm uninstall:*)", "Bash(npx cypress run:*)", "Bash(npx eslint:*)", "Bash(npx http-server:*)", "<PERSON><PERSON>(npx playwright install:*)", "<PERSON><PERSON>(npx playwright test:*)", "Bash(npx tsc:*)", "Bash(pip install:*)", "<PERSON><PERSON>(pip show:*)", "Bash(pnpm build)", "Bash(pnpm install)", "<PERSON><PERSON>(poetry:*)", "Bash(pytest:*)", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(uvicorn:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(curl:*)", "Bash(npx:*)", "Bash(npm run test:*)", "Bash(npm run analyze-code:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm run type-check:*)", "Bash(git merge-base:*)", "Bash(git checkout:*)", "Bash(git stash:*)", "Bash(git log:*)", "<PERSON><PERSON>(git shortlog:*)", "Bash(git cherry-pick:*)", "Bash(git merge:*)", "Bash(npm run chromadb:test:*)", "Bash(git rebase:*)", "<PERSON><PERSON>(docker:*)", "WebFetch(domain:docs.anthropic.com)", "Bash(./bin/kapi:*)", "Bash(KAPI_USE_MOCK_AUTH=true ./bin/kapi ask \"how to list files in terminal?\")", "Bash(NODE_ENV=development USE_DEVELOPMENT_AUTH=true ./bin/kapi status)", "Bash(NODE_ENV=development USE_DEVELOPMENT_AUTH=true timeout 10s ./bin/kapi)", "Bash(NODE_ENV=development USE_DEVELOPMENT_AUTH=true ./bin/kapi --help)", "Bash(NODE_ENV=development USE_DEVELOPMENT_AUTH=true ./bin/kapi login)", "Bash(NODE_ENV=development USE_DEVELOPMENT_AUTH=true ./bin/kapi search \"test\")", "Bash(NODE_ENV=development USE_DEVELOPMENT_AUTH=true ./bin/kapi search \"package\")"], "deny": []}}