const fs = require('fs').promises;
const path = require('path');

// Test script to verify backwards-build template compliance
async function testBackwardsBuildCompliance() {
  console.log('🧪 Testing backwards-build template compliance...');
  
  // Mock the ProjectCreation service behavior with updated structure
  const projectPath = '/Users/<USER>/Code/KAPI/demo-backwards-build-test';
  
  try {
    // Clean up any existing test project
    await fs.rm(projectPath, { recursive: true, force: true });
    
    // Create project structure following backwards-build template
    await fs.mkdir(projectPath, { recursive: true });
    
    // 1. Documentation structure (docs/)
    await fs.mkdir(path.join(projectPath, 'docs/architecture'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'docs/state'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'docs/roadmap'), { recursive: true });
    
    // 2. RevealJS slides structure (slides/)
    await fs.mkdir(path.join(projectPath, 'slides/css'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'slides/assets'), { recursive: true });
    
    // 3. Test structure (tests/)
    await fs.mkdir(path.join(projectPath, 'tests/backend/unit'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'tests/backend/integration'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'tests/backend/e2e'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'tests/frontend/e2e'), { recursive: true });
    
    // 4. Standard project structure
    await fs.mkdir(path.join(projectPath, 'backend/src'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'frontend/src'), { recursive: true });
    
    // Generate documentation files
    const docsReadme = `# Task Management App Documentation

## Overview
A modern collaborative task management application built with backwards-build methodology.

## Quick Links
- [Vision](./vision.md)
- [Architecture](./architecture/system-overview.md)
- [Current State](./state/current-state.md)
- [Roadmap](./roadmap/milestones.md)

## Getting Started
Follow the backwards-build workflow:
1. Review all documentation
2. Examine presentation slides
3. Study test specifications
4. Implement according to tests

## Contributing
All contributions must follow the backwards-build methodology.`;
    
    await fs.writeFile(path.join(projectPath, 'docs/README.md'), docsReadme);
    
    const vision = `# Task Management App Vision

## Executive Summary
Create a modern, collaborative task management application that enables teams to work together efficiently with real-time updates and intuitive interfaces.

## Problem Statement
- Teams struggle with scattered task management
- Lack of real-time collaboration features
- No unified view of project progress

## Solution Overview
A comprehensive task management platform with real-time collaboration, drag-and-drop interface, and robust backend architecture.

## Target Users
- Development teams
- Project managers
- Remote workers

## Success Metrics
1. User adoption rate > 80%
2. Task completion time reduced by 40%
3. User satisfaction score > 4.5/5`;
    
    await fs.writeFile(path.join(projectPath, 'docs/vision.md'), vision);
    
    // Generate slides package.json
    const slidesPackageJson = {
      name: 'task-management-app-slides',
      version: '1.0.0',
      description: 'Presentation slides for Task Management App',
      main: 'index.html',
      scripts: {
        start: 'http-server -p 8080 -o',
        build: 'echo "Slides are ready for presentation"',
        serve: 'http-server -p 8080'
      },
      dependencies: {
        'reveal.js': '^5.0.4'
      },
      devDependencies: {
        'http-server': '^14.1.1'
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'slides/package.json'),
      JSON.stringify(slidesPackageJson, null, 2)
    );
    
    // Generate RevealJS slides
    const mainSlides = `<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>Task Management App - Project Overview</title>
    
    <link rel="stylesheet" href="node_modules/reveal.js/dist/reset.css">
    <link rel="stylesheet" href="node_modules/reveal.js/dist/reveal.css">
    <link rel="stylesheet" href="node_modules/reveal.js/dist/theme/white.css">
    <link rel="stylesheet" href="css/custom.css">
    
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- Title Slide -->
            <section>
                <h1>Task Management App</h1>
                <h3>Modern collaborative task management</h3>
                <p>${new Date().toLocaleDateString()}</p>
            </section>
            
            <!-- Architecture Diagram -->
            <section>
                <h2>System Architecture</h2>
                <div class="mermaid">
                    %%{init: {'theme': 'base', 'themeVariables': { 'primaryColor': '#f5f5f5', 'primaryTextColor': '#333' }}}%%
                    graph TB
                        subgraph "Frontend Layer"
                            FE[React App]
                            WS[WebSocket Client]
                        end
                        
                        subgraph "Backend Layer"
                            API[Node.js API]
                            AUTH[Authentication]
                            SOCKET[WebSocket Server]
                        end
                        
                        FE --> API
                        WS --> SOCKET
                </div>
            </section>
            
            <!-- Implementation Approach -->
            <section>
                <h2>Implementation Approach</h2>
                <h3>Backwards Build Methodology</h3>
                <ol>
                    <li><strong>Documentation First</strong> - Complete specs and architecture</li>
                    <li><strong>Presentation Layer</strong> - Stakeholder communication</li>
                    <li><strong>Test-Driven Development</strong> - Comprehensive test coverage</li>
                    <li><strong>Implementation</strong> - Code that passes all tests</li>
                </ol>
            </section>
        </div>
    </div>
    
    <script src="node_modules/reveal.js/dist/reveal.js"></script>
    <script>
        Reveal.initialize({
            hash: true,
            plugins: []
        });
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>`;
    
    await fs.writeFile(path.join(projectPath, 'slides/index.html'), mainSlides);
    
    // Generate custom CSS
    const customCSS = `.reveal {
    font-family: Arial, sans-serif;
    background: #1a1a1a;
    color: #ffffff;
}

.reveal h1, .reveal h2, .reveal h3 {
    color: #ffffff;
    text-transform: none;
    font-weight: 600;
}

.reveal .slides section .mermaid {
    margin: 2em 0;
    background: #ffffff;
    border-radius: 8px;
    padding: 1em;
}`;
    
    await fs.writeFile(path.join(projectPath, 'slides/css/custom.css'), customCSS);
    
    // Generate pytest conftest
    const conftest = `import pytest
from typing import Generator, AsyncGenerator
from httpx import AsyncClient
from fastapi.testclient import TestClient

@pytest.fixture
def client() -> Generator:
    # Mock test client
    yield None

@pytest.fixture
async def async_client() -> AsyncGenerator:
    # Mock async client
    yield None`;
    
    await fs.writeFile(path.join(projectPath, 'tests/backend/conftest.py'), conftest);
    
    // Generate pytest unit test
    const unitTest = `from typing import Dict, Any
import pytest
from pydantic import BaseModel

class UserCreate(BaseModel):
    email: str
    password: str
    full_name: str | None = None

def test_user_creation():
    user_data = UserCreate(
        email="<EMAIL>",
        password="password123",
        full_name="Test User"
    )
    assert user_data.email == "<EMAIL>"
    assert user_data.password == "password123"
    assert user_data.full_name == "Test User"`;
    
    await fs.writeFile(path.join(projectPath, 'tests/backend/unit/test_example.py'), unitTest);
    
    // Generate Playwright config
    const playwrightConfig = `import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});`;
    
    await fs.writeFile(path.join(projectPath, 'tests/frontend/playwright.config.ts'), playwrightConfig);
    
    // Generate Playwright test
    const playwrightTest = `import { test, expect } from '@playwright/test';

test.describe('Authentication', () => {
  test('should login successfully', async ({ page }) => {
    await page.goto('/login');
    
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('h1')).toContainText('Dashboard');
  });
});`;
    
    await fs.writeFile(path.join(projectPath, 'tests/frontend/e2e/login.spec.ts'), playwrightTest);
    
    // Generate Node.js ready package.json
    const rootPackageJson = {
      name: 'task-management-app',
      version: '1.0.0',
      description: 'Modern collaborative task management application',
      main: 'index.js',
      scripts: {
        start: 'concurrently "npm run start:backend" "npm run start:frontend"',
        'start:backend': 'cd backend && npm start',
        'start:frontend': 'cd frontend && npm start',
        dev: 'npm start',
        build: 'npm run build:backend && npm run build:frontend',
        'build:backend': 'cd backend && npm run build',
        'build:frontend': 'cd frontend && npm run build',
        test: 'npm run test:backend && npm run test:frontend',
        'test:backend': 'cd backend && npm test',
        'test:frontend': 'cd frontend && npm test',
        'install:all': 'npm install && cd backend && npm install && cd ../frontend && npm install',
        'slides:start': 'cd slides && npm start',
        'slides:install': 'cd slides && npm install',
        'docker:up': 'docker-compose up -d',
        'docker:down': 'docker-compose down'
      },
      workspaces: ['backend', 'frontend', 'slides'],
      devDependencies: {
        'concurrently': '^8.2.2'
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'package.json'),
      JSON.stringify(rootPackageJson, null, 2)
    );
    
    // Generate backend and frontend basic structure
    const backendPackageJson = {
      name: 'task-management-backend',
      version: '1.0.0',
      description: 'Task Management App backend',
      main: 'src/server.js',
      scripts: {
        start: 'node src/server.js',
        dev: 'nodemon src/server.js',
        test: 'jest'
      },
      dependencies: {
        express: '^4.18.2',
        cors: '^2.8.5'
      },
      devDependencies: {
        nodemon: '^3.0.1',
        jest: '^29.7.0'
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'backend/package.json'),
      JSON.stringify(backendPackageJson, null, 2)
    );
    
    const frontendPackageJson = {
      name: 'task-management-frontend',
      version: '1.0.0',
      description: 'Task Management App frontend',
      main: 'src/index.js',
      scripts: {
        start: 'react-scripts start',
        build: 'react-scripts build',
        test: 'react-scripts test'
      },
      dependencies: {
        react: '^18.2.0',
        'react-dom': '^18.2.0',
        'react-scripts': '^5.0.1'
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'frontend/package.json'),
      JSON.stringify(frontendPackageJson, null, 2)
    );
    
    // Generate README following backwards-build methodology
    const readme = `# Task Management App

A modern, collaborative task management application built with backwards-build methodology.

## 🎯 Backwards Build Methodology

This project follows the backwards-build workflow:

1. **📚 Documentation First** - Complete specifications and architecture
2. **📊 Presentation Layer** - Stakeholder communication with RevealJS slides
3. **🧪 Test-Driven Development** - Comprehensive test coverage with pytest and Playwright
4. **💻 Implementation** - Code that passes all tests

## 🚀 Quick Start

\`\`\`bash
# Install all dependencies
npm run install:all

# Start the application (Node.js first)
npm start

# Start slides presentation
npm run slides:start
\`\`\`

## 📁 Project Structure

\`\`\`
├── docs/                    # Documentation (Phase 1)
│   ├── architecture/        # System architecture
│   ├── state/              # Current state
│   └── roadmap/            # Development roadmap
├── slides/                  # RevealJS presentations (Phase 2)
│   ├── css/                # Custom slide styling
│   └── assets/             # Slide assets
├── tests/                   # Test specifications (Phase 3)
│   ├── backend/            # Backend tests (pytest)
│   │   ├── unit/           # Unit tests
│   │   ├── integration/    # Integration tests
│   │   └── e2e/            # End-to-end tests
│   └── frontend/           # Frontend tests (Playwright)
│       └── e2e/            # End-to-end tests
├── backend/                # Backend implementation (Phase 4)
└── frontend/               # Frontend implementation (Phase 4)
\`\`\`

## 🔧 Development

### Available Scripts

- \`npm start\` - Start both frontend and backend
- \`npm run dev\` - Same as start (development mode)
- \`npm run build\` - Build for production
- \`npm test\` - Run all tests
- \`npm run slides:start\` - Start slides presentation
- \`npm run install:all\` - Install all dependencies

### Testing

- **Backend**: pytest for Python-style testing
- **Frontend**: Playwright for E2E testing
- **Coverage**: Comprehensive test coverage required

### Presentation

- **Slides**: RevealJS with custom dark theme
- **Architecture**: Mermaid diagrams for system visualization
- **Documentation**: Markdown with proper linking

---

*Generated with KAPI's 5-Minute Project Creation System following backwards-build methodology* 🚀✨`;
    
    await fs.writeFile(path.join(projectPath, 'README.md'), readme);
    
    // Verification
    console.log('✅ Backwards-build template compliance test completed!');
    console.log('');
    console.log('📂 Generated Backwards-Build Compliant Project:');
    console.log('├── 📚 Documentation structure (docs/)');
    console.log('│   ├── README.md, vision.md');
    console.log('│   ├── architecture/, state/, roadmap/');
    console.log('├── 📊 RevealJS slides (slides/)');
    console.log('│   ├── package.json with proper npm scripts');
    console.log('│   ├── index.html with Mermaid diagrams');
    console.log('│   └── css/custom.css with dark theme');
    console.log('├── 🧪 Test structure (tests/)');
    console.log('│   ├── Backend: pytest with conftest.py');
    console.log('│   └── Frontend: Playwright with proper config');
    console.log('├── 💻 Node.js ready implementation');
    console.log('│   ├── Root package.json with workspaces');
    console.log('│   ├── Backend and frontend packages');
    console.log('│   └── Concurrently for multi-service start');
    console.log('└── 📝 README following backwards-build');
    console.log('');
    console.log('🎯 Backwards-Build Workflow Verified:');
    console.log('1. ✅ Documentation First - Complete docs/ structure');
    console.log('2. ✅ Presentation Layer - RevealJS slides with npm');
    console.log('3. ✅ Test-Driven Development - pytest + Playwright');
    console.log('4. ✅ Node.js Implementation - npm start ready');
    console.log('');
    console.log('🚀 Project Ready for Backwards-Build Development:');
    console.log('1. cd ' + projectPath);
    console.log('2. npm run install:all');
    console.log('3. npm start (main app)');
    console.log('4. npm run slides:start (presentations)');
    console.log('');
    console.log('🎉 Your backwards-build compliant project is ready!');
    
    // Verify file structure
    const files = [
      'docs/README.md',
      'docs/vision.md',
      'slides/package.json',
      'slides/index.html',
      'slides/css/custom.css',
      'tests/backend/conftest.py',
      'tests/backend/unit/test_example.py',
      'tests/frontend/playwright.config.ts',
      'tests/frontend/e2e/login.spec.ts',
      'package.json',
      'backend/package.json',
      'frontend/package.json',
      'README.md'
    ];
    
    console.log('');
    console.log('📋 File Structure Verification:');
    for (const file of files) {
      try {
        await fs.access(path.join(projectPath, file));
        console.log(`✅ ${file}`);
      } catch (error) {
        console.log(`❌ ${file} - Missing!`);
      }
    }
    
  } catch (error) {
    console.error('❌ Error testing backwards-build compliance:', error);
    process.exit(1);
  }
}

// Run the test
testBackwardsBuildCompliance();