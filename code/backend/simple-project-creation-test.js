/**
 * Simple Project Creation Test
 * Tests the project creation trigger logic without complex TypeScript types
 */

const { ProjectOnboardingTaskStrategy } = require('./dist/services/conversation/strategies/project-onboarding-task-strategy.js');

async function testProjectCreation() {
  console.log('🧪 [TEST] Starting simple project creation test...');
  
  try {
    // Create strategy instance
    const strategy = new ProjectOnboardingTaskStrategy();
    console.log('✅ [TEST] Strategy created successfully');
    
    // Mock conversation messages that should trigger project creation
    const mockMessages = [
      {
        id: 1,
        conversation_id: 999,
        role: 'assistant',
        content: 'What kind of AI/ML project would you like to build?',
        model: 'claude-3.5-haiku',
        prompt_tokens: 50,
        completion_tokens: 20,
        cost: 0.001,
        duration_ms: 1000,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      },
      {
        id: 2,
        conversation_id: 999,
        role: 'user',
        content: 'I want to build a productivity app with React and Node.js that has AI-powered task insights',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      },
      {
        id: 3,
        conversation_id: 999,
        role: 'user',
        content: 'Yes, let\'s build this project! I\'m ready to create it.',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      }
    ];

    // Create mock context
    const mockContext = {
      conversationId: 999,
      messages: mockMessages.slice(0, -1), // All but the last message
      options: {
        strategy: 'project_onboarding'
      }
    };

    // Create mock response (the final user message that should trigger creation)
    const mockResponse = {
      message: mockMessages[mockMessages.length - 1].content,
      conversationId: 999,
      messageId: mockMessages[mockMessages.length - 1].id.toString()
    };

    console.log('🧪 [TEST] Simulating completed interview with trigger phrase: "let\'s build this project"');

    // Process the response that should trigger project creation
    const result = strategy.processResponse(mockResponse, mockContext);
    console.log('✅ [TEST] Response processed, result:', result ? 'Success' : 'Failed');

    console.log('🧪 [TEST] Waiting for async project creation to complete...');
    
    // Wait for the async project creation to complete
    await new Promise(resolve => setTimeout(resolve, 5000));

    console.log('✅ [TEST] Test completed successfully!');
    console.log('🎉 [TEST] If you see project creation logs above, the trigger is working!');
    
  } catch (error) {
    console.error('❌ [TEST] Test failed:', error);
    console.error('❌ [TEST] Stack trace:', error.stack);
  }
}

// Run the test
testProjectCreation().then(() => {
  console.log('🏁 [TEST] Test execution finished');
  process.exit(0);
}).catch(error => {
  console.error('💥 [TEST] Unhandled error:', error);
  process.exit(1);
});