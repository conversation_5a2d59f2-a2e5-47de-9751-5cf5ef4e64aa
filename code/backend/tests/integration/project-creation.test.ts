import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import * as fs from 'fs/promises';
import * as path from 'path';
import { ProjectCreationService } from '../../src/services/project-creation.service';
import { TextInterviewService } from '../../src/services/text-interview.service';
import { AgentOrchestrator } from '../../src/services/agent-orchestrator.service';

describe('5-Minute Project Creation Integration Test', () => {
  let projectCreationService: ProjectCreationService;
  let testProjectPath: string;
  
  beforeEach(async () => {
    // Initialize services with mocked dependencies
    const mockInterviewService = new TextInterviewService();
    const mockAgentOrchestrator = new AgentOrchestrator();
    
    // Mock template service
    const mockTemplateService = {
      createTemplate: jest.fn(),
      getTemplateById: jest.fn(),
      getAllTemplates: jest.fn(),
      updateTemplate: jest.fn(),
      deleteTemplate: jest.fn(),
      addTemplateFile: jest.fn(),
      getTemplateFiles: jest.fn(),
      updateTemplateFile: jest.fn(),
      deleteTemplateFile: jest.fn(),
      addTemplateVariable: jest.fn(),
      getTemplateVariables: jest.fn(),
      updateTemplateVariable: jest.fn(),
      deleteTemplateVariable: jest.fn(),
      createTemplateCollection: jest.fn(),
      getTemplateCollectionById: jest.fn(),
      getAllTemplateCollections: jest.fn(),
      updateTemplateCollection: jest.fn(),
      deleteTemplateCollection: jest.fn(),
      addTemplateToCollection: jest.fn(),
      removeTemplateFromCollection: jest.fn(),
      createProjectTemplate: jest.fn(),
      getProjectTemplateById: jest.fn(),
      getAllProjectTemplates: jest.fn(),
      updateProjectTemplate: jest.fn(),
      deleteProjectTemplate: jest.fn(),
      recordTemplateUsage: jest.fn()
    };
    
    // Mock conversation service
    const mockConversationService = {
      createConversation: jest.fn(),
      getConversationById: jest.fn(),
      getAllConversations: jest.fn(),
      updateConversation: jest.fn(),
      deleteConversation: jest.fn(),
      addMessage: jest.fn(),
      processMessage: jest.fn(),
      streamMessage: jest.fn(),
      getMessages: jest.fn(),
      closeConversation: jest.fn()
    };
    
    projectCreationService = new ProjectCreationService(
      mockInterviewService,
      mockAgentOrchestrator,
      mockTemplateService as any,
      mockConversationService as any
    );
    
    // Set up test project path
    testProjectPath = '/Users/<USER>/Code/KAPI/demo-task-manager';
    
    // Clean up any existing test project
    try {
      await fs.rm(testProjectPath, { recursive: true, force: true });
    } catch (error) {
      // Ignore if directory doesn't exist
    }
  });

  afterEach(async () => {
    // Clean up test project after each test
    // TEMPORARILY DISABLED - Comment out to preserve generated project
    // try {
    //   await fs.rm(testProjectPath, { recursive: true, force: true });
    // } catch (error) {
    //   // Ignore cleanup errors
    // }
  });

  it('should create a complete project from interview responses', async () => {
    // Mock interview Q&A sequence
    const mockInterviewData = {
      userId: 'test-user-123',
      projectPath: testProjectPath,
      responses: [
        {
          question: "What's your main goal with this project?",
          answer: "🆕 Building a new app",
          context: { objective: 'build-new' }
        },
        {
          question: "What kind of project are you working on?",
          answer: "🌐 Web Application - A task management app for my team",
          context: { type: 'web-app', description: 'task management app for team' }
        },
        {
          question: "What's the core feature that users will love?",
          answer: "Real-time collaboration with drag-and-drop task boards, team notifications, and mobile-responsive design",
          context: { 
            features: ['real-time collaboration', 'drag-and-drop', 'notifications', 'mobile-responsive'],
            techStack: 'modern web stack'
          }
        },
        {
          question: "What's your preferred tech stack?",
          answer: "React with TypeScript for frontend, Node.js with Express for backend, PostgreSQL for database, and WebSocket for real-time features",
          context: {
            frontend: 'React + TypeScript',
            backend: 'Node.js + Express',
            database: 'PostgreSQL',
            realtime: 'WebSocket'
          }
        },
        {
          question: "Any specific timeline or deployment requirements?",
          answer: "Need to demo this to stakeholders in 2 weeks, so it should be production-ready with proper documentation",
          context: {
            timeline: '2 weeks',
            requirements: ['production-ready', 'documentation', 'demo-ready']
          }
        }
      ]
    };

    // Expected project structure
    const expectedStructure = {
      documentation: [
        'README.md',
        'API.md',
        'ARCHITECTURE.md',
        'ROADMAP.md',
        'docs/architecture/system-overview.md',
        'docs/architecture/tech-stack.md',
        'docs/state/current-state.md',
        'docs/roadmap/milestones.md'
      ],
      slides: [
        'slides/index.html',
        'slides/executive-overview.html',
        'slides/technical-deep-dive.html',
        'slides/demo-script.html',
        'slides/css/custom.css'
      ],
      tests: [
        'tests/backend/unit/test-auth.js',
        'tests/backend/integration/test-api.js',
        'tests/backend/e2e/test-workflows.js',
        'tests/frontend/components/test-taskboard.js',
        'tests/frontend/e2e/test-collaboration.js',
        'tests/frontend/playwright.config.ts'
      ],
      code: {
        backend: [
          'backend/src/app.js',
          'backend/src/controllers/auth.controller.js',
          'backend/src/controllers/task.controller.js',
          'backend/src/controllers/team.controller.js',
          'backend/src/models/User.js',
          'backend/src/models/Task.js',
          'backend/src/models/Team.js',
          'backend/src/middleware/auth.middleware.js',
          'backend/src/services/websocket.service.js',
          'backend/src/routes/auth.routes.js',
          'backend/src/routes/task.routes.js',
          'backend/src/routes/team.routes.js',
          'backend/package.json',
          'backend/.env.example'
        ],
        frontend: [
          'frontend/src/App.tsx',
          'frontend/src/components/TaskBoard.tsx',
          'frontend/src/components/TaskCard.tsx',
          'frontend/src/components/TeamCollaboration.tsx',
          'frontend/src/components/AuthForm.tsx',
          'frontend/src/hooks/useWebSocket.ts',
          'frontend/src/hooks/useAuth.ts',
          'frontend/src/services/api.ts',
          'frontend/src/types/Task.ts',
          'frontend/src/types/User.ts',
          'frontend/src/types/Team.ts',
          'frontend/package.json',
          'frontend/tailwind.config.js',
          'frontend/tsconfig.json'
        ],
        database: [
          'database/migrations/001_create_users.sql',
          'database/migrations/002_create_teams.sql',
          'database/migrations/003_create_tasks.sql',
          'database/seeds/dev-data.sql',
          'database/schema.sql'
        ],
        deployment: [
          'docker-compose.yml',
          'Dockerfile.backend',
          'Dockerfile.frontend',
          'k8s/deployment.yaml',
          'k8s/service.yaml',
          '.github/workflows/ci-cd.yml'
        ]
      },
      config: [
        'package.json',
        '.gitignore',
        '.env.example',
        'docker-compose.yml'
      ]
    };

    // Execute the project creation
    console.log('🚀 Starting 5-minute project creation test...');
    const startTime = Date.now();
    
    let result;
    try {
      result = await projectCreationService.createProject(
        mockInterviewData.userId,
        mockInterviewData.responses
      );
    } catch (error) {
      console.error('❌ Error during project creation:', error);
      throw error;
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`✅ Project creation completed in ${duration}ms`);
    console.log(`📊 Project path: ${result.project.path}`);
    console.log(`📊 Project name: ${result.project.name}`);
    
    // Verify the result structure
    expect(result).toHaveProperty('project');
    expect(result).toHaveProperty('qualityScore');
    expect(result).toHaveProperty('timeToComplete');
    expect(result).toHaveProperty('readyForDeployment');
    expect(result.readyForDeployment).toBe(true);
    expect(result.qualityScore).toBeGreaterThan(90);
    expect(duration).toBeLessThan(300000); // Should complete in under 5 minutes
    
    // Verify project directory was created
    const projectExists = await fs.access(testProjectPath).then(() => true).catch(() => false);
    expect(projectExists).toBe(true);
    
    // Verify documentation files
    console.log('📚 Verifying documentation files...');
    for (const docFile of expectedStructure.documentation) {
      const filePath = path.join(testProjectPath, docFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
      
      // Verify content is not empty
      const content = await fs.readFile(filePath, 'utf-8');
      expect(content.length).toBeGreaterThan(50);
      
      // Only check for project name in README and some key files
      if (docFile === 'README.md' || docFile === 'ARCHITECTURE.md' || docFile === 'ROADMAP.md') {
        expect(content).toContain('Task Management'); // Should contain project name
      }
    }
    
    // Verify slides
    console.log('🎨 Verifying presentation files...');
    for (const slideFile of expectedStructure.slides) {
      const filePath = path.join(testProjectPath, slideFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
      
      if (slideFile.endsWith('.html')) {
        const content = await fs.readFile(filePath, 'utf-8');
        expect(content).toContain('reveal.js'); // Should be RevealJS slides
        expect(content).toContain('Task Management'); // Should contain project name
      }
    }
    
    // Verify test files
    console.log('🧪 Verifying test files...');
    for (const testFile of expectedStructure.tests) {
      const filePath = path.join(testProjectPath, testFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
      
      const content = await fs.readFile(filePath, 'utf-8');
      expect(content.length).toBeGreaterThan(50);
      expect(content).toMatch(/(test|describe|it|expect)/); // Should contain test syntax
    }
    
    // Verify backend code
    console.log('💻 Verifying backend code...');
    for (const backendFile of expectedStructure.code.backend) {
      const filePath = path.join(testProjectPath, backendFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
      
      if (backendFile.endsWith('.js')) {
        const content = await fs.readFile(filePath, 'utf-8');
        expect(content).toMatch(/(require|import|module\.exports|export)/); // Should contain JS/Node syntax
      }
    }
    
    // Verify frontend code
    console.log('🎨 Verifying frontend code...');
    for (const frontendFile of expectedStructure.code.frontend) {
      const filePath = path.join(testProjectPath, frontendFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
      
      if (frontendFile.endsWith('.tsx')) {
        const content = await fs.readFile(filePath, 'utf-8');
        expect(content).toMatch(/(import React|interface|type)/); // Should contain React/TS syntax
      }
    }
    
    // Verify database files
    console.log('🗄️ Verifying database files...');
    for (const dbFile of expectedStructure.code.database) {
      const filePath = path.join(testProjectPath, dbFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
      
      if (dbFile.endsWith('.sql')) {
        const content = await fs.readFile(filePath, 'utf-8');
        expect(content).toMatch(/(CREATE TABLE|INSERT INTO|SELECT)/); // Should contain SQL syntax
      }
    }
    
    // Verify deployment files
    console.log('🚀 Verifying deployment files...');
    for (const deployFile of expectedStructure.code.deployment) {
      const filePath = path.join(testProjectPath, deployFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
    }
    
    // Verify package.json files are valid JSON
    console.log('📦 Verifying package.json files...');
    const packageJsonFiles = [
      'package.json',
      'backend/package.json',
      'frontend/package.json'
    ];
    
    for (const packageFile of packageJsonFiles) {
      const filePath = path.join(testProjectPath, packageFile);
      const content = await fs.readFile(filePath, 'utf-8');
      const packageData = JSON.parse(content);
      
      expect(packageData).toHaveProperty('name');
      expect(packageData).toHaveProperty('version');
      expect(packageData).toHaveProperty('scripts');
      expect(packageData).toHaveProperty('dependencies');
    }
    
    // Verify Docker files
    console.log('🐳 Verifying Docker configuration...');
    const dockerComposePath = path.join(testProjectPath, 'docker-compose.yml');
    const dockerComposeContent = await fs.readFile(dockerComposePath, 'utf-8');
    expect(dockerComposeContent).toContain('version:');
    expect(dockerComposeContent).toContain('services:');
    expect(dockerComposeContent).toContain('postgres'); // Should have database service
    
    // Verify .gitignore
    console.log('🔒 Verifying .gitignore...');
    const gitignorePath = path.join(testProjectPath, '.gitignore');
    const gitignoreContent = await fs.readFile(gitignorePath, 'utf-8');
    expect(gitignoreContent).toContain('node_modules');
    expect(gitignoreContent).toContain('.env');
    expect(gitignoreContent).toContain('dist');
    
    // Verify README has proper content
    console.log('📖 Verifying README content...');
    const readmePath = path.join(testProjectPath, 'README.md');
    const readmeContent = await fs.readFile(readmePath, 'utf-8');
    expect(readmeContent).toContain('# Task Management App');
    expect(readmeContent).toContain('## Quick Start');
    expect(readmeContent).toContain('## Features');
    expect(readmeContent).toContain('docker-compose up'); // Should have setup instructions
    
    // Verify API documentation
    console.log('📋 Verifying API documentation...');
    const apiDocPath = path.join(testProjectPath, 'API.md');
    const apiDocContent = await fs.readFile(apiDocPath, 'utf-8');
    expect(apiDocContent).toContain('# API Documentation');
    expect(apiDocContent).toContain('## Authentication');
    expect(apiDocContent).toContain('## Endpoints');
    expect(apiDocContent).toMatch(/POST|GET|PUT|DELETE/); // Should have HTTP methods
    
    // Log final results
    console.log('🎉 Project creation test completed successfully!');
    console.log(`📊 Quality Score: ${result.qualityScore}/100`);
    console.log(`⏱️ Time to Complete: ${result.timeToComplete}`);
    console.log(`📂 Project Created: ${testProjectPath}`);
    console.log(`🔍 Files Generated: ${await countGeneratedFiles(testProjectPath)}`);
    
    // Additional quality checks
    expect(result.qualityScore).toBeGreaterThan(90);
    expect(await countGeneratedFiles(testProjectPath)).toBeGreaterThan(50);
    
  }, 300000); // 5 minute timeout

  it('should handle different project types correctly', async () => {
    const projectTypes = [
      {
        type: 'api-backend',
        description: 'REST API service',
        expectedFiles: ['src/controllers', 'src/models', 'src/routes']
      },
      {
        type: 'mobile-app',
        description: 'React Native app',
        expectedFiles: ['src/components', 'src/screens', 'src/navigation']
      },
      {
        type: 'ai-ml',
        description: 'Machine learning project',
        expectedFiles: ['src/models', 'src/data', 'requirements.txt']
      }
    ];
    
    for (const projectType of projectTypes) {
      const mockResponses = [
        {
          question: "What's your main goal?",
          answer: "🆕 Building a new app",
          context: { objective: 'build-new' }
        },
        {
          question: "What kind of project?",
          answer: `${projectType.type} - ${projectType.description}`,
          context: { type: projectType.type, description: projectType.description }
        }
      ];
      
      const result = await projectCreationService.createProject(
        'test-user',
        mockResponses
      );
      
      expect(result.project.type).toBe(projectType.type);
      expect(result.qualityScore).toBeGreaterThan(85);
      
      // Verify type-specific files exist
      for (const expectedFile of projectType.expectedFiles) {
        const filePath = path.join(testProjectPath, expectedFile);
        const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
        expect(fileExists).toBe(true);
      }
    }
  });

  it('should generate appropriate tests for all components', async () => {
    const mockResponses = [
      {
        question: "What's your main goal?",
        answer: "🆕 Building a new app",
        context: { objective: 'build-new' }
      },
      {
        question: "What kind of project?",
        answer: "🌐 Web Application - E-commerce platform",
        context: { type: 'web-app', description: 'e-commerce platform' }
      }
    ];
    
    const result = await projectCreationService.createProject(
      'test-user',
      mockResponses
    );
    
    // Verify comprehensive test coverage
    const testDirs = [
      'tests/backend/unit',
      'tests/backend/integration',
      'tests/backend/e2e',
      'tests/frontend/components',
      'tests/frontend/e2e'
    ];
    
    for (const testDir of testDirs) {
      const dirPath = path.join(testProjectPath, testDir);
      const dirExists = await fs.access(dirPath).then(() => true).catch(() => false);
      expect(dirExists).toBe(true);
      
      const files = await fs.readdir(dirPath);
      expect(files.length).toBeGreaterThan(0);
    }
    
    // Verify test configuration files
    const testConfigFiles = [
      'tests/backend/jest.config.js',
      'tests/frontend/playwright.config.ts',
      'tests/frontend/jest.config.js'
    ];
    
    for (const configFile of testConfigFiles) {
      const filePath = path.join(testProjectPath, configFile);
      const fileExists = await fs.access(filePath).then(() => true).catch(() => false);
      expect(fileExists).toBe(true);
    }
  });

  // Helper function to count generated files
  async function countGeneratedFiles(dirPath: string): Promise<number> {
    let count = 0;
    
    async function countRecursive(currentPath: string) {
      const items = await fs.readdir(currentPath, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(currentPath, item.name);
        
        if (item.isDirectory() && !item.name.startsWith('.') && item.name !== 'node_modules') {
          await countRecursive(fullPath);
        } else if (item.isFile()) {
          count++;
        }
      }
    }
    
    try {
      await countRecursive(dirPath);
    } catch (error) {
      // Directory might not exist
    }
    
    return count;
  }
});