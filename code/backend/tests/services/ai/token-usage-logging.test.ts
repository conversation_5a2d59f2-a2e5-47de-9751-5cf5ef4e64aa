/**
 * Tests for token usage logging across different model families
 *
 * This test file verifies that token usage is properly logged for each model family:
 * - Nova
 * - Claude
 * - Azure
 * - Gemini
 */
import { prisma } from '../../../src/db/client';
import { Provider } from '../../../src/generated/prisma';
import aiService from '../../../src/services/ai/index';
import modelUsageService from '../../../src/services/model-usage.service';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Test user ID - make sure this exists in your database
const TEST_USER_ID = 1;

describe('Token Usage Logging Tests', () => {
  // Set longer timeout for real API calls
  jest.setTimeout(180000); // 3 minute timeout

  let modelUsageIds: number[] = [];

  beforeAll(async () => {
    // Delete any existing model usage records for this test
    await prisma.model_usage.deleteMany({
      where: {
        userId: TEST_USER_ID,
        timestamp: {
          gte: new Date(Date.now() - 60 * 1000) // Last minute
        }
      }
    });
  });

  afterAll(async () => {
    // Clean up - delete the test model usage records
    if (modelUsageIds.length > 0) {
      await prisma.model_usage.deleteMany({
        where: {
          id: { in: modelUsageIds }
        }
      });
      console.log(`Deleted ${modelUsageIds.length} test model usage records`);
    }

    // Disconnect Prisma client
    await prisma.$disconnect();
  });

  /**
   * Helper function to test token usage logging for a specific model
   */
  async function testModelTokenUsage(provider: string, model: string) {
    try {
      console.log(`\nTesting token usage for ${provider} model: ${model}...`);

      // Short prompt to minimize costs
      const prompt = "Hello, this is a short test message. Please respond briefly.";

      // Get the start time for duration calculation
      const startTime = Date.now();

      // Make a direct API call with streaming
      const stream = await aiService.streamText({
        prompt,
        provider: provider as any, // Cast to any to bypass type checking
        model,
        maxTokens: 100,
        temperature: 0.7,
        systemPrompt: "You are a helpful assistant. Keep responses very brief."
      });

      // Process the stream and collect the full response
      let fullResponse = '';
      let tokenUsage: any = null;

      for await (const chunk of stream) {
        // Extract content from chunks
        if (chunk.choices && chunk.choices[0]?.delta?.content) {
          fullResponse += chunk.choices[0].delta.content;
        }

        // Extract token usage from the final chunk
        if (chunk.usage) {
          tokenUsage = chunk.usage;
        }
      }

      // Calculate duration
      const duration_ms = Date.now() - startTime;

      console.log(`Response from ${provider} ${model}: ${fullResponse.substring(0, 50)}...`);

      if (tokenUsage) {
        console.log('Token usage information from API:');
        console.log('- Prompt tokens:', tokenUsage.prompt_tokens);
        console.log('- Completion tokens:', tokenUsage.completion_tokens);
        console.log('- Total tokens:', tokenUsage.total_tokens);
        console.log('- Cost:', tokenUsage.cost);
        console.log('- Duration:', tokenUsage.duration_ms || duration_ms, 'ms');
      } else {
        console.log('No token usage information received from API');

        // Estimate token usage
        const estimatedPromptTokens = Math.ceil(prompt.length / 4);
        const estimatedCompletionTokens = Math.ceil(fullResponse.length / 4);
        const estimatedTotalTokens = estimatedPromptTokens + estimatedCompletionTokens;

        console.log('Estimated token usage:');
        console.log('- Prompt tokens:', estimatedPromptTokens);
        console.log('- Completion tokens:', estimatedCompletionTokens);
        console.log('- Total tokens:', estimatedTotalTokens);
        console.log('- Duration:', duration_ms, 'ms');

        tokenUsage = {
          prompt_tokens: estimatedPromptTokens,
          completion_tokens: estimatedCompletionTokens,
          total_tokens: estimatedTotalTokens,
          duration_ms: duration_ms
        };
      }

      // Log model usage to the database
      const modelUsage = await modelUsageService.logLlmUsage({
        userId: TEST_USER_ID,
        modelRequested: model,
        modelUsed: model,
        taskType: 'test',
        promptTokens: tokenUsage.prompt_tokens,
        completionTokens: tokenUsage.completion_tokens,
        totalTokens: tokenUsage.total_tokens,
        estimatedCost: tokenUsage.cost,
        processingTime: tokenUsage.duration_ms || duration_ms,
        status: 'success',
        provider: mapProviderToEnum(provider)
      });

      // Save the ID for cleanup
      modelUsageIds.push(modelUsage.id);

      // Check if usage was logged to the database
      const usageRecords = await prisma.model_usage.findMany({
        where: {
          id: modelUsage.id
        }
      });

      if (usageRecords.length === 0) {
        throw new Error('Model usage record not found in database');
      }

      const usage = usageRecords[0];
      console.log('Usage record from database:', {
        modelName: usage.modelName,
        provider: usage.provider,
        promptTokens: usage.promptTokens,
        completionTokens: usage.completionTokens,
        totalTokens: usage.totalTokens,
        estimatedCost: usage.estimatedCost
      });

      // Verify that token counts are present
      if (!usage.promptTokens || !usage.completionTokens || !usage.totalTokens) {
        console.warn(`Warning: Missing token counts for ${provider} ${model}`);
      }

      return {
        success: true,
        usage
      };
    } catch (error: any) {
      console.error(`Error testing ${provider} ${model}:`, error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Helper function to map provider string to Provider enum
   */
  function mapProviderToEnum(provider: string): Provider {
    switch (provider.toLowerCase()) {
      case 'nova':
        return Provider.bedrock;
      case 'claude':
        return Provider.bedrock;
      case 'azure':
        return Provider.azure;
      case 'gemini':
        return Provider.google;
      default:
        return Provider.other;
    }
  }

  it('should test Nova token usage logging', async () => {
    // Skip the test if AWS credentials are not available
    if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      console.log('Skipping Nova tests: AWS credentials not set');
      return;
    }

    const result = await testModelTokenUsage('nova', 'nova-micro');
    expect(result.success).toBe(true);
    if (result.success && result.usage) {
      expect(result.usage.promptTokens).toBeGreaterThan(0);
      expect(result.usage.completionTokens).toBeGreaterThan(0);
      expect(result.usage.totalTokens).toBeGreaterThan(0);
    }
  });

  it('should test Claude token usage logging', async () => {
    // Skip the test if AWS credentials are not available
    if (!process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      console.log('Skipping Claude tests: AWS credentials not set');
      return;
    }

    const result = await testModelTokenUsage('claude', '3.5-sonnet');
    expect(result.success).toBe(true);
    if (result.success && result.usage) {
      expect(result.usage.promptTokens).toBeGreaterThan(0);
      expect(result.usage.completionTokens).toBeGreaterThan(0);
      expect(result.usage.totalTokens).toBeGreaterThan(0);
    }
  });

  it('should test Azure token usage logging', async () => {
    // Skip the test if Azure credentials are not available
    if (!process.env.AZURE_API_KEY || !process.env.AZURE_ENDPOINT) {
      console.log('Skipping Azure tests: AZURE_API_KEY or AZURE_ENDPOINT not set');
      return;
    }

    const result = await testModelTokenUsage('azure', 'o3');
    expect(result.success).toBe(true);
    if (result.success && result.usage) {
      expect(result.usage.promptTokens).toBeGreaterThan(0);
      expect(result.usage.completionTokens).toBeGreaterThan(0);
      expect(result.usage.totalTokens).toBeGreaterThan(0);
    }
  });

  it('should test Gemini token usage logging', async () => {
    // Skip the test if Gemini credentials are not available
    if (!process.env.GEMINI_API_KEY) {
      console.log('Skipping Gemini tests: GEMINI_API_KEY not set');
      return;
    }

    const result = await testModelTokenUsage('gemini', 'gemini-2.0-flash');
    expect(result.success).toBe(true);
    if (result.success && result.usage) {
      expect(result.usage.promptTokens).toBeGreaterThan(0);
      expect(result.usage.completionTokens).toBeGreaterThan(0);
      expect(result.usage.totalTokens).toBeGreaterThan(0);
    }
  });
});
