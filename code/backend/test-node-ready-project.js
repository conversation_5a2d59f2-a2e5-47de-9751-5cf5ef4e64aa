const fs = require('fs').promises;
const path = require('path');

// Simple test to verify the updated ProjectCreation service generates Node.js ready projects
async function testNodeReadyProject() {
  console.log('🧪 Testing Node.js ready project generation...');
  
  // Mock the ProjectCreation service behavior
  const projectPath = '/Users/<USER>/Code/KAPI/demo-task-manager-node';
  
  try {
    // Clean up any existing test project
    await fs.rm(projectPath, { recursive: true, force: true });
    
    // Create project structure
    await fs.mkdir(projectPath, { recursive: true });
    await fs.mkdir(path.join(projectPath, 'backend/src'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'frontend/src'), { recursive: true });
    await fs.mkdir(path.join(projectPath, 'frontend/public'), { recursive: true });
    
    // Generate Node.js ready package.json
    const rootPackageJson = {
      name: 'task-management-app',
      version: '1.0.0',
      description: 'Modern collaborative task management application',
      main: 'index.js',
      scripts: {
        start: 'concurrently "npm run start:backend" "npm run start:frontend"',
        'start:backend': 'cd backend && npm start',
        'start:frontend': 'cd frontend && npm start',
        dev: 'npm start',
        build: 'npm run build:backend && npm run build:frontend',
        'build:backend': 'cd backend && npm run build',
        'build:frontend': 'cd frontend && npm run build',
        test: 'npm run test:backend && npm run test:frontend',
        'test:backend': 'cd backend && npm test',
        'test:frontend': 'cd frontend && npm test',
        'install:all': 'npm install && cd backend && npm install && cd ../frontend && npm install',
        'docker:up': 'docker-compose up -d',
        'docker:down': 'docker-compose down'
      },
      workspaces: ['backend', 'frontend'],
      devDependencies: {
        'concurrently': '^8.2.2'
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'package.json'),
      JSON.stringify(rootPackageJson, null, 2)
    );
    
    // Generate backend package.json
    const backendPackageJson = {
      name: 'task-management-backend',
      version: '1.0.0',
      description: 'Task Management App backend',
      main: 'src/server.js',
      scripts: {
        start: 'node src/server.js',
        dev: 'nodemon src/server.js',
        test: 'jest'
      },
      dependencies: {
        express: '^4.18.2',
        cors: '^2.8.5',
        helmet: '^7.0.0',
        dotenv: '^16.3.1',
        'socket.io': '^4.7.2'
      },
      devDependencies: {
        nodemon: '^3.0.1',
        jest: '^29.7.0',
        supertest: '^6.3.3'
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'backend/package.json'),
      JSON.stringify(backendPackageJson, null, 2)
    );
    
    // Generate frontend package.json
    const frontendPackageJson = {
      name: 'task-management-frontend',
      version: '1.0.0',
      description: 'Task Management App frontend',
      main: 'src/index.js',
      scripts: {
        start: 'react-scripts start',
        dev: 'react-scripts start',
        build: 'react-scripts build',
        test: 'react-scripts test'
      },
      dependencies: {
        react: '^18.2.0',
        'react-dom': '^18.2.0',
        'react-scripts': '^5.0.1'
      },
      browserslist: {
        production: ['>0.2%', 'not dead', 'not op_mini all'],
        development: ['last 1 chrome version', 'last 1 firefox version', 'last 1 safari version']
      }
    };
    
    await fs.writeFile(
      path.join(projectPath, 'frontend/package.json'),
      JSON.stringify(frontendPackageJson, null, 2)
    );
    
    // Generate backend server.js
    const serverJs = `require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Sample API endpoint
app.get('/api/tasks', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: 1, title: 'Setup Project', status: 'completed', priority: 'high' },
      { id: 2, title: 'Create API endpoints', status: 'in_progress', priority: 'medium' },
      { id: 3, title: 'Add authentication', status: 'pending', priority: 'high' }
    ]
  });
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(\`🚀 Server running on port \${PORT}\`);
  console.log(\`📱 Frontend should run on http://localhost:3000\`);
  console.log(\`🔗 API available at http://localhost:\${PORT}\`);
  console.log(\`📊 Health check: http://localhost:\${PORT}/health\`);
});`;
    
    await fs.writeFile(path.join(projectPath, 'backend/src/server.js'), serverJs);
    
    // Generate frontend files
    const indexHtml = `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Task Management App" />
    <title>Task Management App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>`;
    
    await fs.writeFile(path.join(projectPath, 'frontend/public/index.html'), indexHtml);
    
    const indexJs = `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);`;
    
    await fs.writeFile(path.join(projectPath, 'frontend/src/index.js'), indexJs);
    
    const appJs = `import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchTasks();
  }, []);

  const fetchTasks = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/tasks');
      if (response.ok) {
        const data = await response.json();
        setTasks(data.data || []);
      } else {
        setError('Failed to fetch tasks');
      }
    } catch (error) {
      console.error('Error fetching tasks:', error);
      setError('Network error - make sure backend is running on port 3001');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskClick = (task) => {
    const newStatus = task.status === 'pending' ? 'in_progress' : 
                     task.status === 'in_progress' ? 'completed' : 'pending';
    
    const updatedTask = { ...task, status: newStatus };
    setTasks(prevTasks => 
      prevTasks.map(t => t.id === task.id ? updatedTask : t)
    );
  };

  if (loading) {
    return (
      <div className="App">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading tasks...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="App">
        <div className="loading">
          <p style={{ color: 'red' }}>{error}</p>
          <button onClick={fetchTasks} style={{ marginTop: '1rem', padding: '0.5rem 1rem' }}>
            Retry
          </button>
        </div>
      </div>
    );
  }

  const columns = {
    pending: tasks.filter(task => task.status === 'pending'),
    in_progress: tasks.filter(task => task.status === 'in_progress'),
    completed: tasks.filter(task => task.status === 'completed')
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1 className="App-title">Task Management App</h1>
        <div>
          <span style={{ color: '#666' }}>Welcome! Click tasks to move them.</span>
        </div>
      </header>
      
      <main className="task-board">
        {Object.entries(columns).map(([columnId, columnTasks]) => (
          <div key={columnId} className="task-column">
            <h2 style={{ marginBottom: '1rem', textTransform: 'capitalize' }}>
              {columnId.replace('_', ' ')} ({columnTasks.length})
            </h2>
            {columnTasks.map((task) => (
              <div
                key={task.id}
                className={\`task-card priority-\${task.priority}\`}
                onClick={() => handleTaskClick(task)}
              >
                <h3 style={{ margin: '0 0 0.5rem 0', fontSize: '1rem' }}>
                  {task.title}
                </h3>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ 
                    background: task.priority === 'high' ? '#fee2e2' : task.priority === 'medium' ? '#fef3c7' : '#d1fae5',
                    color: task.priority === 'high' ? '#dc2626' : task.priority === 'medium' ? '#d97706' : '#059669',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '4px',
                    fontSize: '0.75rem',
                    fontWeight: 'bold'
                  }}>
                    {task.priority}
                  </span>
                  <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>
                    Click to move
                  </span>
                </div>
              </div>
            ))}
            {columnTasks.length === 0 && (
              <div style={{ textAlign: 'center', padding: '2rem', color: '#9ca3af' }}>
                No tasks here
              </div>
            )}
          </div>
        ))}
      </main>
    </div>
  );
}

export default App;`;
    
    await fs.writeFile(path.join(projectPath, 'frontend/src/App.js'), appJs);
    
    // Generate CSS files
    const indexCss = `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}`;
    
    await fs.writeFile(path.join(projectPath, 'frontend/src/index.css'), indexCss);
    
    const appCss = `.App {
  text-align: center;
  min-height: 100vh;
  background-color: #f8fafc;
}

.App-header {
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.App-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1f2937;
}

.task-board {
  display: flex;
  gap: 1rem;
  padding: 2rem;
  justify-content: center;
  flex-wrap: wrap;
}

.task-column {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem;
  min-width: 300px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.task-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.task-card:hover {
  background: #e2e8f0;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.priority-high {
  border-left: 4px solid #ef4444;
}

.priority-medium {
  border-left: 4px solid #f59e0b;
}

.priority-low {
  border-left: 4px solid #10b981;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  flex-direction: column;
}

.loading-spinner {
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}`;
    
    await fs.writeFile(path.join(projectPath, 'frontend/src/App.css'), appCss);
    
    // Generate README
    const readme = `# Task Management App

A modern, collaborative task management application built with React, Node.js, and PostgreSQL.

## 🚀 Quick Start

\`\`\`bash
# Clone the repository
git clone <repository-url>
cd task-management-app

# Install all dependencies
npm run install:all

# Start the application
npm start
\`\`\`

The application will be running at:
- **Frontend**: http://localhost:3000
- **Backend**: http://localhost:3001

## 🔧 Development

### Available Scripts

- \`npm start\` - Start both frontend and backend
- \`npm run dev\` - Same as start (development mode)
- \`npm run build\` - Build for production
- \`npm test\` - Run all tests
- \`npm run install:all\` - Install all dependencies

### Manual Setup

If you prefer to run services separately:

\`\`\`bash
# Terminal 1: Backend
cd backend
npm start

# Terminal 2: Frontend
cd frontend
npm start
\`\`\`

---

*Generated with KAPI's 5-Minute Project Creation System* 🚀✨`;
    
    await fs.writeFile(path.join(projectPath, 'README.md'), readme);
    
    console.log('✅ Node.js ready project generation test completed!');
    console.log('');
    console.log('📂 Generated Node.js Ready Project:');
    console.log('├── 📦 Root package.json with npm start script');
    console.log('├── 💻 Backend with server.js entry point');
    console.log('├── 🎨 Frontend with index.js and App.js');
    console.log('├── 📝 Node.js first README');
    console.log('└── 🎨 Complete CSS styling');
    console.log('');
    console.log('🚀 Node.js Development Quick Start:');
    console.log('1. cd ' + projectPath);
    console.log('2. npm run install:all');
    console.log('3. npm start');
    console.log('4. Open http://localhost:3000 (frontend) and http://localhost:3001 (backend)');
    console.log('');
    console.log('🎉 Your Node.js ready task management app is ready!');
    
  } catch (error) {
    console.error('❌ Error generating Node.js ready project:', error);
    process.exit(1);
  }
}

// Run the test
testNodeReadyProject();