const fs = require('fs').promises;
const path = require('path');

async function generateNpmFriendlyProject() {
  const projectPath = '/Users/<USER>/Code/KAPI/demo-task-manager';
  
  console.log('🚀 Updating project for npm-based development...');
  
  try {
    // Update root package.json with development scripts
    const rootPackageJson = {
      "name": "task-management-app",
      "version": "1.0.0",
      "description": "Modern collaborative task management application",
      "main": "index.js",
      "scripts": {
        "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"",
        "start:backend": "cd backend && npm run dev",
        "start:frontend": "cd frontend && npm run dev",
        "dev": "npm run start",
        "build": "npm run build:backend && npm run build:frontend",
        "build:backend": "cd backend && npm run build",
        "build:frontend": "cd frontend && npm run build",
        "test": "npm run test:backend && npm run test:frontend",
        "test:backend": "cd backend && npm test",
        "test:frontend": "cd frontend && npm test",
        "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install",
        "clean": "rm -rf node_modules backend/node_modules frontend/node_modules",
        "setup": "npm run install:all",
        "docker:up": "docker compose up -d",
        "docker:down": "docker compose down",
        "docker:build": "docker compose build"
      },
      "keywords": [
        "task-management",
        "collaboration",
        "real-time",
        "react",
        "nodejs",
        "postgresql"
      ],
      "author": "Generated by KAPI",
      "license": "MIT",
      "devDependencies": {
        "concurrently": "^8.2.0"
      },
      "workspaces": [
        "backend",
        "frontend"
      ]
    };
    
    await fs.writeFile(path.join(projectPath, 'package.json'), JSON.stringify(rootPackageJson, null, 2));
    
    // Update backend package.json with better scripts
    const backendPackageJson = {
      "name": "task-manager-backend",
      "version": "1.0.0",
      "description": "Task Management API Server",
      "main": "src/app.js",
      "scripts": {
        "start": "node src/app.js",
        "dev": "nodemon src/app.js",
        "test": "jest",
        "test:watch": "jest --watch",
        "test:coverage": "jest --coverage",
        "migrate": "node scripts/migrate.js",
        "seed": "node scripts/seed.js",
        "db:setup": "npm run migrate && npm run seed",
        "build": "echo 'Backend build complete - No build step needed for Node.js'",
        "lint": "eslint src --fix",
        "format": "prettier --write src/**/*.js"
      },
      "dependencies": {
        "express": "^4.18.2",
        "cors": "^2.8.5",
        "helmet": "^7.0.0",
        "express-rate-limit": "^6.7.0",
        "jsonwebtoken": "^9.0.0",
        "bcryptjs": "^2.4.3",
        "sequelize": "^6.32.1",
        "pg": "^8.11.0",
        "pg-hstore": "^2.3.4",
        "ws": "^8.13.0",
        "dotenv": "^16.1.4",
        "joi": "^17.9.2",
        "uuid": "^9.0.0",
        "sqlite3": "^5.1.6"
      },
      "devDependencies": {
        "nodemon": "^2.0.22",
        "jest": "^29.5.0",
        "supertest": "^6.3.3",
        "eslint": "^8.43.0",
        "prettier": "^2.8.8"
      }
    };
    
    await fs.writeFile(path.join(projectPath, 'backend/package.json'), JSON.stringify(backendPackageJson, null, 2));
    
    // Update frontend package.json with better scripts
    const frontendPackageJson = {
      "name": "task-manager-frontend",
      "version": "1.0.0",
      "description": "Task Management Frontend",
      "type": "module",
      "scripts": {
        "dev": "vite --port 3001",
        "build": "tsc && vite build",
        "preview": "vite preview",
        "test": "vitest",
        "test:ui": "vitest --ui",
        "test:coverage": "vitest --coverage",
        "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
        "lint:fix": "eslint . --ext ts,tsx --fix",
        "format": "prettier --write src/**/*.{ts,tsx}",
        "type-check": "tsc --noEmit"
      },
      "dependencies": {
        "react": "^18.2.0",
        "react-dom": "^18.2.0",
        "react-router-dom": "^6.11.2",
        "react-beautiful-dnd": "^13.1.1",
        "react-hot-toast": "^2.4.1",
        "react-hook-form": "^7.44.3",
        "react-query": "^3.39.3",
        "axios": "^1.4.0",
        "clsx": "^1.2.1",
        "date-fns": "^2.30.0",
        "lucide-react": "^0.263.1"
      },
      "devDependencies": {
        "@types/react": "^18.2.7",
        "@types/react-dom": "^18.2.4",
        "@types/react-beautiful-dnd": "^13.1.4",
        "@vitejs/plugin-react": "^4.0.0",
        "typescript": "^5.0.2",
        "vite": "^4.3.9",
        "vitest": "^0.32.2",
        "autoprefixer": "^10.4.14",
        "postcss": "^8.4.24",
        "tailwindcss": "^3.3.2",
        "eslint": "^8.43.0",
        "eslint-plugin-react": "^7.32.2",
        "eslint-plugin-react-hooks": "^4.6.0",
        "eslint-plugin-react-refresh": "^0.4.1",
        "prettier": "^2.8.8"
      }
    };
    
    await fs.writeFile(path.join(projectPath, 'frontend/package.json'), JSON.stringify(frontendPackageJson, null, 2));
    
    // Create a simple SQLite database config for development
    const devDbConfig = `const { Sequelize } = require('sequelize');
const path = require('path');
require('dotenv').config();

let sequelize;

if (process.env.NODE_ENV === 'production') {
  // Use PostgreSQL in production
  sequelize = new Sequelize(
    process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/task_manager',
    {
      dialect: 'postgres',
      logging: false,
      pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
      }
    }
  );
} else {
  // Use SQLite for development (no setup required)
  sequelize = new Sequelize({
    dialect: 'sqlite',
    storage: path.join(__dirname, '../data/database.sqlite'),
    logging: process.env.NODE_ENV === 'development' ? console.log : false
  });
}

// Test connection
sequelize.authenticate()
  .then(() => {
    console.log('✅ Database connection established successfully.');
  })
  .catch(err => {
    console.error('❌ Unable to connect to the database:', err);
  });

module.exports = sequelize;`;
    
    await fs.writeFile(path.join(projectPath, 'backend/src/config/database.js'), devDbConfig);
    
    // Create database directory
    await fs.mkdir(path.join(projectPath, 'backend/data'), { recursive: true });
    
    // Create database setup script
    const setupScript = `const sequelize = require('../src/config/database');
const User = require('../src/models/User');
const Task = require('../src/models/Task');
const Team = require('../src/models/Team');

async function setupDatabase() {
  try {
    console.log('🔄 Setting up database...');
    
    // Create tables
    await sequelize.sync({ force: true });
    console.log('✅ Database tables created');
    
    // Create sample data
    const user = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      name: 'Demo User',
      role: 'user'
    });
    
    const team = await Team.create({
      name: 'Demo Team',
      description: 'A sample team for testing',
      ownerId: user.id
    });
    
    const tasks = await Task.bulkCreate([
      {
        title: 'Welcome to Task Manager',
        description: 'This is your first task. Try dragging it to different columns!',
        status: 'pending',
        priority: 'medium',
        userId: user.id,
        teamId: team.id
      },
      {
        title: 'Create a new task',
        description: 'Click the + button to add your own task',
        status: 'pending',
        priority: 'low',
        userId: user.id,
        teamId: team.id
      },
      {
        title: 'Explore real-time updates',
        description: 'Open the app in multiple tabs to see live updates',
        status: 'in_progress',
        priority: 'high',
        userId: user.id,
        teamId: team.id
      }
    ]);
    
    console.log('✅ Sample data created');
    console.log('📧 Demo login: <EMAIL> / password123');
    console.log('🎉 Database setup complete!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error);
  } finally {
    await sequelize.close();
  }
}

setupDatabase();`;
    
    await fs.mkdir(path.join(projectPath, 'backend/scripts'), { recursive: true });
    await fs.writeFile(path.join(projectPath, 'backend/scripts/setup.js'), setupScript);
    
    // Update the main app.js to auto-setup database
    const updatedApp = `const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const WebSocket = require('ws');
const path = require('path');
require('dotenv').config();

// Import database
const sequelize = require('./config/database');

// Import models to ensure they're loaded
const User = require('./models/User');
const Task = require('./models/Task');
const Team = require('./models/Team');

// Import routes
const authRoutes = require('./routes/auth.routes');
const taskRoutes = require('./routes/task.routes');
const teamRoutes = require('./routes/team.routes');

// Import middleware
const { authenticateToken } = require('./middleware/auth.middleware');
const { errorHandler } = require('./middleware/error.middleware');

const app = express();
const server = createServer(app);

// WebSocket server
const wss = new WebSocket.Server({ server });

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' ? 
    process.env.CORS_ORIGIN?.split(',') || ['https://yourdomain.com'] : 
    ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { 
    success: false, 
    error: 'Too many requests from this IP, please try again later.',
    code: 'RATE_LIMIT_EXCEEDED'
  }
});
app.use('/api/', limiter);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Add WebSocket to request object
app.use((req, res, next) => {
  req.wss = wss;
  next();
});

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    database: sequelize.options.dialect
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/tasks', authenticateToken, taskRoutes);
app.use('/api/teams', authenticateToken, teamRoutes);

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../frontend/dist')));
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/dist/index.html'));
  });
}

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log('🔌 New WebSocket connection');
  
  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      
      // Handle different message types
      switch (data.type) {
        case 'authenticate':
          ws.userId = data.userId;
          ws.send(JSON.stringify({ type: 'authenticated', userId: data.userId }));
          break;
        case 'join_room':
          ws.roomId = data.roomId;
          break;
        case 'task_update':
          // Broadcast task updates to all clients
          wss.clients.forEach((client) => {
            if (client !== ws && client.readyState === WebSocket.OPEN) {
              client.send(JSON.stringify({
                type: 'task_update',
                data: data.payload
              }));
            }
          });
          break;
      }
    } catch (error) {
      console.error('WebSocket message error:', error);
    }
  });
  
  ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
  });
  
  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
});

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: \`Route not found: \${req.originalUrl}\`,
    code: 'ROUTE_NOT_FOUND'
  });
});

const PORT = process.env.PORT || 3000;

// Initialize database and start server
async function startServer() {
  try {
    // Sync database (create tables if they don't exist)
    await sequelize.sync({ alter: true });
    console.log('✅ Database synchronized');
    
    // Start server
    server.listen(PORT, () => {
      console.log('🚀 Task Manager Backend Server');
      console.log(\`📡 Server running on port \${PORT}\`);
      console.log(\`🌐 API: http://localhost:\${PORT}/api\`);
      console.log(\`🏥 Health: http://localhost:\${PORT}/health\`);
      console.log(\`🔌 WebSocket: ws://localhost:\${PORT}\`);
      console.log(\`📊 Environment: \${process.env.NODE_ENV || 'development'}\`);
      
      if (process.env.NODE_ENV !== 'production') {
        console.log('💡 Tip: Run "npm run db:setup" to create sample data');
      }
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;`;
    
    await fs.writeFile(path.join(projectPath, 'backend/src/app.js'), updatedApp);
    
    // Update the environment file for development
    const devEnv = `# Development Environment Configuration

# Database - Uses SQLite for development (no setup required)
# For production, use: DATABASE_URL=postgresql://postgres:password@localhost:5432/task_manager
NODE_ENV=development

# Server Configuration
PORT=3000

# JWT Secret (change this in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# CORS Origins
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# WebSocket Configuration
WS_PORT=3001`;
    
    await fs.writeFile(path.join(projectPath, 'backend/.env'), devEnv);
    
    // Create an updated README with npm instructions
    const newReadme = `# 🚀 Task Management App

A modern, collaborative task management application built with React, Node.js, and real-time WebSocket updates.

## ✨ Features

- **🎯 Task Management** - Create, update, and organize tasks with drag-and-drop
- **👥 Team Collaboration** - Work together with real-time updates
- **📱 Mobile Responsive** - Works perfectly on all devices
- **🔐 Authentication** - Secure JWT-based user authentication
- **⚡ Real-time Updates** - See changes instantly across all connected clients
- **🎨 Modern UI** - Clean, intuitive interface with Tailwind CSS
- **🚀 Production Ready** - Docker support and deployment configurations

## 🛠️ Tech Stack

- **Frontend**: React 18 + TypeScript + Tailwind CSS + Vite
- **Backend**: Node.js + Express + WebSocket
- **Database**: SQLite (development) / PostgreSQL (production)
- **Authentication**: JWT tokens
- **Real-time**: WebSocket
- **Deployment**: Docker + Kubernetes

## 🚀 Quick Start (Recommended)

### Option 1: npm Development (Easiest)

1. **Clone and navigate:**
   \`\`\`bash
   cd /Users/<USER>/Code/KAPI/demo-task-manager
   \`\`\`

2. **Install dependencies:**
   \`\`\`bash
   npm run setup
   \`\`\`

3. **Start the application:**
   \`\`\`bash
   npm start
   \`\`\`

4. **Open your browser:**
   - Frontend: http://localhost:3001
   - Backend: http://localhost:3000

5. **Optional - Create sample data:**
   \`\`\`bash
   cd backend
   npm run db:setup
   \`\`\`
   Then login with: \`<EMAIL>\` / \`password123\`

### Option 2: Docker (Production-like)

1. **Install Docker Desktop** (see DOCKER_SETUP.md)
2. **Start with Docker:**
   \`\`\`bash
   docker compose up -d
   \`\`\`

## 📋 Available Scripts

### Root Level
- \`npm start\` - Start both frontend and backend
- \`npm run dev\` - Same as start (development mode)
- \`npm run setup\` - Install all dependencies
- \`npm run build\` - Build for production
- \`npm test\` - Run all tests

### Backend Only
- \`npm run start:backend\` - Start backend server
- \`cd backend && npm run dev\` - Start backend in watch mode
- \`cd backend && npm run db:setup\` - Create sample data

### Frontend Only
- \`npm run start:frontend\` - Start frontend dev server
- \`cd frontend && npm run dev\` - Start frontend in watch mode

## 🎯 Getting Started Guide

### 1. First Time Setup
\`\`\`bash
# Install everything
npm run setup

# Start the app
npm start

# In another terminal, create sample data
cd backend && npm run db:setup
\`\`\`

### 2. Login
- Go to http://localhost:3001
- Register a new account or use demo account:
  - Email: \`<EMAIL>\`
  - Password: \`password123\`

### 3. Try Key Features
- **Create tasks** - Click the + button
- **Drag and drop** - Move tasks between columns
- **Real-time updates** - Open multiple browser tabs
- **Team collaboration** - Create teams and assign tasks

## 🏗️ Project Structure

\`\`\`
task-management-app/
├── 📦 package.json          # Root scripts and dependencies
├── 🚀 backend/               # Express.js API server
│   ├── src/
│   │   ├── app.js           # Main server file
│   │   ├── models/          # Database models (User, Task, Team)
│   │   ├── routes/          # API routes
│   │   ├── controllers/     # Business logic
│   │   ├── middleware/      # Auth, validation, error handling
│   │   └── config/          # Database and app configuration
│   ├── scripts/             # Database setup scripts
│   ├── data/                # SQLite database (development)
│   └── package.json         # Backend dependencies
├── 🎨 frontend/              # React TypeScript application
│   ├── src/
│   │   ├── App.tsx          # Main app component
│   │   ├── components/      # Reusable components
│   │   ├── pages/           # Page components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── contexts/        # React contexts
│   │   └── services/        # API services
│   ├── public/              # Static assets
│   └── package.json         # Frontend dependencies
├── 🐳 docker-compose.yml    # Docker multi-service setup
├── 📋 tests/                # Test suites
└── 📚 docs/                 # Documentation
\`\`\`

## 🔧 Development

### Backend Development
\`\`\`bash
cd backend
npm run dev          # Start with nodemon (auto-restart)
npm test            # Run tests
npm run db:setup    # Reset database with sample data
\`\`\`

### Frontend Development
\`\`\`bash
cd frontend
npm run dev         # Start Vite dev server
npm test           # Run tests
npm run build      # Build for production
\`\`\`

## 🗄️ Database

### Development (SQLite)
- **Location**: \`backend/data/database.sqlite\`
- **No setup required** - automatically created
- **Reset**: Delete the file and restart the server

### Production (PostgreSQL)
- Set \`DATABASE_URL\` environment variable
- Run migrations: \`npm run migrate\`

## 🔐 Authentication

### JWT Tokens
- **Login**: \`POST /api/auth/login\`
- **Register**: \`POST /api/auth/register\`
- **Token**: Include in \`Authorization: Bearer <token>\` header

### Demo Account
- **Email**: \`<EMAIL>\`
- **Password**: \`password123\`

## 🌐 API Endpoints

### Authentication
- \`POST /api/auth/register\` - Register new user
- \`POST /api/auth/login\` - Login user
- \`GET /api/auth/me\` - Get current user

### Tasks
- \`GET /api/tasks\` - Get user's tasks
- \`POST /api/tasks\` - Create new task
- \`PUT /api/tasks/:id\` - Update task
- \`DELETE /api/tasks/:id\` - Delete task

### Teams
- \`GET /api/teams\` - Get user's teams
- \`POST /api/teams\` - Create new team
- \`PUT /api/teams/:id\` - Update team

## 🚀 Deployment

### Docker Deployment
\`\`\`bash
docker compose up -d
\`\`\`

### Manual Deployment
\`\`\`bash
# Build frontend
cd frontend && npm run build

# Start backend (serves built frontend)
cd backend && npm start
\`\`\`

### Environment Variables
\`\`\`bash
NODE_ENV=production
DATABASE_URL=********************************/db
JWT_SECRET=your-secret-key
PORT=3000
CORS_ORIGIN=https://yourdomain.com
\`\`\`

## 🧪 Testing

### Run All Tests
\`\`\`bash
npm test
\`\`\`

### Backend Tests
\`\`\`bash
cd backend
npm test                 # Run once
npm run test:watch       # Watch mode
npm run test:coverage    # With coverage
\`\`\`

### Frontend Tests
\`\`\`bash
cd frontend
npm test                 # Run once
npm run test:ui          # UI mode
npm run test:coverage    # With coverage
\`\`\`

## 🛠️ Troubleshooting

### Common Issues

1. **Port conflicts**
   - Change ports in \`package.json\` scripts
   - Default: Backend 3000, Frontend 3001

2. **Database issues**
   - Delete \`backend/data/database.sqlite\` and restart
   - Run \`npm run db:setup\` to recreate sample data

3. **Module not found**
   - Run \`npm run setup\` to install all dependencies
   - Clear node_modules: \`npm run clean && npm run setup\`

4. **Build errors**
   - Check Node.js version (recommended: 18+)
   - Update dependencies: \`npm update\`

### Getting Help
- Check the console for error messages
- Review the \`/health\` endpoint: http://localhost:3000/health
- Look at the browser dev tools Network tab

## 📊 Performance

### Development
- **SQLite** database for zero-setup development
- **Hot reloading** for both frontend and backend
- **Concurrent** script to run both servers

### Production
- **PostgreSQL** for robust data storage
- **Built frontend** served by Express
- **WebSocket** for real-time updates
- **Docker** for containerized deployment

## 🎯 Next Steps

1. **Customize the UI** - Modify colors, layout, components
2. **Add features** - File uploads, notifications, advanced filters
3. **Integrate services** - Email, push notifications, analytics
4. **Scale up** - Add Redis, load balancing, monitoring
5. **Deploy** - Use the included Docker/Kubernetes configs

## 🙏 Credits

Generated by **KAPI's 5-Minute Project Creation System** - from idea to production-ready app in minutes!

---

## 📝 License

MIT License - feel free to use this project as a starting point for your own applications.

---

🎉 **Happy coding!** Your task management app is ready to go!`;
    
    await fs.writeFile(path.join(projectPath, 'README.md'), newReadme);
    
    console.log('✅ Project updated for npm-based development!');
    console.log('');
    console.log('🚀 Quick Start Instructions:');
    console.log('1. cd /Users/<USER>/Code/KAPI/demo-task-manager');
    console.log('2. npm run setup');
    console.log('3. npm start');
    console.log('4. Open http://localhost:3001');
    console.log('');
    console.log('🎯 Key Features:');
    console.log('- ✅ npm start (no Docker needed)');
    console.log('- ✅ SQLite database (zero setup)');
    console.log('- ✅ Hot reloading for development');
    console.log('- ✅ Sample data with npm run db:setup');
    console.log('- ✅ Production Docker option still available');
    console.log('');
    console.log('🎉 Project is now npm-friendly and ready to use!');
    
  } catch (error) {
    console.error('❌ Error updating project:', error);
    process.exit(1);
  }
}

generateNpmFriendlyProject();