/**
 * Test the updated trigger logic with ML project conversation
 */

function testMLProjectTrigger() {
  console.log('🧪 [ML-TRIGGER-TEST] Testing ML project trigger detection...');
  
  // Simulate the conversation from the logs
  const conversationMessages = [
    'what kind of project would you like to build today?',
    'linear regression ml project', // User response
    'I\'ve saved your linear regression ML project setup' // Assistant response that should trigger
  ];
  
  const conversationText = conversationMessages.join(' ').toLowerCase();
  
  console.log(`🔍 [ML-TRIGGER-TEST] Conversation text: "${conversationText}"`);
  
  // Test all trigger conditions
  const hasBuildIntent = /\b(build|create|make|develop|generate)\b/.test(conversationText);
  const hasProjectType = /\b(app|application|website|api|dashboard|system|tool)\b/.test(conversationText);
  const hasTechInfo = /\b(react|vue|angular|node|python|java|database|frontend|backend)\b/.test(conversationText);
  const hasCreationTrigger = /\b(let's build|start building|create this|generate the project|ready to build)\b/.test(conversationText);
  const hasCompletionIndicator = /\b(ready|let's start|go ahead|create it|build it|start the project)\b/.test(conversationText);
  const hasProjectSetupComplete = /\b(saved your|setup|configured|project setup|ml project|created your)\b/.test(conversationText);
  const hasMLProject = /\b(ml|machine learning|ai|linear regression|neural network|deep learning)\b/.test(conversationText);
  
  const readyToCreate = hasCreationTrigger || hasCompletionIndicator || 
                       (hasProjectSetupComplete && (hasMLProject || hasProjectType || hasTechInfo)) ||
                       (hasBuildIntent && (hasProjectType || hasTechInfo));
  
  console.log(`🚀 [ML-TRIGGER-TEST] Creation criteria analysis:`);
  console.log(`   - Build Intent: ${hasBuildIntent}`);
  console.log(`   - Project Type: ${hasProjectType}`);
  console.log(`   - Tech Info: ${hasTechInfo}`);
  console.log(`   - Creation Trigger: ${hasCreationTrigger}`);
  console.log(`   - Completion Indicator: ${hasCompletionIndicator}`);
  console.log(`   - Project Setup Complete: ${hasProjectSetupComplete}`);
  console.log(`   - ML Project: ${hasMLProject}`);
  console.log(`   - Ready to Create: ${readyToCreate}`);
  
  if (readyToCreate) {
    console.log('✅ [ML-TRIGGER-TEST] SUCCESS: ML project conversation SHOULD trigger project creation!');
    console.log('🚀 [ML-TRIGGER-TEST] The updated logic correctly detects ML project completion.');
  } else {
    console.log('❌ [ML-TRIGGER-TEST] FAILURE: ML project conversation does NOT trigger project creation.');
    console.log('⚠️ [ML-TRIGGER-TEST] The trigger logic needs further adjustment.');
  }
  
  return readyToCreate;
}

// Test with more specific conversation patterns
function testVariousMLConversations() {
  console.log('\n🧪 [ML-TRIGGER-TEST] Testing various ML conversation patterns...');
  
  const testCases = [
    {
      name: 'Linear Regression Setup',
      conversation: 'what kind of project would you like to build today? linear regression ml project I\'ve saved your linear regression ML project setup',
      shouldTrigger: true
    },
    {
      name: 'Machine Learning App',
      conversation: 'I want to build a machine learning application for data analysis',
      shouldTrigger: true
    },
    {
      name: 'AI Project Ready',
      conversation: 'your AI project setup is complete and ready to build',
      shouldTrigger: true
    },
    {
      name: 'Incomplete Setup',
      conversation: 'tell me more about your project requirements',
      shouldTrigger: false
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n📋 [ML-TRIGGER-TEST] Testing: ${testCase.name}`);
    
    const conversationText = testCase.conversation.toLowerCase();
    
    const hasBuildIntent = /\b(build|create|make|develop|generate)\b/.test(conversationText);
    const hasProjectType = /\b(app|application|website|api|dashboard|system|tool)\b/.test(conversationText);
    const hasTechInfo = /\b(react|vue|angular|node|python|java|database|frontend|backend)\b/.test(conversationText);
    const hasCreationTrigger = /\b(let's build|start building|create this|generate the project|ready to build)\b/.test(conversationText);
    const hasCompletionIndicator = /\b(ready|let's start|go ahead|create it|build it|start the project)\b/.test(conversationText);
    const hasProjectSetupComplete = /\b(saved your|setup|configured|project setup|ml project|created your)\b/.test(conversationText);
    const hasMLProject = /\b(ml|machine learning|ai|linear regression|neural network|deep learning)\b/.test(conversationText);
    
    const readyToCreate = hasCreationTrigger || hasCompletionIndicator || 
                         (hasProjectSetupComplete && (hasMLProject || hasProjectType || hasTechInfo)) ||
                         (hasBuildIntent && (hasProjectType || hasTechInfo));
    
    const result = readyToCreate === testCase.shouldTrigger ? '✅ PASS' : '❌ FAIL';
    console.log(`   Result: ${result} (Expected: ${testCase.shouldTrigger}, Got: ${readyToCreate})`);
    
    if (readyToCreate !== testCase.shouldTrigger) {
      console.log(`   Analysis: BuildIntent=${hasBuildIntent}, ProjectType=${hasProjectType}, TechInfo=${hasTechInfo}`);
      console.log(`   ML: MLProject=${hasMLProject}, SetupComplete=${hasProjectSetupComplete}`);
    }
  });
}

// Run tests
console.log('🧪 [ML-TRIGGER-TEST] Starting ML project trigger tests...');
testMLProjectTrigger();
testVariousMLConversations();
console.log('\n🏁 [ML-TRIGGER-TEST] ML trigger tests completed!');