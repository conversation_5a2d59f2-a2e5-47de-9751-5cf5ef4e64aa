{"name": "kapi-node-backend", "version": "1.0.0", "description": "KAPI Node.js Backend - Unified architecture for KAPI IDE and Modern AI Pro", "main": "dist/server.js", "scripts": {"build": "tsc -p tsconfig.json", "build:prod": "tsc -p tsconfig.prod.json", "build:next": "cd src/next && next build", "build:all": "npm run prisma:generate && npm run build && npm run build:next", "dev": "concurrently \"./start-chromadb.sh\" \"PORT=3000 nodemon\" \"cd src/next && npm run dev\"", "start": "node dist/server.js", "start:prod": "npm run prisma:generate && npm run build:all && npm run start", "install:all": "npm install && cd src/next && npm install", "lint": "eslint src/**/*.ts --fix", "lint:check": "eslint src/**/*.ts", "format": "prettier --write \"src/**/*.{ts,tsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,json,md}\"", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:coverage": "NODE_ENV=test jest --coverage", "test:unit": "NODE_ENV=test jest --testPathIgnorePatterns='integration'", "test:integration": "NODE_ENV=test jest tests/integration", "test:admin-models": "NODE_ENV=test npx tsx tests/test-admin-models.ts", "test:google-tts": "npx ts-node tests/audio/google-tts.test.ts", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:migrate": "prisma migrate dev", "prisma:migrate:prod": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:format": "prisma format", "setup:prisma": "chmod +x setup-prisma.sh && ./setup-prisma.sh", "create-admin-user": "npx tsx scripts/create-admin-user.ts", "create-admin-user-simple": "npx tsx scripts/create-admin-user-simple.ts", "apply-indexes": "npx ts-node scripts/apply-performance-indexes.ts", "analyze-code": "npx tsx src/scripts/analyze-code.ts", "test-api-analysis": "npx tsx src/scripts/test-api-analysis.ts", "chromadb:start": "./start-chromadb.sh", "chromadb:stop": "./stop-chromadb.sh", "chromadb:restart": "./stop-chromadb.sh && ./start-chromadb.sh", "chromadb:test": "node test-chromadb-connection.js", "chromadb:test-semantic": "node test-semantic-search-simple.js", "test:docs": "node test-documentation-generation.js", "test:docs-python": "./test-python-doc-generation.sh", "test:semantic-api": "node test-semantic-search-api.js", "test:workflow": "node test-end-to-end-workflow.js", "test:all-features": "npm run chromadb:test && npm run test:docs && npm run test:workflow", "prepare": "husky"}, "keywords": ["kapi", "ai", "ide", "node", "typescript"], "author": "KAPI Team", "license": "Proprietary", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@aws-sdk/client-bedrock-runtime": "^3.810.0", "@aws-sdk/credential-providers": "^3.810.0", "@azure/identity": "^4.10.0", "@chroma-core/default-embed": "^0.1.8", "@clerk/clerk-sdk-node": "^5.1.6", "@google-cloud/text-to-speech": "^6.1.0", "@google/genai": "^1.3.0", "@nestjs/common": "^11.1.1", "@nestjs/core": "^11.1.1", "@overnightjs/core": "^1.7.6", "@prisma/client": "^6.8.2", "@smithy/node-http-handler": "^4.0.4", "@smithy/types": "^4.2.0", "@types/redis": "^4.0.11", "@xenova/transformers": "^2.17.2", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "chromadb": "^3.0.9", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.5", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "form-data": "^4.0.2", "helmet": "^7.2.0", "inversify": "^7.5.1", "joi": "^17.13.3", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "jwk-to-pem": "^2.0.7", "lucide-react": "^0.511.0", "mermaid": "^11.9.0", "next": "^15.3.2", "nunjucks": "^3.2.4", "openai": "^5.0.1", "pg": "^8.16.0", "prisma": "^6.7.0", "redis": "^5.1.1", "reflect-metadata": "^0.2.1", "rxjs": "^7.8.2", "simple-git": "^3.28.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-morph": "^26.0.0", "tsyringe": "^4.10.0", "winston": "^3.17.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.6", "@types/jwk-to-pem": "^2.0.3", "@types/node": "^20.11.25", "@types/nunjucks": "^3.2.6", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "concurrently": "^9.1.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.2.0", "nodemon": "^3.1.0", "prettier": "^3.3.3", "supertest": "^7.1.1", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.4.2"}, "overrides": {"@clerk/shared": "^3.9.2"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}