// Direct API test for 5-minute project creation
const axios = require('axios');
const fs = require('fs').promises;

async function testProjectCreationAPI() {
  console.log('🌐 Testing 5-Minute Project Creation via API...');
  
  const baseURL = 'http://localhost:3000'; // Adjust if different
  const testProjectPath = '/Users/<USER>/Code/KAPI/demo-api-test-project';
  
  // Mock interview request
  const requestData = {
    userId: 'test-user-123',
    projectPath: testProjectPath,
    responses: [
      {
        question: "What's your main goal with this project?",
        answer: "🆕 Building a new app",
        context: { objective: 'build-new' }
      },
      {
        question: "What kind of project are you working on?",
        answer: "🌐 Web Application - A task management app for my team",
        context: { type: 'web-app', description: 'task management app for team' }
      },
      {
        question: "What's the core feature that users will love?",
        answer: "Real-time collaboration with drag-and-drop task boards",
        context: { 
          features: ['real-time collaboration', 'drag-and-drop', 'notifications'],
          priority: 'high'
        }
      }
    ]
  };
  
  try {
    console.log('📤 Sending project creation request...');
    const startTime = Date.now();
    
    const response = await axios.post(`${baseURL}/api/projects/create`, requestData, {
      timeout: 300000 // 5 minutes timeout
    });
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`✅ API Response received in ${duration.toFixed(2)} seconds`);
    console.log('📊 Response data:', response.data);
    
    // Verify the generated project
    if (response.data.success) {
      console.log('🔍 Verifying generated project...');
      
      try {
        await fs.access(testProjectPath);
        console.log('✅ Project directory created');
        
        // Check key files
        const keyFiles = ['package.json', 'README.md', 'docs/README.md'];
        for (const file of keyFiles) {
          try {
            await fs.access(`${testProjectPath}/${file}`);
            console.log(`✅ ${file} exists`);
          } catch (error) {
            console.log(`❌ ${file} missing`);
          }
        }
        
      } catch (error) {
        console.log('❌ Project directory not found');
      }
    }
    
    return true;
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.log('❌ API server not running. Start with: npm run dev');
      console.log('💡 Alternative: Use the direct service test instead');
    } else {
      console.error('❌ API test failed:', error.message);
    }
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  testProjectCreationAPI()
    .then(success => {
      if (success) {
        console.log('✅ API test passed!');
        process.exit(0);
      } else {
        console.log('❌ API test failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Test error:', error);
      process.exit(1);
    });
}

module.exports = { testProjectCreationAPI };