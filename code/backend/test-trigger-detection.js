/**
 * Test the trigger detection logic to see if onboarding flow triggers project creation
 * This tests the core logic without the dependency injection complications
 */

// Simple implementation to test the trigger detection
class TriggerDetectionTest {
  
  /**
   * Determine if we have enough information to start project creation
   * This is the core logic from the strategy
   */
  shouldTriggerProjectCreation(messages) {
    const conversationText = messages.map(m => m?.content || '').join(' ').toLowerCase();
    
    console.log(`🔍 [TRIGGER-TEST] Analyzing conversation: "${conversationText.substring(0, 200)}..."`);
    
    // Look for build/create intent
    const hasBuildIntent = /\b(build|create|make|develop|generate)\b/.test(conversationText);
    
    // Look for project type
    const hasProjectType = /\b(app|application|website|api|dashboard|system|tool)\b/.test(conversationText);
    
    // Look for tech stack or requirements
    const hasTechInfo = /\b(react|vue|angular|node|python|java|database|frontend|backend)\b/.test(conversationText);
    
    // Look for explicit creation trigger phrases
    const hasCreationTrigger = /\b(let's build|start building|create this|generate the project|ready to build)\b/.test(conversationText);
    
    // Also check for completion/readiness indicators
    const hasCompletionIndicator = /\b(ready|let's start|go ahead|create it|build it|start the project|sounds perfect)\b/.test(conversationText);
    
    // More relaxed trigger condition for testing
    const readyToCreate = hasCreationTrigger || hasCompletionIndicator || (hasBuildIntent && (hasProjectType || hasTechInfo));
    
    console.log(`🚀 [TRIGGER-TEST] Creation criteria analysis:`);
    console.log(`   - Build Intent: ${hasBuildIntent}`);
    console.log(`   - Project Type: ${hasProjectType}`);
    console.log(`   - Tech Info: ${hasTechInfo}`);
    console.log(`   - Creation Trigger: ${hasCreationTrigger}`);
    console.log(`   - Completion Indicator: ${hasCompletionIndicator}`);
    console.log(`   - Ready to Create: ${readyToCreate}`);
    
    return readyToCreate;
  }

  /**
   * Convert conversation messages to interview responses format
   */
  convertConversationToInterviewResponses(messages) {
    const responses = [];
    
    for (let i = 0; i < messages.length - 1; i += 2) {
      const question = messages[i];
      const answer = messages[i + 1];
      
      if (question?.role === 'assistant' && answer?.role === 'user') {
        responses.push({
          question: question.content || '',
          answer: answer.content || '',
          context: {
            timestamp: answer.createdAt || new Date(),
            messageId: answer.id
          }
        });
      }
    }
    
    console.log(`🚀 [TRIGGER-TEST] Converted ${messages.length} messages to ${responses.length} interview responses`);
    return responses;
  }

  /**
   * Simulate project creation trigger check
   */
  async checkProjectCreationTrigger(messages) {
    try {
      console.log(`🔍 [TRIGGER-TEST] Checking project creation trigger for ${messages.length} messages`);
      
      // Extract conversation history to analyze readiness
      const allMessages = messages.filter(Boolean);
      
      console.log(`🔍 [TRIGGER-TEST] Total messages to analyze: ${allMessages.length}`);
      
      // Look for key indicators that we should start project creation
      const shouldTriggerCreation = this.shouldTriggerProjectCreation(allMessages);
      
      if (shouldTriggerCreation) {
        console.log(`🚀 [TRIGGER-TEST] ✅ SHOULD TRIGGER project creation!`);
        
        // Convert conversation to interview responses
        const interviewResponses = this.convertConversationToInterviewResponses(allMessages);
        
        console.log(`🚀 [TRIGGER-TEST] Would trigger project creation with ${interviewResponses.length} interview responses`);
        console.log(`🚀 [TRIGGER-TEST] Interview data:`);
        interviewResponses.forEach((response, index) => {
          console.log(`   ${index + 1}. Q: ${response.question.substring(0, 60)}...`);
          console.log(`      A: ${response.answer.substring(0, 60)}...`);
        });
        
        return true;
      } else {
        console.log(`⏳ [TRIGGER-TEST] ❌ NOT ready for project creation yet`);
        return false;
      }
    } catch (error) {
      console.error('❌ [TRIGGER-TEST] Error checking for project creation trigger:', error);
      return false;
    }
  }
}

async function testTriggerDetection() {
  console.log('🧪 [TRIGGER-TEST] Testing trigger detection logic...');
  
  const triggerTest = new TriggerDetectionTest();
  
  // Test Case 1: Incomplete conversation (should NOT trigger)
  console.log('\n📋 [TRIGGER-TEST] === TEST CASE 1: Incomplete Conversation ===');
  const incompleteMessages = [
    {
      role: 'assistant',
      content: 'What kind of project would you like to build?'
    },
    {
      role: 'user', 
      content: 'I\'m not sure yet, maybe something with AI'
    }
  ];
  
  const shouldNotTrigger = await triggerTest.checkProjectCreationTrigger(incompleteMessages);
  console.log(`📋 [TRIGGER-TEST] Result: ${shouldNotTrigger ? 'TRIGGERED (unexpected!)' : 'NOT TRIGGERED (expected)'}`);

  // Test Case 2: Complete conversation with explicit trigger (should trigger)
  console.log('\n📋 [TRIGGER-TEST] === TEST CASE 2: Complete Conversation with Trigger ===');
  const completeMessages = [
    {
      role: 'assistant',
      content: 'What kind of project would you like to build?'
    },
    {
      role: 'user',
      content: 'I want to build a productivity application with AI-powered task insights using React and Node.js'
    },
    {
      role: 'assistant', 
      content: 'That sounds like a great project! Do you want me to include features like user authentication, a dashboard, and database integration?'
    },
    {
      role: 'user',
      content: 'Yes, that sounds perfect! Let\'s build this project with all those features.'
    }
  ];
  
  const shouldTrigger = await triggerTest.checkProjectCreationTrigger(completeMessages);
  console.log(`📋 [TRIGGER-TEST] Result: ${shouldTrigger ? 'TRIGGERED (expected!)' : 'NOT TRIGGERED (unexpected!)'}`);

  // Test Case 3: Direct build request (should trigger immediately)
  console.log('\n📋 [TRIGGER-TEST] === TEST CASE 3: Direct Build Request ===');
  const directRequestMessages = [
    {
      role: 'user',
      content: 'I have a productivity app idea with React and Node.js. Can you build this project for me?'
    }
  ];
  
  const shouldTriggerDirect = await triggerTest.checkProjectCreationTrigger(directRequestMessages);
  console.log(`📋 [TRIGGER-TEST] Result: ${shouldTriggerDirect ? 'TRIGGERED (expected!)' : 'NOT TRIGGERED (unexpected!)'}`);

  // Test Case 4: Onboarding-style conversation (real scenario)
  console.log('\n📋 [TRIGGER-TEST] === TEST CASE 4: Real Onboarding Flow ===');
  const onboardingMessages = [
    {
      role: 'assistant',
      content: 'What kind of project would you like to build today?'
    },
    {
      role: 'user',
      content: 'I want to build a productivity application with AI-powered task insights using React and Node.js'
    },
    {
      role: 'assistant',
      content: 'That sounds like a great project! Do you want me to include features like user authentication, a dashboard, and database integration?'
    },
    {
      role: 'user',
      content: 'Yes, that sounds perfect! Let\'s build this project with all those features.'
    }
  ];
  
  const shouldTriggerOnboarding = await triggerTest.checkProjectCreationTrigger(onboardingMessages);
  console.log(`📋 [TRIGGER-TEST] Result: ${shouldTriggerOnboarding ? 'TRIGGERED (expected!)' : 'NOT TRIGGERED (unexpected!)'}`);

  console.log('\n🎉 [TRIGGER-TEST] Trigger detection test completed!');
  console.log('📊 [TRIGGER-TEST] Summary:');
  console.log(`   - Incomplete conversation: ${shouldNotTrigger ? '❌ Unexpected trigger' : '✅ Correctly did not trigger'}`);
  console.log(`   - Complete conversation: ${shouldTrigger ? '✅ Correctly triggered' : '❌ Should have triggered'}`);
  console.log(`   - Direct request: ${shouldTriggerDirect ? '✅ Correctly triggered' : '❌ Should have triggered'}`);
  console.log(`   - Onboarding flow: ${shouldTriggerOnboarding ? '✅ Correctly triggered' : '❌ Should have triggered'}`);
  
  const allWorking = !shouldNotTrigger && shouldTrigger && shouldTriggerDirect && shouldTriggerOnboarding;
  console.log(`\n${allWorking ? '🎉 ALL TESTS PASSED!' : '⚠️ SOME TESTS FAILED'}`);
  
  if (allWorking) {
    console.log('✅ [TRIGGER-TEST] The trigger detection logic is working correctly!');
    console.log('✅ [TRIGGER-TEST] Real onboarding conversations should trigger project creation.');
  } else {
    console.log('❌ [TRIGGER-TEST] There are issues with the trigger detection logic.');
  }
}

// Run the test
testTriggerDetection().then(() => {
  console.log('🏁 [TRIGGER-TEST] Test execution finished');
  process.exit(0);
}).catch(error => {
  console.error('💥 [TRIGGER-TEST] Unhandled error:', error);
  process.exit(1);
});