import { Request, Response } from 'express';
import { randomUUID } from 'crypto';

import { logger } from '../../../common/logger';
import { ClerkSdkUser } from '../../../common/types';
import { clerkService } from '../../../services/clerk.service';

// In-memory storage for device codes (in production, use Redis or database)
const deviceCodes = new Map<string, {
  deviceCode: string;
  userCode: string;
  verificationUri: string;
  expiresAt: Date;
  interval: number;
  status: 'pending' | 'completed' | 'expired';
  clerkUserId?: string;
  accessToken?: string;
}>();

/**
 * Controller for auth-related endpoints
 */
export class AuthController {
  /**
   * Start device authorization flow for CLI clients
   * POST /api/auth/device/authorize
   */
  async deviceAuthorize(req: Request, res: Response): Promise<Response> {
    try {
      const { client_type = 'terminal', device_name = 'KAPI CLI' } = req.body;

      // Generate device and user codes
      const deviceCode = randomUUID();
      const userCode = this.generateUserCode();
      const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
      const interval = 5; // Poll every 5 seconds

      // Use backend admin interface for device authentication
      const backendUrl = process.env.KAPI_API_URL || 'http://localhost:3000';
      const verificationUri = `${backendUrl}/admin/device?code=${userCode}`;
      
      // Store device flow data
      deviceCodes.set(deviceCode, {
        deviceCode,
        userCode,
        verificationUri,
        expiresAt,
        interval,
        status: 'pending'
      });

      logger.info(`Device authorization started: deviceCode=${deviceCode}, userCode=${userCode}`);

      return res.status(200).json({
        success: true,
        data: {
          device_code: deviceCode,
          user_code: userCode,
          verification_uri: verificationUri,
          verification_uri_complete: `${verificationUri}&auto=true`,
          expires_in: 600, // 10 minutes
          interval
        }
      });
    } catch (error) {
      logger.error('Device authorize error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to start device authorization',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Poll for device authorization completion
   * POST /api/auth/device/token
   */
  async deviceToken(req: Request, res: Response): Promise<Response> {
    try {
      const { device_code } = req.body;

      if (!device_code) {
        return res.status(400).json({
          success: false,
          error: 'invalid_request',
          error_description: 'Missing device_code parameter'
        });
      }

      const deviceData = deviceCodes.get(device_code);

      if (!deviceData) {
        return res.status(400).json({
          success: false,
          error: 'invalid_grant',
          error_description: 'Invalid device code'
        });
      }

      // Check if expired
      if (deviceData.expiresAt < new Date()) {
        deviceCodes.delete(device_code);
        return res.status(400).json({
          success: false,
          error: 'expired_token',
          error_description: 'Device code expired'
        });
      }

      // Check status
      if (deviceData.status === 'pending') {
        return res.status(400).json({
          success: false,
          error: 'authorization_pending',
          error_description: 'User has not completed authorization yet'
        });
      }

      if (deviceData.status === 'completed' && deviceData.accessToken) {
        // Clean up
        deviceCodes.delete(device_code);

        return res.status(200).json({
          success: true,
          access_token: deviceData.accessToken,
          token_type: 'bearer',
          expires_in: 86400 // 24 hours
        });
      }

      return res.status(400).json({
        success: false,
        error: 'authorization_pending',
        error_description: 'Authorization still pending'
      });

    } catch (error) {
      logger.error('Device token error:', error);
      return res.status(500).json({
        success: false,
        error: 'server_error',
        error_description: 'Internal server error'
      });
    }
  }

  /**
   * Complete device authorization (called by web frontend after Clerk auth)
   * POST /api/auth/device/complete
   */
  async deviceComplete(req: Request, res: Response): Promise<Response> {
    try {
      const { user_code, clerk_token } = req.body;

      // Find device by user code
      let deviceData: any = null;
      let deviceCode = '';
      
      for (const [code, data] of deviceCodes.entries()) {
        if (data.userCode === user_code) {
          deviceData = data;
          deviceCode = code;
          break;
        }
      }

      if (!deviceData || deviceData.expiresAt < new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Invalid or expired user code'
        });
      }

      // Validate Clerk JWT token
      let clerkUserId: string;
      try {
        const { verifyClerkToken } = await import('../../../middleware/clerk.middleware');
        const payload = await verifyClerkToken(clerk_token);
        
        if (!payload || !payload.sub) {
          return res.status(401).json({
            success: false,
            message: 'Invalid Clerk authentication token'
          });
        }
        
        clerkUserId = payload.sub;
      } catch (error) {
        logger.error('Clerk token validation failed:', error);
        return res.status(401).json({
          success: false,
          message: 'Failed to validate Clerk token'
        });
      }

      // Get user info from Clerk to determine role
      let userRole = 'USER';
      try {
        const clerkUser = await clerkService.getUserById(clerkUserId);
        const email = clerkUser.emailAddresses[0]?.emailAddress;
        
        if (email?.endsWith('@kapihq.com')) {
          userRole = 'ADMIN';
        }
        
        logger.info(`User ${email} authenticated with role: ${userRole}`);
      } catch (error) {
        logger.warn('Could not fetch user details from Clerk, defaulting to USER role:', error);
      }

      // Generate a proper JWT token for the client to use
      const jwt = require('jsonwebtoken');
      const clientToken = jwt.sign(
        { 
          sub: clerkUserId,
          role: userRole,
          iat: Math.floor(Date.now() / 1000),
          exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
          iss: 'kapi-backend'
        },
        process.env.JWT_SECRET || 'your-secret-key'
      );

      // Mark as completed
      deviceData.status = 'completed';
      deviceData.clerkUserId = clerkUserId;
      deviceData.accessToken = clientToken; // Store the client JWT token
      
      deviceCodes.set(deviceCode, deviceData);

      return res.status(200).json({
        success: true,
        message: 'Device authorization completed successfully',
        user: {
          id: clerkUserId,
          role: userRole
        }
      });

    } catch (error) {
      logger.error('Device complete error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to complete device authorization'
      });
    }
  }

  /**
   * Generate a user-friendly code for device authorization
   */
  private generateUserCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
      if (i === 4) result += '-';
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * Get the current user's profile
   */
  async getCurrentUser(req: Request, res: Response): Promise<Response> {
    try {
      const { userId } = req;

      // Validate that userId is present and valid
      if (!userId) {
        logger.error('Auth Controller: Missing userId in request');
        return res.status(401).json({
          success: false,
          message: 'Invalid authentication - missing user ID'
        });
      }

      // Check if this is a development bypass (userId = 1)
      if (process.env.NODE_ENV === 'development' && userId.toString() === '1') {
        logger.info('Auth Controller: Using development bypass user data');

        // Return mock user data for development
        return res.status(200).json({
          success: true,
          data: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Admin',
            lastName: 'User',
            username: 'admin',
            role: 'admin',
            email_verified: true,
            imageUrl: null,
          },
        });
      }

      // Get the user from Clerk for real authentication
      const user: ClerkSdkUser = await clerkService.getUserById(userId.toString());

      return res.status(200).json({
        success: true,
        data: {
          id: user.id,
          email: user.email_addresses[0]?.email_address ?? '',
          first_name: user.first_name,
          last_name: user.last_name,
          username: user.username,
          image_url: user.image_url,
          public_metadata: user.public_metadata,
        },
      });
    } catch (error) {
      logger.error('Error fetching current user:', error);
      return res.status(500).json({
        success: false,
        message: 'Error fetching user profile',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}

export const authController = new AuthController();
