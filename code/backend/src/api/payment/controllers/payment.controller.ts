/**
 * Payment controller for handling payment-related API requests
 */
import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import { PaymentService } from '../../../services/payment/payment.service';
import { SubscriptionService } from '../../../services/payment/subscription.service';
import { logger } from '../../../common/logger';
import {
  CreatePaymentDto,
  UpdatePaymentDto,
  CreateRefundDto,
  UpdateRefundDto,
  CreateSubscriptionDto,
  UpdateSubscriptionDto,
  CancelSubscriptionDto,
  CreateSubscriptionPaymentDto,
} from '../dto';
import { AuthenticatedRequest } from '../../../common/types';

/**
 * Controller for payment operations
 */
@injectable()
export class PaymentController {
  constructor(
    @inject(TYPES.PaymentService) private paymentService: PaymentService,
    @inject(TYPES.SubscriptionService) private subscriptionService: SubscriptionService,
  ) {}

  /**
   * Get payment by ID
   */
  async getPaymentById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const paymentId = parseInt(req.params.id, 10);
      if (isNaN(paymentId)) {
        res.status(400).json({ error: 'Invalid payment ID' });
        return;
      }

      const payment = await this.paymentService.getPaymentById(paymentId);
      if (!payment) {
        res.status(404).json({ error: 'Payment not found' });
        return;
      }

      // Check if user is authorized to view this payment
      if (payment.userId !== req.userId && req.userRole !== 'admin') {
        res.status(403).json({ error: 'Not authorized to view this payment' });
        return;
      }

      res.status(200).json(payment);
    } catch (error) {
      logger.error('Error getting payment by ID:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get payments for current user
   */
  async getUserPayments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.userId;
      const skip = req.query.skip ? parseInt(req.query.skip as string, 10) : 0;
      const take = req.query.take ? parseInt(req.query.take as string, 10) : 10;

      const payments = await this.paymentService.getPaymentsByUserId(userId, { skip, take });
      res.status(200).json(payments);
    } catch (error) {
      logger.error('Error getting user payments:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new payment
   */
  async createPayment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const paymentData: CreatePaymentDto = req.body;

      // Ensure user can only create payments for themselves unless they're an admin
      if (paymentData.userId !== req.userId && req.userRole !== 'admin') {
        res.status(403).json({ error: 'Not authorized to create payment for another user' });
        return;
      }

      const payment = await this.paymentService.createPayment(paymentData);
      res.status(201).json(payment);
    } catch (error) {
      logger.error('Error creating payment:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const paymentId = parseInt(req.params.id, 10);
      if (isNaN(paymentId)) {
        res.status(400).json({ error: 'Invalid payment ID' });
        return;
      }

      const { status } = req.body;
      if (!status) {
        res.status(400).json({ error: 'Status is required' });
        return;
      }

      // Only admins can update payment status
      if (req.userRole !== 'admin') {
        res.status(403).json({ error: 'Not authorized to update payment status' });
        return;
      }

      const payment = await this.paymentService.updatePaymentStatus(paymentId, status);
      if (!payment) {
        res.status(404).json({ error: 'Payment not found' });
        return;
      }

      res.status(200).json(payment);
    } catch (error) {
      logger.error('Error updating payment status:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Process a refund
   */
  async createRefund(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const refundData: CreateRefundDto = req.body;

      // Only admins can create refunds
      if (req.userRole !== 'admin') {
        res.status(403).json({ error: 'Not authorized to create refunds' });
        return;
      }

      // Set the admin user as the refunder
      refundData.refundedByUserId = req.userId;

      const refund = await this.paymentService.createRefund(refundData);
      if (!refund) {
        res.status(404).json({ error: 'Payment not found' });
        return;
      }

      res.status(201).json(refund);
    } catch (error) {
      logger.error('Error creating refund:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get active subscription for current user
   */
  async getUserActiveSubscription(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.userId;
      const subscription = await this.subscriptionService.getActiveSubscriptionByUserId(userId);

      if (!subscription) {
        res.status(404).json({ error: 'No active subscription found' });
        return;
      }

      res.status(200).json(subscription);
    } catch (error) {
      logger.error('Error getting user active subscription:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get all subscriptions for current user
   */
  async getUserSubscriptions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.userId;
      const subscriptions = await this.subscriptionService.getSubscriptionsByUserId(userId);
      res.status(200).json(subscriptions);
    } catch (error) {
      logger.error('Error getting user subscriptions:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new subscription
   */
  async createSubscription(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const subscriptionData: CreateSubscriptionDto = req.body;

      // Ensure user can only create subscriptions for themselves unless they're an admin
      if (subscriptionData.userId !== req.userId && req.userRole !== 'admin') {
        res.status(403).json({ error: 'Not authorized to create subscription for another user' });
        return;
      }

      const subscription = await this.subscriptionService.createSubscription(subscriptionData);
      res.status(201).json(subscription);
    } catch (error) {
      logger.error('Error creating subscription:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const subscriptionId = parseInt(req.params.id, 10);
      if (isNaN(subscriptionId)) {
        res.status(400).json({ error: 'Invalid subscription ID' });
        return;
      }

      const cancelData: CancelSubscriptionDto = req.body;

      // Get subscription to check ownership
      const subscription = await this.subscriptionService.getSubscriptionById(subscriptionId);
      if (!subscription) {
        res.status(404).json({ error: 'Subscription not found' });
        return;
      }

      // Ensure user can only cancel their own subscriptions unless they're an admin
      if (subscription.userId !== req.userId && req.userRole !== 'admin') {
        res.status(403).json({ error: 'Not authorized to cancel this subscription' });
        return;
      }

      const updatedSubscription = await this.subscriptionService.cancelSubscription(
        subscriptionId,
        cancelData,
      );
      res.status(200).json(updatedSubscription);
    } catch (error) {
      logger.error('Error canceling subscription:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
