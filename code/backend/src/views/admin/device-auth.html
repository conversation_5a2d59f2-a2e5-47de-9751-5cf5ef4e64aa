<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KAPI Terminal Authentication</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 3rem;
            border-radius: 12px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .device-code {
            background: #f8f9fa;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 1.5rem;
            font-size: 2rem;
            font-weight: bold;
            letter-spacing: 0.3em;
            color: #333;
            margin: 2rem 0;
            font-family: 'Courier New', monospace;
        }
        .instructions {
            color: #666;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 6px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: background 0.3s;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #5a67d8;
        }
        .status {
            margin-top: 2rem;
            padding: 1rem;
            border-radius: 6px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖 KAPI</div>
        <h1>Terminal Authentication</h1>
        
        <div class="instructions">
            <p>To authorize your KAPI terminal client, please confirm this device code matches the one shown in your terminal:</p>
        </div>
        
        <div class="device-code" id="deviceCode">
            Loading...
        </div>
        
        <div class="instructions">
            <p>Click the button below to authenticate with your KAPI account and complete the terminal setup.</p>
        </div>
        
        <button class="btn" id="authBtn">
            Authenticate Terminal
        </button>
        
        <div class="status" id="status"></div>
    </div>

    <script>
        // Get device code from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const deviceCode = urlParams.get('code');
        
        if (deviceCode) {
            document.getElementById('deviceCode').textContent = deviceCode;
        } else {
            document.getElementById('deviceCode').textContent = 'Invalid Code';
            showStatus('error', 'No device code provided in URL');
        }
        
        function showStatus(type, message) {
            const status = document.getElementById('status');
            status.className = `status ${type}`;
            status.textContent = message;
            status.style.display = 'block';
        }
        
        function showLoading(show) {
            const btn = document.getElementById('authBtn');
            if (show) {
                btn.innerHTML = '<span class="loading"></span>Authenticating...';
                btn.disabled = true;
            } else {
                btn.innerHTML = 'Authenticate Terminal';
                btn.disabled = false;
            }
        }
        
        async function authenticateDevice() {
            if (!deviceCode) {
                showStatus('error', 'No device code available');
                return;
            }
            
            showLoading(true);
            
            try {
                console.log('Starting Clerk authentication for device code:', deviceCode);
                
                // Get Clerk configuration from backend
                const configResponse = await fetch('/api/admin/auth/clerk-config');
                const config = await configResponse.json();
                
                // Use your specific Clerk instance URL (not the generic accounts.clerk.com)
                const clerkAuthUrl = `https://${config.clerkFrontendApi}/sign-in?redirect_url=${encodeURIComponent(window.location.origin + '/admin/device/callback?code=' + deviceCode)}`;
                
                // Store device code for after authentication
                sessionStorage.setItem('deviceCode', deviceCode);
                
                showStatus('success', 'Redirecting to authentication...');
                
                // Redirect to Clerk authentication
                window.location.href = clerkAuthUrl;
                
            } catch (error) {
                console.error('Authentication error:', error);
                showStatus('error', 'Authentication error: ' + error.message);
                showLoading(false);
            }
        }
        
        // Handle authentication callback (if needed)
        if (window.location.pathname === '/admin/device/callback') {
            handleAuthCallback();
        }
        
        async function handleAuthCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const deviceCode = urlParams.get('code') || sessionStorage.getItem('deviceCode');
            
            if (!deviceCode) {
                showStatus('error', 'Missing device code');
                return;
            }
            
            // Get Clerk token from URL or session
            // This is a simplified implementation - in reality, you'd get the JWT token from Clerk
            const clerkToken = urlParams.get('clerk_token') || 'CLERK_JWT_TOKEN_HERE';
            
            try {
                const response = await fetch('/api/admin/auth/device/complete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_code: deviceCode,
                        clerk_token: clerkToken
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('success', 'Terminal authenticated successfully! You can now return to your terminal.');
                    setTimeout(() => window.close(), 3000);
                } else {
                    showStatus('error', result.message || 'Authentication failed');
                }
            } catch (error) {
                showStatus('error', 'Authentication error: ' + error.message);
            }
        }
        
        // Add event listener to auth button
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('authBtn').addEventListener('click', authenticateDevice);
            
            // Auto-authenticate in development mode for faster testing
            if (window.location.search.includes('auto=true')) {
                setTimeout(authenticateDevice, 1000);
            }
        });
    </script>
</body>
</html>