/**
 * Unified Authentication Helper
 * Provides consistent authentication logic across HTTP requests and WebSocket connections
 */

import { logger } from '../common/logger';
import { clerkService } from '../services/clerk.service';
import { verifyClerkToken } from './clerk.middleware';

export interface AuthResult {
  success: boolean;
  userId?: number;
  role?: string;
  error?: string;
}

export interface AuthContext {
  token?: string;
  session_auth?: string;
  role?: string;
  user_id?: number;
}

/**
 * Validates authentication token and returns user information
 * Works for both HTTP requests and WebSocket connections
 */
export async function validateAuth(authContext: AuthContext): Promise<AuthResult> {
  try {
    // Check for admin session authentication
    if (authContext.session_auth && 
        authContext.token?.startsWith('admin-session-') && 
        authContext.role === 'ADMIN') {
      logger.info(`✅ Admin session authentication accepted`);
      return {
        success: true,
        userId: authContext.user_id || 1,
        role: 'ADMIN'
      };
    }
    
    // Check for IDE development token
    if (process.env.NODE_ENV === 'development' && 
        process.env.DEV_IDE_TOKEN && 
        authContext.token === process.env.DEV_IDE_TOKEN) {
      logger.info(`✅ Development IDE token accepted`);
      return {
        success: true,
        userId: parseInt(process.env.DEV_USER_ID || '1', 10),
        role: 'ADMIN' // Development users get admin role
      };
    }
    
    // Validate Clerk JWT token
    if (authContext.token && authContext.token.startsWith('eyJ')) {
      try {
        const clerkUserId = await validateClerkJWT(authContext.token);
        if (clerkUserId) {
          const userRole = await assignUserRoleByClerkId(clerkUserId);
          logger.info(`✅ Clerk JWT token validated for user ${clerkUserId}, role: ${userRole}`);
          return {
            success: true,
            userId: await getUserIdByClerkId(clerkUserId),
            role: userRole
          };
        }
      } catch (error) {
        logger.error('Clerk JWT validation failed:', error);
      }
    }
    
    // For production, validate Clerk JWT token here
    if (process.env.NODE_ENV === 'production' && authContext.token) {
      // TODO: Add Clerk JWT validation
      // const clerkUserId = await getClerkUserId(authContext.token);
      // if (clerkUserId) {
      //   return {
      //     success: true,
      //     userId: parseInt(clerkUserId, 10),
      //     role: 'USER'
      //   };
      // }
    }
    
    // Allow connections without auth for backward compatibility in development
    if (process.env.NODE_ENV === 'development' && (!authContext.token || authContext.token === '')) {
      logger.warn(`⚠️ No authentication provided, allowing for backward compatibility`);
      return {
        success: true,
        userId: 1, // Default to user ID 1 for development
        role: 'USER'
      };
    }
    
    return {
      success: false,
      error: 'Authentication failed'
    };
    
  } catch (error) {
    logger.error('Error validating authentication:', error);
    return {
      success: false,
      error: 'Authentication validation error'
    };
  }
}

/**
 * Extract authentication context from HTTP request headers
 */
export function extractHttpAuthContext(req: any): AuthContext {
  const authHeader = req.headers.authorization;
  const token = authHeader?.startsWith('Bearer ') ? authHeader.split(' ')[1] : undefined;
  
  return {
    token,
    session_auth: req.session?.admin_auth,
    role: req.session?.admin_user?.role,
    user_id: req.session?.admin_user?.id
  };
}

/**
 * Extract authentication context from WebSocket handshake
 */
export function extractSocketAuthContext(socket: any): AuthContext {
  const auth = socket.handshake.auth;
  
  return {
    token: auth?.token,
    session_auth: auth?.session_auth,
    role: auth?.role,
    user_id: auth?.user_id
  };
}

/**
 * Express middleware for unified authentication
 */
export function unifiedAuthMiddleware(req: any, res: any, next: any): void {
  const authContext = extractHttpAuthContext(req);
  
  validateAuth(authContext)
    .then((authResult) => {
      if (authResult.success) {
        // Set user info on request for use in route handlers
        req.user = {
          id: authResult.userId,
          role: authResult.role
        };
        req.userId = authResult.userId;
        req.userRole = authResult.role;
        next();
      } else {
        res.status(401).json({
          success: false,
          message: authResult.error || 'Authentication failed'
        });
      }
    })
    .catch((error) => {
      logger.error('Authentication middleware error:', error);
      res.status(500).json({
        success: false,
        message: 'Authentication validation error'
      });
    });
}

/**
 * Optional authentication middleware - doesn't fail if no token is provided
 */
export function optionalUnifiedAuthMiddleware(req: any, res: any, next: any): void {
  const authContext = extractHttpAuthContext(req);
  
  // If no auth provided, just continue
  if (!authContext.token && !authContext.session_auth) {
    next();
    return;
  }
  
  validateAuth(authContext)
    .then((authResult) => {
      if (authResult.success) {
        req.user = {
          id: authResult.userId,
          role: authResult.role
        };
        req.userId = authResult.userId;
        req.userRole = authResult.role;
      }
      next();
    })
    .catch((error) => {
      logger.error('Optional authentication middleware error:', error);
      next(); // Continue even if auth fails
    });
}

/**
 * Validate Clerk JWT token and return Clerk user ID
 */
async function validateClerkJWT(token: string): Promise<string | null> {
  try {
    const payload = await verifyClerkToken(token);
    return payload?.sub || null; // 'sub' contains the Clerk user ID
  } catch (error) {
    logger.error('Failed to validate Clerk JWT:', error);
    return null;
  }
}

/**
 * Assign user role based on Clerk user ID
 */
async function assignUserRoleByClerkId(clerkUserId: string): Promise<string> {
  try {
    const clerkUser = await clerkService.getUserById(clerkUserId);
    const email = clerkUser.emailAddresses[0]?.emailAddress;
    
    if (email?.endsWith('@kapihq.com')) {
      return 'ADMIN';
    }
    return 'USER';
  } catch (error) {
    logger.error('Failed to assign user role:', error);
    return 'USER'; // Default to USER role
  }
}

/**
 * Get local user ID by Clerk user ID (for database integration)
 */
async function getUserIdByClerkId(clerkUserId: string): Promise<number> {
  // For now, use a simple mapping
  // In production, this should query the local database
  return parseInt(clerkUserId.replace(/\D/g, '').slice(-6) || '1', 10);
}