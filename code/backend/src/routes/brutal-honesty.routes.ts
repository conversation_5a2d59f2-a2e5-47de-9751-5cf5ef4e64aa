import { Router } from 'express';
import { Request, Response } from 'express';
import { container } from '../inversify.config';
import { TYPES } from '../types';
import { BrutalHonestyMessagingService } from '../services/brutal-honesty-messaging.service';
import { CodeAnalysisService } from '../services/code-analysis.service';
import UnifiedConversationService from '../services/unified-conversation.service';
import fs from 'fs';
import path from 'path';

const router = Router();

/**
 * @swagger
 * /brutal-honesty/generate:
 *   post:
 *     summary: Generate brutal honesty report
 *     description: Generates a brutal honesty report for a project based on analysis results
 *     tags: [Brutal Honesty]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project directory
 *               analysisResult:
 *                 type: object
 *                 description: Analysis results from project analysis
 *     responses:
 *       200:
 *         description: Brutal honesty report generated successfully
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.post('/generate', async (req: Request, res: Response) => {
  try {
    const { projectPath, analysisResult } = req.body;

    if (!projectPath || !analysisResult) {
      return res.status(400).json({
        success: false,
        message: 'projectPath and analysisResult are required'
      });
    }

    const brutalHonestyService = container.get<BrutalHonestyMessagingService>(TYPES.BrutalHonestyMessagingService);
    
    console.log('📊 [BRUTAL-HONESTY] Analysis result structure:', {
      hasHealth: !!analysisResult.health,
      hasStructure: !!analysisResult.structure, 
      hasIssues: !!analysisResult.issues,
      hasFileMetrics: !!analysisResult.fileMetrics,
      keys: Object.keys(analysisResult)
    });
    
    // Extract the required data from analysis result
    const health = analysisResult.health || analysisResult;
    
    // Create file metrics from structure data if fileMetrics doesn't exist
    let fileMetrics = analysisResult.fileMetrics || [];
    if (fileMetrics.length === 0 && analysisResult.structure) {
      // Create synthetic file metrics from structure data
      const structure = analysisResult.structure;
      fileMetrics = [{
        filePath: 'project-summary',
        complexity: structure.avgComplexity || 1,
        nestingDepth: 1,
        functionCount: structure.totalFunctions || 0,
        lineCount: structure.totalLines || 0,
        hasErrorHandling: true,
        riskLevel: 'medium' as const,
        issues: analysisResult.issues || [],
        functions: []
      }];
    }
    
    console.log('📊 [BRUTAL-HONESTY] Extracted data:', {
      healthScore: health?.overallScore,
      complexityScore: health?.complexityScore,
      fileMetricsCount: fileMetrics.length,
      issuesCount: health?.issues?.length,
      structureKeys: analysisResult.structure ? Object.keys(analysisResult.structure) : [],
      healthKeys: health ? Object.keys(health) : []
    });

    // Ensure we have valid health report structure  
    const healthReport: any = {
      id: 0, // Temporary ID for analysis
      projectId: 0, // Temporary project ID
      overallScore: health?.overallScore || 50,
      complexityScore: health?.complexityScore || 50,
      maintainability: health?.maintainability || 50,
      technicalDebt: health?.technicalDebt || 50,
      fileCount: fileMetrics.length || 0,
      analysisTimeMs: 0, // Will be set by service if needed
      createdAt: new Date(),
      issues: health?.issues || [],
      metrics: {
        avgComplexity: health?.complexity?.average || health?.metrics?.avgComplexity || 0,
        highComplexityFiles: health?.metrics?.highComplexityFiles || 0,
        missingErrorHandling: health?.metrics?.missingErrorHandling || 0,
        totalFunctions: health?.metrics?.totalFunctions || analysisResult.structure?.totalFunctions || 0,
        totalLines: health?.metrics?.totalLines || analysisResult.structure?.totalLines || 0
      },
      gitAnalysis: analysisResult.gitAnalysis
    };

    // Generate the brutal honesty report
    console.log('📊 [BRUTAL-HONESTY] Calling service with:', {
      healthReportKeys: Object.keys(healthReport),
      fileMetricsCount: fileMetrics.length,
      healthReportMetrics: healthReport.metrics
    });
    
    let report;
    try {
      report = brutalHonestyService.generateBrutalHonestyReport(
        healthReport,
        fileMetrics,
        [] // Empty mermaid diagrams for now
      );
    } catch (serviceError) {
      console.error('📊 [BRUTAL-HONESTY] Service error:', serviceError);
      throw new Error(`Brutal honesty service failed: ${serviceError instanceof Error ? serviceError.message : String(serviceError)}`);
    }
    
    console.log('📊 [BRUTAL-HONESTY] Generated report:', {
      productionReadiness: report.productionReadiness,
      messagesCount: report.messages.length,
      hasDetailedMetrics: !!report.detailedMetrics
    });

    return res.json({
      success: true,
      data: report
    });

  } catch (error) {
    console.error('📊 [BRUTAL-HONESTY] Error generating brutal honesty report:', error);
    console.error('📊 [BRUTAL-HONESTY] Error stack:', error instanceof Error ? error.stack : 'No stack available');
    return res.status(500).json({
      success: false,
      message: 'Failed to generate brutal honesty report',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * @swagger
 * /project/save-brutal-honesty:
 *   post:
 *     summary: Save brutal honesty report to .kapi folder
 *     description: Saves the brutal honesty report to the project's .kapi folder
 *     tags: [Project]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project directory
 *               report:
 *                 type: object
 *                 description: The brutal honesty report to save
 *     responses:
 *       200:
 *         description: Report saved successfully
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.post('/save-brutal-honesty', async (req: Request, res: Response) => {
  try {
    const { projectPath, report } = req.body;

    if (!projectPath || !report) {
      return res.status(400).json({
        success: false,
        message: 'projectPath and report are required'
      });
    }

    // Create .kapi directory if it doesn't exist
    const kapiDir = path.join(projectPath, '.kapi');
    if (!fs.existsSync(kapiDir)) {
      fs.mkdirSync(kapiDir, { recursive: true });
    }

    // Save brutal honesty report as markdown with timestamp
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const reportMarkdown = generateBrutalHonestyMarkdown(report, path.basename(projectPath));
    const reportPath = path.join(kapiDir, `brutal-honesty-report-${timestamp}.md`);
    fs.writeFileSync(reportPath, reportMarkdown);

    // Also save as JSON with timestamp
    const jsonPath = path.join(kapiDir, `brutal-honesty-report-${timestamp}.json`);
    fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2));

    // Keep latest version for backwards compatibility
    const latestReportPath = path.join(kapiDir, 'brutal-honesty-report.md');
    const latestJsonPath = path.join(kapiDir, 'brutal-honesty-report.json');
    fs.writeFileSync(latestReportPath, reportMarkdown);
    fs.writeFileSync(latestJsonPath, JSON.stringify(report, null, 2));

    // Save metrics with timestamp
    const metricsPath = path.join(kapiDir, `metrics-${timestamp}.json`);
    const latestMetricsPath = path.join(kapiDir, 'metrics.json');
    const metrics = {
      timestamp: new Date().toISOString(),
      scores: {
        overall: Math.round(report.productionReadiness),
        complexity: 100 - (report.messages.find((m: any) => m.category === 'complexity')?.readinessImpact || 0),
        maintainability: 100 - (report.messages.find((m: any) => m.category === 'quality')?.readinessImpact || 0),
        technicalDebt: Math.round(100 - report.productionReadiness)
      },
      metrics: {
        avgComplexity: report.funFacts?.find((f: string) => f.includes('complexity'))?.match(/\d+/)?.[0] || 0,
        highComplexityFiles: 0,
        missingErrorHandling: report.messages.filter((m: any) => m.category === 'error_handling').length,
        totalFunctions: report.funFacts?.find((f: string) => f.includes('functions'))?.match(/\d+/)?.[0] || 0,
        totalLines: report.funFacts?.find((f: string) => f.includes('lines'))?.match(/\d+/)?.[0] || 0
      },
      fileCount: 1,
      analysisTimeMs: 300
    };
    fs.writeFileSync(metricsPath, JSON.stringify(metrics, null, 2));
    fs.writeFileSync(latestMetricsPath, JSON.stringify(metrics, null, 2));

    return res.json({
      success: true,
      message: 'Brutal honesty report saved successfully',
      files: {
        markdown: reportPath,
        json: jsonPath,
        metrics: metricsPath
      }
    });

  } catch (error) {
    console.error('Error saving brutal honesty report:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to save brutal honesty report',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to generate markdown report
function generateBrutalHonestyMarkdown(report: any, projectName: string): string {
  const date = new Date().toLocaleString();
  
  let markdown = `# 🔥 Brutal Honesty Report

**Project:** ${projectName}  
**Generated:** ${date}  
**Production Readiness:** ${Math.round(report.productionReadiness)}%  
**Overall Grade:** ${report.overallGrade}

## 📊 Summary

Your project has a production readiness score of **${Math.round(report.productionReadiness)}%** with an overall grade of **${report.overallGrade}**.

## 💥 The Brutal Truth

`;

  // Add messages by category
  const categories = ['security', 'performance', 'quality', 'documentation', 'error_handling', 'complexity'];
  
  for (const category of categories) {
    const messages = report.messages.filter((m: any) => m.category === category);
    if (messages.length > 0) {
      const message = messages[0];
      const categoryEmoji = {
        security: '🔒',
        performance: '⚡',
        quality: '✨',
        documentation: '📚',
        error_handling: '🚨',
        complexity: '🧠'
      }[category] || '📋';
      
      markdown += `### ${categoryEmoji} ${category.toUpperCase().replace('_', ' ')} (Grade: ${message.grade})

**The Reality Check:**
> ${message.brutalmessage}

**How to Fix It:**
${message.helpfulGuidance}

`;
    }
  }

  markdown += `## 🎯 Your Path to Production

**Estimated Time to Fix:** ${report.timeToFix}

`;

  if (report.nextSteps && report.nextSteps.length > 0) {
    for (let i = 0; i < report.nextSteps.length; i++) {
      markdown += `${i + 1}. ${report.nextSteps[i]}\n`;
    }
  }

  if (report.funFacts && report.funFacts.length > 0) {
    markdown += `
## 🎉 Fun Facts

`;
    for (const fact of report.funFacts) {
      markdown += `- ${fact}\n`;
    }
  }

  markdown += `
## 💪 Encouragement

${report.encouragement}

---

*This report was generated by KAPI's Brutal Honesty Analysis System*
`;

  return markdown;
}

/**
 * @swagger
 * /project/progressive-analysis:
 *   post:
 *     summary: Generate progressive improvement analysis
 *     description: Analyzes historical .kapi reports to provide improvement insights
 *     tags: [Progressive Improvement]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project directory
 *               userId:
 *                 type: number
 *                 description: User ID for context
 *     responses:
 *       200:
 *         description: Progressive improvement analysis generated
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Server error
 */
router.post('/progressive-analysis', async (req: Request, res: Response) => {
  try {
    const { projectPath, userId } = req.body;

    if (!projectPath) {
      return res.status(400).json({
        success: false,
        message: 'projectPath is required'
      });
    }

    const kapiDir = path.join(projectPath, '.kapi');
    if (!fs.existsSync(kapiDir)) {
      return res.status(404).json({
        success: false,
        message: 'No .kapi directory found - project not analyzed yet'
      });
    }

    // Parse historical reports
    const historicalData = parseHistoricalReports(kapiDir);
    
    if (historicalData.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No historical analysis reports found'
      });
    }

    // Use Unified Conversation Service for analysis
    const conversationService = container.get<typeof UnifiedConversationService>(TYPES.UnifiedConversationService);
    
    // Create a conversation for progressive improvement analysis
    const conversation = await conversationService.createConversation(
      userId || 1,
      'Progressive Improvement Analysis',
      'progressive-improvement',
      buildProgressiveAnalysisPrompt(historicalData)
    });

    // Get the AI analysis
    const message = await conversationService.addMessage(
      conversation.id,
      buildProgressiveAnalysisPrompt(historicalData),
      'user'
    );

    // Extract insights from the response
    const insights = parseProgressiveInsights(message.content);

    return res.json({
      success: true,
      data: {
        conversationId: conversation.id,
        insights: insights,
        historicalData: historicalData,
        trends: analyzeTrends(historicalData),
        recommendations: generateRecommendations(historicalData)
      }
    });

  } catch (error) {
    console.error('Error generating progressive analysis:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to generate progressive analysis',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Helper function to parse historical reports from .kapi folder
function parseHistoricalReports(kapiDir: string): any[] {
  const historicalData: any[] = [];
  
  try {
    const files = fs.readdirSync(kapiDir);
    
    // Find all timestamped report files
    const reportFiles = files.filter(file => 
      file.match(/^brutal-honesty-report-\d{4}-\d{2}-\d{2}\.json$/) ||
      file.match(/^metrics-\d{4}-\d{2}-\d{2}\.json$/)
    );

    // Group by date
    const dateMap = new Map<string, any>();
    
    for (const file of reportFiles) {
      const dateMatch = file.match(/(\d{4}-\d{2}-\d{2})/);
      if (dateMatch) {
        const date = dateMatch[1];
        
        if (!dateMap.has(date)) {
          dateMap.set(date, { date, timestamp: new Date(date).toISOString() });
        }
        
        const filePath = path.join(kapiDir, file);
        const content = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        
        if (file.includes('brutal-honesty-report')) {
          dateMap.get(date)!.brutalHonestyReport = content;
        } else if (file.includes('metrics')) {
          dateMap.get(date)!.metrics = content;
        }
      }
    }

    // Convert to array and sort by date
    for (const entry of dateMap.values()) {
      if (entry.brutalHonestyReport || entry.metrics) {
        historicalData.push(entry);
      }
    }

    historicalData.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
  } catch (error) {
    console.error('Error parsing historical reports:', error);
  }
  
  return historicalData;
}

// Helper function to build prompt for progressive analysis
function buildProgressiveAnalysisPrompt(historicalData: any[]): string {
  const dataString = JSON.stringify(historicalData, null, 2);
  
  return `You are KAPI's Progressive Improvement Analyst. Analyze this project's historical improvement data and provide insights.

Historical Analysis Data:
${dataString}

Please analyze this data and provide:

1. **Improvement Trends**: What's getting better over time?
2. **Areas of Concern**: What's getting worse or plateauing?
3. **Achievement Recognition**: Celebrate wins and progress
4. **Smart Next Steps**: Prioritized recommendations for improvement

Respond in a friendly, encouraging tone that motivates continued improvement. Focus on actionable insights.`;
}

// Helper function to parse AI insights
function parseProgressiveInsights(aiResponse: string): any {
  // Simple parsing - could be enhanced with more sophisticated NLP
  return {
    summary: aiResponse.substring(0, 500) + '...',
    fullAnalysis: aiResponse,
    generatedAt: new Date().toISOString()
  };
}

// Helper function to analyze trends from historical data
function analyzeTrends(historicalData: any[]): any {
  if (historicalData.length < 2) {
    return { message: 'Need more data points to analyze trends' };
  }

  const first = historicalData[0];
  const latest = historicalData[historicalData.length - 1];
  
  const trends = {
    productionReadiness: {
      change: (latest.brutalHonestyReport?.productionReadiness || 0) - (first.brutalHonestyReport?.productionReadiness || 0),
      direction: 'stable'
    },
    overallScore: {
      change: (latest.metrics?.scores?.overall || 0) - (first.metrics?.scores?.overall || 0),
      direction: 'stable'
    }
  };

  // Determine direction
  if (trends.productionReadiness.change > 5) trends.productionReadiness.direction = 'improving';
  else if (trends.productionReadiness.change < -5) trends.productionReadiness.direction = 'declining';

  if (trends.overallScore.change > 5) trends.overallScore.direction = 'improving';
  else if (trends.overallScore.change < -5) trends.overallScore.direction = 'declining';

  return trends;
}

// Helper function to generate recommendations
function generateRecommendations(historicalData: any[]): string[] {
  const recommendations = [];
  
  if (historicalData.length > 0) {
    const latest = historicalData[historicalData.length - 1];
    
    if (latest.brutalHonestyReport?.messages) {
      for (const message of latest.brutalHonestyReport.messages) {
        if (message.grade === 'F' || message.grade === 'D') {
          recommendations.push(`Improve ${message.category}: ${message.helpfulGuidance}`);
        }
      }
    }
  }
  
  if (recommendations.length === 0) {
    recommendations.push('Keep up the great work! Continue monitoring your progress.');
  }
  
  return recommendations.slice(0, 3); // Limit to top 3
}

export default router;