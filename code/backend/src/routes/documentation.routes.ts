import { Router } from 'express';
import { generateDocumentation, generateDocumentationSelective } from '../services/documentation.service';
import { chromaDBService } from '../services/chroma-db.service';
import { DocumentationIndexerService } from '../services/documentation-indexer.service';

const router = Router();
const documentationIndexer = new DocumentationIndexerService();

/**
 * @swagger
 * /documentation/generate:
 *   post:
 *     summary: Generate documentation for selected files or entire project
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: The absolute path to the project directory
 *               selectedFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of specific file paths to document
 *               directoryPath:
 *                 type: string
 *                 description: Path to directory for batch documentation
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, critical]
 *                 description: Documentation priority level
 *               includeSubdirectories:
 *                 type: boolean
 *                 description: Include subdirectories when documenting a directory
 *     responses:
 *       202:
 *         description: Documentation generation started
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Error generating documentation
 */
router.post('/generate', async (req, res) => {
  try {
    const {
      projectPath,
      selectedFiles,
      directoryPath,
      priority = 'normal',
      includeSubdirectories = true
    } = req.body;

    if (!projectPath) {
      return res.status(400).json({ error: 'projectPath is required' });
    }

    // Validate that at least one target is specified
    if (!selectedFiles && !directoryPath) {
      return res.status(400).json({
        error: 'Either selectedFiles or directoryPath must be specified'
      });
    }

    // We run this in the background and immediately return a response
    generateDocumentationSelective({
      projectPath,
      selectedFiles,
      directoryPath,
      priority,
      includeSubdirectories
    }).catch(error => {
        console.error('Error during background documentation generation:', error);
    });

    res.status(202).json({
      message: 'Documentation generation started in the background',
      targets: {
        selectedFiles: selectedFiles?.length || 0,
        directoryPath: directoryPath || null,
        priority,
        includeSubdirectories
      }
    });
  } catch (error) {
    console.error('Error starting documentation generation:', error);
    res.status(500).json({ error: 'Error starting documentation generation', details: error instanceof Error ? error instanceof Error ? error.message : String(error) : String(error) });
  }
});

/**
 * @swagger
 * /documentation/readme:
 *   post:
 *     summary: Generate README.md for a specific directory
 *     description: Generates a README.md file for the specified directory using the documentation script
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - directoryPath
 *             properties:
 *               directoryPath:
 *                 type: string
 *                 description: Absolute path to the directory for README generation
 *     responses:
 *       200:
 *         description: README generation completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 readmePath:
 *                   type: string
 *                   description: Path to the generated README.md file
 *       400:
 *         description: Invalid request parameters
 *       500:
 *         description: Error generating README
 */
router.post('/readme', async (req, res) => {
  try {
    const { directoryPath } = req.body;

    if (!directoryPath) {
      return res.status(400).json({ error: 'directoryPath is required' });
    }

    // Validate that the directory exists
    const fs = require('fs');
    if (!fs.existsSync(directoryPath) || !fs.statSync(directoryPath).isDirectory()) {
      return res.status(400).json({ error: 'Invalid directory path' });
    }

    console.log(`Generating README for directory: ${directoryPath}`);

    // Import the function to generate README
    const { generateReadmeForDirectory } = require('../services/documentation.service');

    const readmePath = await generateReadmeForDirectory(directoryPath);

    if (readmePath) {
      res.status(200).json({
        message: 'README generated successfully',
        readmePath: readmePath
      });
    } else {
      res.status(500).json({ error: 'Failed to generate README' });
    }
  } catch (error) {
    console.error('Error generating README:', error);
    res.status(500).json({ error: 'Error generating README', details: error instanceof Error ? error.message : String(error) });
  }
});

/**
 * @swagger
 * /documentation/search:
 *   post:
 *     summary: Search documentation using semantic search
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *                 description: Natural language search query
 *               projectPath:
 *                 type: string
 *                 description: Project path to filter results (optional)
 *               limit:
 *                 type: integer
 *                 description: Maximum number of results (default 10)
 *               threshold:
 *                 type: number
 *                 description: Minimum similarity threshold (default 0.7)
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       content:
 *                         type: string
 *                       metadata:
 *                         type: object
 *                       similarity:
 *                         type: number
 *       400:
 *         description: Query is required
 *       500:
 *         description: Search failed
 */
router.post('/search', async (req, res) => {
  try {
    const { query, projectPath, limit = 10, threshold = 0.7 } = req.body;

    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      return res.status(400).json({ error: 'Query is required and must be a non-empty string' });
    }

    // Build filters based on projectPath if provided
    const filters: Record<string, any> = {};
    // Note: ChromaDB doesn't support prefix matching in where clauses
    // We'll filter after getting results instead

    // Perform semantic search without path filtering first
    const searchResults = await chromaDBService.semanticSearch(query, limit, filters);

    // Transform results to include similarity score
    let results = searchResults.map((result: any) => ({
      id: result.id,
      content: result.content,
      metadata: result.metadata,
      similarity: Math.max(0, 1 - result.distance / 2) // Convert distance to similarity (0-1 range, max distance = 2)
    })).filter((result: any) => result.similarity >= threshold);

    // Apply projectPath filtering after semantic search if provided
    if (projectPath) {
      results = results.filter((result: any) => {
        const sourceFile = result.metadata?.sourceFile || '';
        return sourceFile.startsWith(projectPath);
      });
    }

    res.json({
      query,
      results,
      total: results.length,
      limit,
      threshold
    });
  } catch (error) {
    console.error('Error performing semantic search:', error);
    res.status(500).json({ error: 'Search failed', details: error instanceof Error ? error.message : String(error) });
  }
});

/**
 * @swagger
 * /documentation/index:
 *   post:
 *     summary: Index documentation files for semantic search
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project to index
 *               force:
 *                 type: boolean
 *                 description: Force reindexing of all documents
 *     responses:
 *       202:
 *         description: Indexing started
 *       400:
 *         description: projectPath is required
 *       500:
 *         description: Indexing failed
 */
router.post('/index', async (req, res) => {
  try {
    const { projectPath, force = false } = req.body;

    if (!projectPath) {
      return res.status(400).json({ error: 'projectPath is required' });
    }

    // Run indexing in background
    documentationIndexer.indexProjectDocumentation(projectPath, force).catch(error => {
      console.error('Error during background documentation indexing:', error);
    });

    res.status(202).json({
      message: 'Documentation indexing started in the background',
      projectPath,
      force
    });
  } catch (error) {
    console.error('Error starting documentation indexing:', error);
    res.status(500).json({ error: 'Indexing failed', details: error instanceof Error ? error.message : String(error) });
  }
});

/**
 * @swagger
 * /documentation/health:
 *   get:
 *     summary: Check health of documentation search service
 *     tags: [Documentation]
 *     responses:
 *       200:
 *         description: Service is healthy
 *       503:
 *         description: Service is unhealthy
 */
/**
 * @swagger
 * /documentation/files:
 *   post:
 *     summary: Get file information for documentation selection
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               directoryPath:
 *                 type: string
 *                 description: Path to directory to scan
 *               includeSubdirectories:
 *                 type: boolean
 *                 description: Include subdirectories in scan
 *     responses:
 *       200:
 *         description: File information retrieved
 *       400:
 *         description: directoryPath is required
 *       500:
 *         description: Error scanning directory
 */
router.post('/files', async (req, res) => {
  try {
    const { directoryPath, includeSubdirectories = true } = req.body;

    if (!directoryPath) {
      return res.status(400).json({ error: 'directoryPath is required' });
    }

    // Import the function here to avoid circular dependencies
    const { getFilesFromDirectory, getFileImportance } = require('../services/documentation.service');

    const files = getFilesFromDirectory(directoryPath, includeSubdirectories);

    const fileInfo = files.map(filePath => ({
      path: filePath,
      name: require('path').basename(filePath),
      relativePath: require('path').relative(directoryPath, filePath),
      importance: getFileImportance(filePath),
      size: require('fs').statSync(filePath).size,
      lastModified: require('fs').statSync(filePath).mtime
    }));

    // Sort by importance and name
    fileInfo.sort((a, b) => {
      const importanceOrder = { critical: 4, high: 3, normal: 2, low: 1 };
      const importanceDiff = importanceOrder[b.importance] - importanceOrder[a.importance];

      if (importanceDiff !== 0) return importanceDiff;

      return a.name.localeCompare(b.name);
    });

    res.json({
      directory: directoryPath,
      includeSubdirectories,
      totalFiles: fileInfo.length,
      files: fileInfo,
      summary: {
        critical: fileInfo.filter(f => f.importance === 'critical').length,
        high: fileInfo.filter(f => f.importance === 'high').length,
        normal: fileInfo.filter(f => f.importance === 'normal').length,
        low: fileInfo.filter(f => f.importance === 'low').length
      }
    });
  } catch (error) {
    console.error('Error scanning directory:', error);
    res.status(500).json({ error: 'Error scanning directory', details: error instanceof Error ? error.message : String(error) });
  }
});

/**
 * @swagger
 * /documentation/commit/{commitHash}:
 *   post:
 *     summary: Index documentation for a specific commit
 *     tags: [Documentation]
 *     parameters:
 *       - in: path
 *         name: commitHash
 *         required: true
 *         schema:
 *           type: string
 *         description: Git commit hash
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               projectPath:
 *                 type: string
 *                 description: Path to the project
 *     responses:
 *       202:
 *         description: Commit documentation indexing started
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Indexing failed
 */
router.post('/commit/:commitHash', async (req, res) => {
  try {
    const { commitHash } = req.params;
    const { projectPath } = req.body;

    if (!commitHash || !projectPath) {
      return res.status(400).json({
        error: 'commitHash and projectPath are required'
      });
    }

    // Run commit indexing in background
    documentationIndexer.indexCommitDocumentation(commitHash, projectPath).catch(error => {
      console.error('Error during background commit documentation indexing:', error);
    });

    res.status(202).json({
      message: 'Commit documentation indexing started in the background',
      commitHash,
      projectPath
    });
  } catch (error) {
    console.error('Error starting commit documentation indexing:', error);
    res.status(500).json({
      error: 'Commit indexing failed',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

/**
 * @swagger
 * /documentation/changes:
 *   post:
 *     summary: Get documentation changes between commits
 *     tags: [Documentation]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fromCommit:
 *                 type: string
 *                 description: Starting commit hash
 *               toCommit:
 *                 type: string
 *                 description: Ending commit hash (defaults to HEAD)
 *               projectPath:
 *                 type: string
 *                 description: Path to the project
 *     responses:
 *       200:
 *         description: Documentation changes retrieved
 *       400:
 *         description: Invalid parameters
 *       500:
 *         description: Failed to get changes
 */
router.post('/changes', async (req, res) => {
  try {
    const { fromCommit, toCommit = 'HEAD', projectPath } = req.body;

    if (!fromCommit || !projectPath) {
      return res.status(400).json({
        error: 'fromCommit and projectPath are required'
      });
    }

    const changes = await documentationIndexer.getDocumentationChanges(
      fromCommit,
      toCommit,
      projectPath
    );

    res.json({
      fromCommit,
      toCommit,
      projectPath,
      changes,
      total: changes.length
    });
  } catch (error) {
    console.error('Error getting documentation changes:', error);
    res.status(500).json({
      error: 'Failed to get documentation changes',
      details: error instanceof Error ? error.message : String(error)
    });
  }
});

router.get('/health', async (req, res) => {
  try {
    const isHealthy = await chromaDBService.healthCheck();

    if (isHealthy) {
      res.json({
        status: 'healthy',
        service: 'documentation-search',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(503).json({
        status: 'unhealthy',
        service: 'documentation-search',
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      service: 'documentation-search',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
