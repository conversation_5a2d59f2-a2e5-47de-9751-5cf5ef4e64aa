import { Router } from 'express';
import { unifiedAuthMiddleware } from '../../middleware/unified-auth';
import { authController } from '../../api/auth/controllers';
import { authRateLimiter } from '../../common/middleware/security.middleware';

const router = Router();

/**
 * Device flow authentication endpoints for CLI clients
 */

/**
 * Start device authorization flow
 * Endpoint: POST /api/auth/device/authorize
 */
router.post('/device/authorize', authRateLimiter, (req, res) => authController.deviceAuthorize(req, res));

/**
 * Poll for device authorization completion
 * Endpoint: POST /api/auth/device/token
 */
router.post('/device/token', authRateLimiter, (req, res) => authController.deviceToken(req, res));

/**
 * Complete device authorization (called by frontend after Clerk auth)
 * Endpoint: POST /api/auth/device/complete
 */
router.post('/device/complete', authRateLimiter, (req, res) => authController.deviceComplete(req, res));

/**
 * Get the current user's profile
 * Endpoint: GET /api/auth/me
 */
router.get('/me', authRateLimiter, unifiedAuthMiddleware, (req, res) => authController.getCurrentUser(req, res));

export default router;
