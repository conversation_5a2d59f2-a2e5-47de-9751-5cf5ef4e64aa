// Use declaration merging to add custom properties to the Express Request object.
// This is safer than extending the interface in a way that can cause conflicts.
import { Request, Response, NextFunction } from 'express';

// Interface for a Clerk user object from the Clerk SDK
export interface ClerkSdkUser {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email_addresses: { email_address: string }[];
  username: string | null;
  image_url: string;
  public_metadata: Record<string, unknown>;
  private_metadata: Record<string, unknown>;
  unsafe_metadata: Record<string, unknown>;
}

// Interface for a user object from JWT authentication
export interface JwtUser {
  id: number;
  email: string;
  username?: string;
  role?: string;
}

// Use declaration merging to add custom properties to the Express Request object.
declare global {
  namespace Express {
    interface Request {
      user?: ClerkSdkUser | JwtUser; // User from either Clerk or JWT
      userId?: number; // Normalized to number for consistency
      userRole?: string;
      rawBody?: string; // For webhooks
    }
  }
}

// Define a custom middleware type that works with async functions
export type AsyncRequestHandler = (
  req: Request,
  res: Response,
  next: NextFunction,
) => Promise<void> | void;

// Authenticated request interface for controllers
export interface AuthenticatedRequest extends Request {
  userId: number;
  user?: ClerkSdkUser | JwtUser;
  userRole?: string;
}

// User creation interface
export interface UserCreate {
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}

// User update interface
export interface UserUpdate {
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
}