/**
 * Inversify dependency injection container configuration
 */
import 'reflect-metadata';
import { Container } from 'inversify';
import { TYPES } from './types';

// Database services
import { DatabaseService } from './db/database.service';
import { PrismaClient } from '@prisma/client';

// Repositories
import {
  BaseRepository,
  UserRepository,
  ProjectRepository,
  ConversationRepository,
  AIAgentRepository,
  AIPromptTemplateRepository,
  WorkshopRepository,
  ModuleRepository,
  EloHistoryRepository,
  KarmaRepository,
  TemplateRepository,
  TemplateFileRepository,
  TemplateVariableRepository,
  TemplateCollectionRepository,
  ProjectTemplateRepository,
  PaymentRepository,
  SubscriptionRepository,
  QuestionRepository,
  AnswerRepository,
  TagRepository,
  ChannelRepository,
  SocialMessageRepository,
  NotificationRepository,
} from './db/repositories';
import { ConcreteBaseRepository } from './db/repositories/concrete-base.repository';

// Controllers
import { AIAgentController } from './api/ai/controllers/ai-agent.controller';
import { WorkshopController } from './api/workshop/controllers/workshop.controller';
import { TemplateController } from './api/template/template.controller';
import { PaymentController } from './api/payment/controllers';
import { QAController } from './api/qa/controllers';
import { SocialController } from './api/social/controllers';
import { AgentController } from './api/agent/controllers/agent.controller';
import { CodeAnalysisController } from './api/code-analysis/code-analysis.controller';
import { DocumentationConsistencyController } from './api/documentation-consistency/documentation-consistency.controller';

// Services
import { TemplateService } from './services/template';
import { PaymentService, SubscriptionService } from './services/payment';
import { EnhancedQAService } from './services/qa';
import { ChannelService, MessageService, NotificationService } from './services/social/split';
import { VectorService } from './services/vector.service';
import { DocumentProcessingService } from './services/document-processing.service';

// Agent Services
import { AgentService } from './services/agent/agent.service';
import { AgentOrchestrator } from './services/agent-orchestrator.service';
import { ShellService } from './services/agent/shell.service';
import { FileService } from './services/agent/file.service';
import { GitService } from './services/agent/git.service';
import { PipelineService } from './services/agent/pipeline.service';

// Code Analysis Services
import { CodeAnalysisService } from './services/code-analysis.service';
import UnifiedConversationService from './services/unified-conversation.service';
import ProjectService from './services/project.service';
import { DocumentationConsistencyService } from './services/documentation-consistency.service';
import { BrutalHonestyMessagingService } from './services/brutal-honesty-messaging.service';
import { MermaidDiagramService } from './services/mermaid-diagram.service';
import { KapiReportGeneratorService } from './services/kapi-report-generator.service';

// Create and configure container
const container = new Container();

// Register database services
container.bind<DatabaseService>(TYPES.DatabaseService).to(DatabaseService).inSingletonScope();
container.bind<PrismaClient>(TYPES.PrismaService).toConstantValue(new PrismaClient());

// Register repositories
container.bind<BaseRepository<any>>(TYPES.BaseRepository).to(ConcreteBaseRepository);
container.bind<UserRepository>(TYPES.UserRepository).to(UserRepository);
container.bind<ProjectRepository>(TYPES.ProjectRepository).to(ProjectRepository);
container.bind<ConversationRepository>(TYPES.ConversationRepository).to(ConversationRepository);

// Register AI repositories
container.bind<AIAgentRepository>(TYPES.AIAgentRepository).to(AIAgentRepository);
container
  .bind<AIPromptTemplateRepository>(TYPES.AIPromptTemplateRepository)
  .to(AIPromptTemplateRepository);

// Register Workshop repositories
container.bind<WorkshopRepository>(TYPES.WorkshopRepository).to(WorkshopRepository);
container.bind<ModuleRepository>(TYPES.ModuleRepository).to(ModuleRepository);

// Register Gamification repositories
container.bind<EloHistoryRepository>(TYPES.EloHistoryRepository).to(EloHistoryRepository);
container.bind<KarmaRepository>(TYPES.KarmaRepository).to(KarmaRepository);

// Register Template repositories
container.bind<TemplateRepository>(TYPES.TemplateRepository).to(TemplateRepository);
container.bind<TemplateFileRepository>(TYPES.TemplateFileRepository).to(TemplateFileRepository);
container
  .bind<TemplateVariableRepository>(TYPES.TemplateVariableRepository)
  .to(TemplateVariableRepository);
container
  .bind<TemplateCollectionRepository>(TYPES.TemplateCollectionRepository)
  .to(TemplateCollectionRepository);
container
  .bind<ProjectTemplateRepository>(TYPES.ProjectTemplateRepository)
  .to(ProjectTemplateRepository);

// Register Payment repositories
container.bind<PaymentRepository>(TYPES.PaymentRepository).to(PaymentRepository);
container.bind<SubscriptionRepository>(TYPES.SubscriptionRepository).to(SubscriptionRepository);

// Register QA repositories
container.bind<QuestionRepository>(TYPES.QuestionRepository).to(QuestionRepository);
container.bind<AnswerRepository>(TYPES.AnswerRepository).to(AnswerRepository);
container.bind<TagRepository>(TYPES.TagRepository).to(TagRepository);

// Register Social repositories
container.bind<ChannelRepository>(TYPES.ChannelRepository).to(ChannelRepository);
container.bind<SocialMessageRepository>(TYPES.SocialMessageRepository).to(SocialMessageRepository);
container.bind<NotificationRepository>(TYPES.NotificationRepository).to(NotificationRepository);

// Register controllers
container.bind<AIAgentController>(TYPES.AIAgentController).to(AIAgentController);
container.bind<WorkshopController>(TYPES.WorkshopController).to(WorkshopController);
container.bind<TemplateController>(TYPES.TemplateController).to(TemplateController);
container.bind<PaymentController>(TYPES.PaymentController).to(PaymentController);
container.bind<QAController>(TYPES.QAController).to(QAController);
container.bind<SocialController>(TYPES.SocialController).to(SocialController);
container.bind<AgentController>(TYPES.AgentController).to(AgentController);
container.bind<CodeAnalysisController>(TYPES.CodeAnalysisController).to(CodeAnalysisController);
container.bind<DocumentationConsistencyController>(TYPES.DocumentationConsistencyController).to(DocumentationConsistencyController);

// Register services
container.bind<TemplateService>(TYPES.TemplateService).to(TemplateService);
container.bind<PaymentService>(TYPES.PaymentService).to(PaymentService);
container.bind<SubscriptionService>(TYPES.SubscriptionService).to(SubscriptionService);
container.bind<EnhancedQAService>(TYPES.EnhancedQAService).to(EnhancedQAService);
container.bind<ChannelService>(TYPES.ChannelService).to(ChannelService);
container.bind<MessageService>(TYPES.MessageService).to(MessageService);
container.bind<NotificationService>(TYPES.NotificationService).to(NotificationService);
container.bind<VectorService>(TYPES.VectorService).to(VectorService).inSingletonScope();
container.bind<DocumentProcessingService>(TYPES.DocumentProcessingService).to(DocumentProcessingService).inSingletonScope();

// Register agent services
container.bind<AgentService>(TYPES.AgentService).to(AgentService).inSingletonScope();
container.bind<AgentOrchestrator>(TYPES.AgentOrchestrator).to(AgentOrchestrator).inSingletonScope();
container.bind<ShellService>(TYPES.ShellService).to(ShellService).inSingletonScope();
container.bind<FileService>(TYPES.FileService).to(FileService).inSingletonScope();
container.bind<GitService>(TYPES.GitService).to(GitService).inSingletonScope();
container.bind<PipelineService>(TYPES.PipelineService).to(PipelineService).inSingletonScope();

// Register code analysis services
container.bind<CodeAnalysisService>(TYPES.CodeAnalysisService).to(CodeAnalysisService);
container.bind<typeof UnifiedConversationService>(TYPES.UnifiedConversationService).toConstantValue(UnifiedConversationService);
container.bind<typeof ProjectService>(TYPES.ProjectService).toConstantValue(ProjectService);
container.bind<DocumentationConsistencyService>(TYPES.DocumentationConsistencyService).to(DocumentationConsistencyService);
container.bind<BrutalHonestyMessagingService>(TYPES.BrutalHonestyMessagingService).to(BrutalHonestyMessagingService);
container.bind<MermaidDiagramService>(TYPES.MermaidDiagramService).to(MermaidDiagramService);
container.bind<KapiReportGeneratorService>(TYPES.KapiReportGeneratorService).to(KapiReportGeneratorService);

export { container };
