import { Router } from 'express';
import { login, logout } from '../controllers/auth.controller';
import { authController } from '../../api/auth/controllers/auth.controller';

const router = Router();

/**
 * @swagger
 * /admin/auth/login:
 *   post:
 *     summary: Process admin login
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', login);

/**
 * @swagger
 * /admin/auth/logout:
 *   get:
 *     summary: Process admin logout
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.get('/logout', logout);

/**
 * @swagger
 * /admin/auth/device/complete:
 *   post:
 *     summary: Complete device authorization
 *     tags: [Admin]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_code:
 *                 type: string
 *               clerk_user_id:
 *                 type: string  
 *               access_token:
 *                 type: string
 *     responses:
 *       200:
 *         description: Device authorization completed
 *       400:
 *         description: Invalid request
 */
router.post('/device/complete', authController.deviceComplete.bind(authController));

/**
 * @swagger
 * /admin/auth/clerk-config:
 *   get:
 *     summary: Get Clerk configuration for frontend
 *     tags: [Admin]
 *     responses:
 *       200:
 *         description: Clerk configuration
 */
router.get('/clerk-config', (req, res) => {
  const frontendApi = process.env.CLERK_FRONTEND_API || 'https://leading-monkfish-43.clerk.accounts.dev';
  const publishableKey = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || process.env.CLERK_PUBLISHABLE_KEY;
  
  res.json({
    clerkFrontendApi: frontendApi.replace('https://', ''), // Remove https:// prefix
    clerkPublishableKey: publishableKey
  });
});

export default router;
