/**
 * State Management Service for Project Creation
 * Handles persistence, resume functionality, and state tracking
 */

import { injectable } from 'tsyringe';
import fs from 'fs/promises';
import path from 'path';
import crypto from 'crypto';
import {
  ProjectCreationState,
  PhaseState,
  StateMetadata,
  ResumeData,
  StateSummary,
  StateQuery,
  StateUpdateOperation,
  StateEvent,
  PhaseStatus,
  StatePersistenceConfig
} from './types/state-management.types';
import { ProjectPhase, ProjectContext, ResumePoint } from './types/project-context.types';

@injectable()
export class StateManagementService {
  private readonly stateDirectory: string;
  private readonly config: StatePersistenceConfig;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor() {
    this.stateDirectory = path.join(process.cwd(), '.kapi', 'project-states');
    this.config = {
      autoSave: true,
      saveInterval: 30000, // 30 seconds
      maxVersions: 10,
      compressionEnabled: true
    };
    this.ensureStateDirectory();
  }

  /**
   * Save project creation state
   */
  async saveProjectState(state: ProjectCreationState): Promise<void> {
    try {
      const stateFile = this.getStateFilePath(state.projectId);
      const stateData = {
        ...state,
        metadata: {
          ...state.metadata,
          updatedAt: new Date()
        }
      };

      await fs.writeFile(stateFile, JSON.stringify(stateData, null, 2), 'utf-8');
      
      // Save backup version if enabled
      if (this.config.maxVersions > 0) {
        await this.saveStateVersion(state.projectId, stateData);
      }

      console.log(`✅ [STATE-MGMT] Saved state for project ${state.projectId}`);
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error saving state:`, error);
      throw new Error(`Failed to save project state: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Load project creation state
   */
  async loadProjectState(userId: string): Promise<ProjectCreationState | null> {
    try {
      const states = await this.queryStates({ userId, limit: 1 });
      return states.length > 0 ? states[0] : null;
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error loading state:`, error);
      return null;
    }
  }

  /**
   * Update phase status and data
   */
  async updatePhase(
    projectId: string, 
    phase: ProjectPhase, 
    status: PhaseStatus,
    data?: any
  ): Promise<void> {
    try {
      const state = await this.loadProjectStateById(projectId);
      if (!state) {
        throw new Error(`Project state not found for ID: ${projectId}`);
      }

      // Update phase state
      state.phaseData[phase] = {
        ...state.phaseData[phase],
        status,
        data: data || state.phaseData[phase]?.data,
        ...(status === 'in_progress' && { startedAt: new Date() }),
        ...(status === 'completed' && { completedAt: new Date() })
      };

      // Update current phase if moving to in_progress
      if (status === 'in_progress') {
        state.currentPhase = phase;
      }

      // Update completion percentage
      state.metadata.completionPercentage = this.calculateCompletionPercentage(state);

      await this.saveProjectState(state);
      
      // Emit phase update event
      this.emitEvent({
        type: status === 'in_progress' ? 'phase_start' : 'phase_complete',
        phase,
        timestamp: new Date(),
        ...(status === 'completed' && { result: data })
      } as StateEvent);

      console.log(`✅ [STATE-MGMT] Updated phase ${phase} to ${status}`);
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error updating phase:`, error);
      throw error;
    }
  }

  /**
   * Get resume point for user
   */
  async getResumePoint(userId: string): Promise<ResumePoint | null> {
    try {
      const state = await this.loadProjectState(userId);
      if (!state) return null;

      // Find the last incomplete phase
      const phases: ProjectPhase[] = [
        'personal_onboarding',
        'project_discovery',
        'requirements_assembly',
        'specification_generation', 
        'test_generation',
        'code_generation',
        'quality_analysis',
        'deployment'
      ];

      let resumePhase: ProjectPhase = 'personal_onboarding';
      for (const phase of phases) {
        const phaseState = state.phaseData[phase];
        if (!phaseState || phaseState.status !== 'completed') {
          resumePhase = phase;
          break;
        }
      }

      return {
        userId,
        phase: resumePhase,
        context: state.context,
        stateData: state.phaseData[resumePhase]?.data || {},
        lastUpdated: state.metadata.updatedAt
      };
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error getting resume point:`, error);
      return null;
    }
  }

  /**
   * Create new project state
   */
  async createProjectState(
    userId: string,
    context: ProjectContext
  ): Promise<ProjectCreationState> {
    const projectId = this.generateProjectId();
    
    const state: ProjectCreationState = {
      projectId,
      userId,
      currentPhase: 'personal_onboarding',
      context,
      phaseData: this.initializePhaseData(),
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0.0',
        isResumed: false,
        resumeCount: 0,
        completionPercentage: 0
      }
    };

    await this.saveProjectState(state);
    return state;
  }

  /**
   * Mark project as resumed
   */
  async markAsResumed(projectId: string): Promise<void> {
    try {
      const state = await this.loadProjectStateById(projectId);
      if (state) {
        state.metadata.isResumed = true;
        state.metadata.resumeCount += 1;
        await this.saveProjectState(state);
      }
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error marking as resumed:`, error);
    }
  }

  /**
   * Get project summary
   */
  async getProjectSummary(projectId: string): Promise<StateSummary | null> {
    try {
      const state = await this.loadProjectStateById(projectId);
      if (!state) return null;

      const issues = this.analyzeStateIssues(state);
      const estimatedTimeRemaining = this.estimateTimeRemaining(state);

      return {
        projectId,
        currentPhase: state.currentPhase,
        completionPercentage: state.metadata.completionPercentage,
        estimatedTimeRemaining,
        lastActivity: state.metadata.updatedAt,
        canResume: this.canResumeProject(state),
        issues
      };
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error getting summary:`, error);
      return null;
    }
  }

  /**
   * Clean up old states
   */
  async cleanupOldStates(retentionDays: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
      
      const files = await fs.readdir(this.stateDirectory);
      let cleanedCount = 0;

      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(this.stateDirectory, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            await fs.unlink(filePath);
            cleanedCount++;
          }
        }
      }

      console.log(`✅ [STATE-MGMT] Cleaned up ${cleanedCount} old state files`);
      return cleanedCount;
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error cleaning up:`, error);
      return 0;
    }
  }

  // Private helper methods

  private async ensureStateDirectory(): Promise<void> {
    try {
      await fs.access(this.stateDirectory);
    } catch {
      await fs.mkdir(this.stateDirectory, { recursive: true });
    }
  }

  private getStateFilePath(projectId: string): string {
    return path.join(this.stateDirectory, `${projectId}.json`);
  }

  private async loadProjectStateById(projectId: string): Promise<ProjectCreationState | null> {
    try {
      const stateFile = this.getStateFilePath(projectId);
      const stateData = await fs.readFile(stateFile, 'utf-8');
      return JSON.parse(stateData);
    } catch (error) {
      return null;
    }
  }

  private async queryStates(query: StateQuery): Promise<ProjectCreationState[]> {
    try {
      const files = await fs.readdir(this.stateDirectory);
      const states: ProjectCreationState[] = [];

      for (const file of files) {
        if (file.endsWith('.json')) {
          const stateFile = path.join(this.stateDirectory, file);
          const stateData = await fs.readFile(stateFile, 'utf-8');
          const state: ProjectCreationState = JSON.parse(stateData);

          // Apply query filters
          if (query.userId && state.userId !== query.userId) continue;
          if (query.phase && state.currentPhase !== query.phase) continue;
          
          states.push(state);
        }
      }

      // Sort by updated date (most recent first)
      states.sort((a, b) => 
        new Date(b.metadata.updatedAt).getTime() - new Date(a.metadata.updatedAt).getTime()
      );

      // Apply limit
      if (query.limit) {
        return states.slice(0, query.limit);
      }

      return states;
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error querying states:`, error);
      return [];
    }
  }

  private async saveStateVersion(projectId: string, state: ProjectCreationState): Promise<void> {
    try {
      const versionsDir = path.join(this.stateDirectory, 'versions', projectId);
      await fs.mkdir(versionsDir, { recursive: true });
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const versionFile = path.join(versionsDir, `${timestamp}.json`);
      
      await fs.writeFile(versionFile, JSON.stringify(state, null, 2), 'utf-8');
      
      // Clean up old versions
      await this.cleanupOldVersions(versionsDir);
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error saving version:`, error);
    }
  }

  private async cleanupOldVersions(versionsDir: string): Promise<void> {
    try {
      const files = await fs.readdir(versionsDir);
      const versionFiles = files
        .filter(f => f.endsWith('.json'))
        .sort()
        .reverse();

      if (versionFiles.length > this.config.maxVersions) {
        const filesToDelete = versionFiles.slice(this.config.maxVersions);
        for (const file of filesToDelete) {
          await fs.unlink(path.join(versionsDir, file));
        }
      }
    } catch (error) {
      console.error(`❌ [STATE-MGMT] Error cleaning versions:`, error);
    }
  }

  private initializePhaseData(): Record<ProjectPhase, PhaseState> {
    const phases: ProjectPhase[] = [
      'personal_onboarding',
      'project_discovery',
      'requirements_assembly',
      'specification_generation',
      'test_generation', 
      'code_generation',
      'quality_analysis',
      'deployment'
    ];

    const phaseData: Record<ProjectPhase, PhaseState> = {} as any;
    
    phases.forEach(phase => {
      phaseData[phase] = {
        status: 'pending',
        data: {}
      };
    });

    return phaseData;
  }

  private calculateCompletionPercentage(state: ProjectCreationState): number {
    const phases = Object.values(state.phaseData);
    const completedPhases = phases.filter(p => p.status === 'completed').length;
    return Math.round((completedPhases / phases.length) * 100);
  }

  private generateProjectId(): string {
    return `proj_${crypto.randomBytes(8).toString('hex')}_${Date.now()}`;
  }

  private analyzeStateIssues(state: ProjectCreationState): any[] {
    const issues: any[] = [];
    
    // Check for stalled phases
    Object.entries(state.phaseData).forEach(([phase, phaseState]) => {
      if (phaseState.status === 'in_progress' && phaseState.startedAt) {
        const hoursSinceStart = (Date.now() - new Date(phaseState.startedAt).getTime()) / (1000 * 60 * 60);
        if (hoursSinceStart > 24) {
          issues.push({
            phase,
            severity: 'warning',
            message: `Phase ${phase} has been running for ${Math.round(hoursSinceStart)} hours`,
            action: 'Consider reviewing or restarting this phase'
          });
        }
      }
    });

    return issues;
  }

  private estimateTimeRemaining(state: ProjectCreationState): number {
    const remainingPhases = Object.values(state.phaseData)
      .filter(p => p.status === 'pending' || p.status === 'in_progress').length;
    
    // Estimate 2 minutes per phase (simplified)
    return remainingPhases * 2;
  }

  private canResumeProject(state: ProjectCreationState): boolean {
    return state.currentPhase !== 'completed' && 
           Object.values(state.phaseData).some(p => p.status !== 'completed');
  }

  private emitEvent(event: StateEvent): void {
    const listeners = this.eventListeners.get(event.type) || [];
    listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`❌ [STATE-MGMT] Event listener error:`, error);
      }
    });
  }

  /**
   * Add event listener
   */
  addEventListener(eventType: string, listener: Function): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType)!.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }
}