/**
 * Test Generation Service
 * Generates comprehensive test suites based on specifications and requirements
 */

import { injectable } from 'tsyringe';
import { ProjectContext, ValidationResult } from './types/project-context.types';
import { 
  SpecificationResult,
  TestSuiteResult,
  TestSuite,
  TestCategory,
  TestFile,
  TestCase,
  TestConfiguration
} from './types/generation-results.types';

@injectable()
export class TestGenerationService {

  /**
   * Generate comprehensive test suite from specifications
   */
  async generateTestSuite(
    approvedSpecs: SpecificationResult,
    projectContext: ProjectContext
  ): Promise<TestSuiteResult> {
    try {
      console.log('🧪 [TEST-GEN] Starting test suite generation...');
      
      const framework = this.selectTestFramework(projectContext);
      const unitTests = this.generateUnitTests(approvedSpecs, projectContext);
      const integrationTests = this.generateIntegrationTests(approvedSpecs, projectContext);
      const e2eTests = this.generateE2ETests(approvedSpecs, projectContext);
      const configuration = this.generateTestConfiguration(projectContext, framework);
      
      const totalTests = unitTests.count + integrationTests.count + e2eTests.count;
      const estimatedCoverage = this.estimateTestCoverage(unitTests, integrationTests, e2eTests);

      const testSuite: TestSuite = {
        framework,
        totalTests,
        coverage: {
          target: 85,
          estimated: estimatedCoverage
        },
        unitTests,
        integrationTests,
        e2eTests,
        configuration
      };

      const result: TestSuiteResult = {
        testSuite,
        metadata: {
          generatedAt: new Date(),
          estimatedExecutionTime: this.estimateExecutionTime(testSuite),
          complexity: this.calculateTestComplexity(testSuite)
        }
      };

      console.log(`✅ [TEST-GEN] Test suite generated: ${totalTests} tests with ${estimatedCoverage}% estimated coverage`);
      return result;
      
    } catch (error) {
      console.error('❌ [TEST-GEN] Error generating test suite:', error);
      throw new Error(`Test generation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Validate test strategy and completeness
   */
  async validateTestStrategy(tests: TestSuiteResult): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check minimum test requirements
    if (tests.testSuite.totalTests < 5) {
      errors.push('Minimum 5 tests required for adequate coverage');
    }

    // Check test distribution
    const unitRatio = tests.testSuite.unitTests.count / tests.testSuite.totalTests;
    if (unitRatio < 0.6) {
      warnings.push('Unit tests should comprise at least 60% of total tests');
    }

    // Check coverage target
    if (tests.testSuite.coverage.estimated < tests.testSuite.coverage.target) {
      warnings.push(`Estimated coverage ${tests.testSuite.coverage.estimated}% below target ${tests.testSuite.coverage.target}%`);
    }

    // Suggest improvements
    if (tests.testSuite.e2eTests.count === 0) {
      suggestions.push('Consider adding end-to-end tests for critical user journeys');
    }

    const confidence = Math.max(0, 1 - (errors.length * 0.3) - (warnings.length * 0.1));

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      confidence
    };
  }

  // Private helper methods

  private selectTestFramework(context: ProjectContext): string {
    const techStack = context.projectDiscovery.techStack;
    
    if (techStack.includes('React')) {
      return 'Jest + React Testing Library';
    } else if (techStack.includes('Vue')) {
      return 'Jest + Vue Test Utils';
    } else if (techStack.includes('Node.js')) {
      return 'Jest + Supertest';
    } else if (techStack.includes('Python')) {
      return 'pytest';
    }
    
    return 'Jest'; // Default
  }

  private generateUnitTests(
    specs: SpecificationResult,
    context: ProjectContext
  ): TestCategory {
    const testFiles: TestFile[] = [];
    let totalTests = 0;

    // Generate component tests for React projects
    if (context.projectDiscovery.techStack.includes('React')) {
      const componentTests = this.generateComponentTests(context);
      testFiles.push(...componentTests);
      totalTests += componentTests.reduce((sum, file) => sum + file.testCases.length, 0);
    }

    // Generate service/utility tests
    const serviceTests = this.generateServiceTests(context);
    testFiles.push(...serviceTests);
    totalTests += serviceTests.reduce((sum, file) => sum + file.testCases.length, 0);

    // Generate validation tests
    const validationTests = this.generateValidationTests(context);
    testFiles.push(...validationTests);
    totalTests += validationTests.reduce((sum, file) => sum + file.testCases.length, 0);

    return {
      count: totalTests,
      files: testFiles,
      mockData: this.generateMockData(context)
    };
  }

  private generateComponentTests(context: ProjectContext): TestFile[] {
    const features = context.projectDiscovery.features;
    
    return features.map((feature, index) => ({
      path: `src/components/${this.pascalCase(feature)}/${this.pascalCase(feature)}.test.tsx`,
      description: `Unit tests for ${feature} component`,
      testCases: [
        {
          name: `renders ${feature} component`,
          description: `Should render the ${feature} component without crashing`,
          type: 'unit' as const,
          priority: 'high' as const,
          code: `test('renders ${feature} component', () => {
  render(<${this.pascalCase(feature)} />);
  expect(screen.getByRole('main')).toBeInTheDocument();
});`,
          expectedBehavior: 'Component renders successfully',
          mockData: []
        },
        {
          name: `handles ${feature} interactions`,
          description: `Should handle user interactions with ${feature}`,
          type: 'unit' as const,
          priority: 'high' as const,
          code: `test('handles user interactions', async () => {
  const mockHandler = jest.fn();
  render(<${this.pascalCase(feature)} onAction={mockHandler} />);
  
  const button = screen.getByRole('button');
  fireEvent.click(button);
  
  expect(mockHandler).toHaveBeenCalled();
});`,
          expectedBehavior: 'User interactions trigger expected handlers',
          mockData: ['mockHandler']
        }
      ],
      dependencies: ['@testing-library/react', '@testing-library/jest-dom']
    }));
  }

  private generateServiceTests(context: ProjectContext): TestFile[] {
    const requirements = context.projectDiscovery.requirements;
    
    return requirements.map(requirement => ({
      path: `src/services/${this.camelCase(requirement)}.test.ts`,
      description: `Unit tests for ${requirement} service`,
      testCases: [
        {
          name: `${requirement} service functionality`,
          description: `Should handle ${requirement} operations correctly`,
          type: 'unit' as const,
          priority: 'high' as const,
          code: `describe('${this.pascalCase(requirement)}Service', () => {
  test('performs ${requirement} operation', async () => {
    const service = new ${this.pascalCase(requirement)}Service();
    const result = await service.execute(mockData);
    
    expect(result).toBeDefined();
    expect(result.success).toBe(true);
  });
});`,
          expectedBehavior: 'Service operations complete successfully',
          mockData: ['mockData']
        }
      ],
      dependencies: ['jest']
    }));
  }

  private generateValidationTests(context: ProjectContext): TestFile[] {
    return [{
      path: 'src/utils/validation.test.ts',
      description: 'Input validation and data validation tests',
      testCases: [
        {
          name: 'validates user input',
          description: 'Should validate user input according to business rules',
          type: 'unit' as const,
          priority: 'high' as const,
          code: `test('validates user input', () => {
  const validInput = { name: 'John', email: '<EMAIL>' };
  const invalidInput = { name: '', email: 'invalid' };
  
  expect(validateInput(validInput)).toBe(true);
  expect(validateInput(invalidInput)).toBe(false);
});`,
          expectedBehavior: 'Input validation works correctly',
          mockData: []
        }
      ],
      dependencies: ['jest']
    }];
  }

  private generateIntegrationTests(
    specs: SpecificationResult,
    context: ProjectContext
  ): TestCategory {
    const testFiles: TestFile[] = [];

    // API integration tests
    if (context.projectDiscovery.projectType === 'web-app') {
      testFiles.push({
        path: 'tests/integration/api.test.ts',
        description: 'API endpoint integration tests',
        testCases: [
          {
            name: 'API endpoints integration',
            description: 'Should test API endpoints with database',
            type: 'integration' as const,
            priority: 'high' as const,
            code: `describe('API Integration', () => {
  test('creates and retrieves data', async () => {
    const response = await request(app)
      .post('/api/data')
      .send(testData);
    
    expect(response.status).toBe(201);
    
    const getResponse = await request(app)
      .get(\`/api/data/\${response.body.id}\`);
    
    expect(getResponse.status).toBe(200);
    expect(getResponse.body).toMatchObject(testData);
  });
});`,
            expectedBehavior: 'API operations work end-to-end',
            mockData: ['testData']
          }
        ],
        dependencies: ['supertest', 'jest']
      });
    }

    // Database integration tests
    if (context.projectDiscovery.requirements.includes('data persistence')) {
      testFiles.push({
        path: 'tests/integration/database.test.ts',
        description: 'Database integration tests',
        testCases: [
          {
            name: 'database operations',
            description: 'Should test database CRUD operations',
            type: 'integration' as const,
            priority: 'medium' as const,
            code: `describe('Database Integration', () => {
  test('CRUD operations', async () => {
    const created = await db.create(testRecord);
    expect(created.id).toBeDefined();
    
    const retrieved = await db.findById(created.id);
    expect(retrieved).toMatchObject(testRecord);
    
    await db.update(created.id, { updated: true });
    const updated = await db.findById(created.id);
    expect(updated.updated).toBe(true);
    
    await db.delete(created.id);
    const deleted = await db.findById(created.id);
    expect(deleted).toBeNull();
  });
});`,
            expectedBehavior: 'Database operations work correctly',
            mockData: ['testRecord']
          }
        ],
        dependencies: ['jest']
      });
    }

    const totalTests = testFiles.reduce((sum, file) => sum + file.testCases.length, 0);

    return {
      count: totalTests,
      files: testFiles
    };
  }

  private generateE2ETests(
    specs: SpecificationResult,
    context: ProjectContext
  ): TestCategory {
    const testFiles: TestFile[] = [];

    // User journey tests
    if (context.projectDiscovery.projectType === 'web-app') {
      testFiles.push({
        path: 'tests/e2e/user-journey.test.ts',
        description: 'End-to-end user journey tests',
        testCases: [
          {
            name: 'complete user journey',
            description: 'Should test complete user workflow',
            type: 'e2e' as const,
            priority: 'high' as const,
            code: `describe('User Journey', () => {
  test('user can complete main workflow', async () => {
    await page.goto('/');
    
    // Navigate to main feature
    await page.click('[data-testid="main-feature"]');
    
    // Perform actions
    await page.fill('[data-testid="input-field"]', 'test data');
    await page.click('[data-testid="submit-button"]');
    
    // Verify results
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});`,
            expectedBehavior: 'User can complete primary workflows',
            mockData: []
          }
        ],
        dependencies: ['playwright', '@playwright/test']
      });
    }

    const totalTests = testFiles.reduce((sum, file) => sum + file.testCases.length, 0);

    return {
      count: totalTests,
      files: testFiles
    };
  }

  private generateTestConfiguration(
    context: ProjectContext,
    framework: string
  ): TestConfiguration {
    if (framework.includes('Jest')) {
      return {
        setupFile: 'tests/setup.ts',
        teardownFile: 'tests/teardown.ts',
        globalMocks: ['fetch', 'localStorage'],
        environment: {
          NODE_ENV: 'test',
          DATABASE_URL: 'postgresql://localhost:5432/test_db'
        },
        scripts: {
          'test': 'jest',
          'test:watch': 'jest --watch',
          'test:coverage': 'jest --coverage',
          'test:e2e': 'playwright test'
        }
      };
    }

    return {
      setupFile: 'tests/setup.ts',
      teardownFile: 'tests/teardown.ts',
      globalMocks: [],
      environment: {},
      scripts: {
        'test': 'npm test'
      }
    };
  }

  private generateMockData(context: ProjectContext): any[] {
    return [
      {
        name: 'mockUser',
        type: 'user',
        data: {
          id: 1,
          name: 'Test User',
          email: '<EMAIL>'
        },
        usage: ['user authentication tests', 'profile tests']
      },
      {
        name: 'mockApiResponse',
        type: 'api_response',
        data: {
          status: 'success',
          data: {},
          message: 'Operation completed'
        },
        usage: ['API response tests']
      }
    ];
  }

  private estimateTestCoverage(
    unitTests: TestCategory,
    integrationTests: TestCategory,
    e2eTests: TestCategory
  ): number {
    // Simple coverage estimation based on test distribution
    const unitWeight = 0.6;
    const integrationWeight = 0.3;
    const e2eWeight = 0.1;
    
    const totalTests = unitTests.count + integrationTests.count + e2eTests.count;
    if (totalTests === 0) return 0;
    
    const unitCoverage = Math.min(85, (unitTests.count / totalTests) * 100 * unitWeight);
    const integrationCoverage = Math.min(70, (integrationTests.count / totalTests) * 100 * integrationWeight);
    const e2eCoverage = Math.min(50, (e2eTests.count / totalTests) * 100 * e2eWeight);
    
    return Math.round(unitCoverage + integrationCoverage + e2eCoverage);
  }

  private estimateExecutionTime(testSuite: TestSuite): number {
    // Estimate in seconds
    const unitTestTime = testSuite.unitTests.count * 0.1; // 100ms per unit test
    const integrationTestTime = testSuite.integrationTests.count * 2; // 2s per integration test
    const e2eTestTime = testSuite.e2eTests.count * 10; // 10s per e2e test
    
    return Math.round(unitTestTime + integrationTestTime + e2eTestTime);
  }

  private calculateTestComplexity(testSuite: TestSuite): number {
    // Complexity score from 1-10
    const complexityFactors = [
      testSuite.unitTests.count * 0.1,
      testSuite.integrationTests.count * 0.3,
      testSuite.e2eTests.count * 0.5
    ];
    
    const totalComplexity = complexityFactors.reduce((sum, factor) => sum + factor, 0);
    return Math.min(10, Math.max(1, Math.round(totalComplexity)));
  }

  // Utility methods for naming conventions
  private pascalCase(str: string): string {
    return str.split(/[\s-_]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  private camelCase(str: string): string {
    const pascal = this.pascalCase(str);
    return pascal.charAt(0).toLowerCase() + pascal.slice(1);
  }
}