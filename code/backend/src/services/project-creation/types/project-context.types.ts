/**
 * Core types for project creation context and data flow
 */

export interface PersonalProfile {
  name?: string;
  experienceLevel?: string;
  learningGoals?: string[];
  preferredStyle?: string;
  motivation?: string;
  techBackground?: string;
  communicationPreference?: string;
}

export interface ProjectDiscoveryResult {
  projectType: string;
  objective: string;
  requirements: string[];
  techStack: string[];
  features: string[];
  constraints?: string[];
  timeline?: string;
  complexity?: 'simple' | 'moderate' | 'complex';
}

export interface ProjectContext {
  personalProfile: PersonalProfile;
  projectDiscovery: ProjectDiscoveryResult;
  folder: string;
  conversationData?: any[];
  metadata: {
    createdAt: Date;
    userId: string;
    version: string;
  };
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
  confidence: number;
}

export interface UserFeedback {
  type: 'approve' | 'reject' | 'modify';
  comments?: string;
  modifications?: Record<string, any>;
  rating?: number;
}

export interface ResumePoint {
  userId: string;
  phase: ProjectPhase;
  context: ProjectContext;
  stateData: any;
  lastUpdated: Date;
}

export type ProjectPhase = 
  | 'personal_onboarding'
  | 'project_discovery' 
  | 'requirements_assembly'
  | 'specification_generation'
  | 'test_generation'
  | 'code_generation'
  | 'quality_analysis'
  | 'deployment'
  | 'completed';

export interface ProjectCreationState {
  projectId: string;
  userId: string;
  currentPhase: ProjectPhase;
  context: ProjectContext;
  stateData: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  isResumed: boolean;
}