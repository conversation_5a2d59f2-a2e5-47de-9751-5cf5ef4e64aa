/**
 * Types for state management and persistence
 */

import { ProjectPhase, ProjectContext } from './project-context.types';

export interface ProjectCreationState {
  projectId: string;
  userId: string;
  currentPhase: ProjectPhase;
  context: ProjectContext;
  phaseData: Record<ProjectPhase, PhaseState>;
  metadata: StateMetadata;
  resumeData?: ResumeData;
}

export interface PhaseState {
  status: PhaseStatus;
  startedAt?: Date;
  completedAt?: Date;
  data: any;
  validationResult?: PhaseValidation;
  userFeedback?: PhaseFeedback;
}

export type PhaseStatus = 
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'failed'
  | 'skipped'
  | 'requires_user_input';

export interface PhaseValidation {
  isValid: boolean;
  score: number;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
}

export interface ValidationError {
  code: string;
  message: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  phase: ProjectPhase;
  field?: string;
}

export interface ValidationWarning {
  code: string;
  message: string;
  impact: string;
  recommendation?: string;
}

export interface ValidationSuggestion {
  type: 'improvement' | 'optimization' | 'alternative';
  title: string;
  description: string;
  benefit: string;
  effort: 'low' | 'medium' | 'high';
}

export interface PhaseFeedback {
  rating: number; // 1-5
  comments?: string;
  improvements?: string[];
  approved: boolean;
  timestamp: Date;
}

export interface StateMetadata {
  createdAt: Date;
  updatedAt: Date;
  version: string;
  totalDuration?: number;
  isResumed: boolean;
  resumeCount: number;
  completionPercentage: number;
}

export interface ResumeData {
  lastActivePhase: ProjectPhase;
  pausedAt: Date;
  resumeToken: string;
  contextSnapshot: any;
  userNotes?: string;
}

export interface StatePersistenceConfig {
  autoSave: boolean;
  saveInterval: number; // milliseconds
  maxVersions: number;
  compressionEnabled: boolean;
}

export interface StateQuery {
  userId?: string;
  projectId?: string;
  phase?: ProjectPhase;
  status?: PhaseStatus;
  dateRange?: {
    start: Date;
    end: Date;
  };
  limit?: number;
  offset?: number;
}

export interface StateUpdateOperation {
  type: 'phase_start' | 'phase_complete' | 'data_update' | 'user_feedback' | 'validation';
  phase: ProjectPhase;
  data?: any;
  metadata?: Record<string, any>;
}

export interface StateSummary {
  projectId: string;
  currentPhase: ProjectPhase;
  completionPercentage: number;
  estimatedTimeRemaining: number;
  lastActivity: Date;
  canResume: boolean;
  issues: StateSummaryIssue[];
}

export interface StateSummaryIssue {
  phase: ProjectPhase;
  severity: 'blocking' | 'warning' | 'info';
  message: string;
  action?: string;
}

// Event types for state management
export type StateEvent = 
  | PhaseStartEvent
  | PhaseCompleteEvent
  | UserInputEvent
  | ValidationEvent
  | ErrorEvent;

export interface PhaseStartEvent {
  type: 'phase_start';
  phase: ProjectPhase;
  timestamp: Date;
  context: any;
}

export interface PhaseCompleteEvent {
  type: 'phase_complete';
  phase: ProjectPhase;
  timestamp: Date;
  result: any;
  duration: number;
}

export interface UserInputEvent {
  type: 'user_input';
  phase: ProjectPhase;
  input: any;
  timestamp: Date;
}

export interface ValidationEvent {
  type: 'validation';
  phase: ProjectPhase;
  result: PhaseValidation;
  timestamp: Date;
}

export interface ErrorEvent {
  type: 'error';
  phase: ProjectPhase;
  error: Error;
  timestamp: Date;
  context?: any;
}