/**
 * Types for generation results from different phases
 */

export interface DocumentationSet {
  readme: string;
  apiDocs: string;
  architectureDiagram: string;
  setupGuide: string;
  contributionGuide: string;
  changelog: string;
  license: string;
}

export interface PresentationSet {
  executiveOverview: {
    title: string;
    slides: PresentationSlide[];
    duration: number;
  };
  technicalDeepDive: {
    title: string;
    slides: PresentationSlide[];
    duration: number;
  };
  demoScript: {
    title: string;
    steps: DemoStep[];
    duration: number;
  };
}

export interface PresentationSlide {
  title: string;
  content: string;
  notes?: string;
  layout: 'title' | 'content' | 'two-column' | 'image' | 'code';
  visualElements?: VisualElement[];
}

export interface DemoStep {
  step: number;
  title: string;
  description: string;
  code?: string;
  expectedOutput?: string;
  tips?: string[];
}

export interface VisualElement {
  type: 'diagram' | 'chart' | 'image' | 'code-block';
  content: string;
  caption?: string;
}

export interface TestSuite {
  framework: string;
  totalTests: number;
  coverage: {
    target: number;
    estimated: number;
  };
  unitTests: TestCategory;
  integrationTests: TestCategory;
  e2eTests: TestCategory;
  configuration: TestConfiguration;
}

export interface TestCategory {
  count: number;
  files: TestFile[];
  mockData?: MockDataSet[];
}

export interface TestFile {
  path: string;
  description: string;
  testCases: TestCase[];
  dependencies: string[];
}

export interface TestCase {
  name: string;
  description: string;
  type: 'unit' | 'integration' | 'e2e';
  priority: 'high' | 'medium' | 'low';
  code: string;
  expectedBehavior: string;
  mockData?: string[];
}

export interface MockDataSet {
  name: string;
  type: string;
  data: any;
  usage: string[];
}

export interface TestConfiguration {
  setupFile: string;
  teardownFile: string;
  globalMocks: string[];
  environment: Record<string, string>;
  scripts: Record<string, string>;
}

export interface CodeBase {
  structure: ProjectStructure;
  files: CodeFile[];
  dependencies: DependencyManifest;
  configuration: ProjectConfiguration;
  deployment: DeploymentConfig;
}

export interface ProjectStructure {
  directories: Directory[];
  totalFiles: number;
  estimatedSize: string;
  architecture: ArchitecturePattern;
}

export interface Directory {
  path: string;
  purpose: string;
  files: string[];
  subdirectories?: Directory[];
}

export interface CodeFile {
  path: string;
  type: 'component' | 'service' | 'model' | 'config' | 'test' | 'util';
  language: string;
  content: string;
  imports: string[];
  exports: string[];
  description: string;
  complexity: 'low' | 'medium' | 'high';
}

export interface DependencyManifest {
  production: Dependency[];
  development: Dependency[];
  packageManager: 'npm' | 'yarn' | 'pnpm' | 'pip' | 'cargo';
  lockFile: string;
}

export interface Dependency {
  name: string;
  version: string;
  purpose: string;
  category: 'framework' | 'library' | 'tool' | 'utility';
  optional?: boolean;
}

export interface ProjectConfiguration {
  buildSystem: string;
  entryPoints: string[];
  outputDirectory: string;
  environment: Record<string, string>;
  scripts: Record<string, string>;
  linting: LintConfiguration;
  formatting: FormatConfiguration;
}

export interface LintConfiguration {
  enabled: boolean;
  rules: Record<string, any>;
  ignorePatterns: string[];
}

export interface FormatConfiguration {
  enabled: boolean;
  style: string;
  config: Record<string, any>;
}

export interface DeploymentConfig {
  platforms: DeploymentPlatform[];
  environment: 'development' | 'staging' | 'production';
  scaling: ScalingConfiguration;
  monitoring: MonitoringConfiguration;
}

export interface DeploymentPlatform {
  name: 'railway' | 'vercel' | 'netlify' | 'aws' | 'gcp' | 'azure';
  config: Record<string, any>;
  url?: string;
  status: 'pending' | 'deploying' | 'deployed' | 'failed';
}

export interface ScalingConfiguration {
  autoScale: boolean;
  minInstances: number;
  maxInstances: number;
  targetMetric: string;
}

export interface MonitoringConfiguration {
  enabled: boolean;
  metrics: string[];
  alerts: AlertConfiguration[];
}

export interface AlertConfiguration {
  name: string;
  condition: string;
  threshold: number;
  action: string;
}

export type ArchitecturePattern = 
  | 'mvc'
  | 'mvvm' 
  | 'microservices'
  | 'layered'
  | 'component-based'
  | 'event-driven'
  | 'serverless';

// Specific result types for each generation phase
export interface SpecificationResult {
  documentation: DocumentationSet;
  presentations: PresentationSet;
  metadata: {
    generatedAt: Date;
    version: string;
    qualityScore: number;
  };
}

export interface TestSuiteResult {
  testSuite: TestSuite;
  metadata: {
    generatedAt: Date;
    estimatedExecutionTime: number;
    complexity: number;
  };
}

export interface CodeGenerationResult {
  codeBase: CodeBase;
  metadata: {
    generatedAt: Date;
    linesOfCode: number;
    complexity: number;
    estimatedDevelopmentTime: string;
  };
}