/**
 * Specification Generation Service
 * Generates comprehensive documentation and presentations from requirements
 */

import { injectable } from 'tsyringe';
import { AgentOrchestrator } from '../agent-orchestrator.service';
import { TemplateService } from '../template/template.service';
import { ProjectContext, UserFeedback } from './types/project-context.types';
import { 
  SpecificationResult,
  DocumentationSet,
  PresentationSet,
  PresentationSlide
} from './types/generation-results.types';

@injectable()
export class SpecificationGenerationService {
  constructor(
    private agentOrchestrator: AgentOrchestrator,
    private templateService: TemplateService
  ) {}

  /**
   * Generate comprehensive specifications including documentation and presentations
   */
  async generateSpecifications(projectContext: ProjectContext): Promise<SpecificationResult> {
    try {
      console.log('📚 [SPEC-GEN] Starting specification generation...');
      
      // Generate documentation using the agent orchestrator
      const documentation = await this.generateDocumentation(projectContext);
      
      // Generate presentations for different audiences
      const presentations = await this.generatePresentations(projectContext);
      
      const result: SpecificationResult = {
        documentation,
        presentations,
        metadata: {
          generatedAt: new Date(),
          version: '1.0.0',
          qualityScore: this.calculateSpecQuality(documentation, presentations)
        }
      };

      console.log(`✅ [SPEC-GEN] Specifications generated with quality score: ${result.metadata.qualityScore}`);
      return result;
      
    } catch (error) {
      console.error('❌ [SPEC-GEN] Error generating specifications:', error);
      throw new Error(`Specification generation failed: ${error.message}`);
    }
  }

  /**
   * Regenerate specifications with user feedback
   */
  async regenerateWithFeedback(
    specs: SpecificationResult,
    feedback: UserFeedback
  ): Promise<SpecificationResult> {
    try {
      console.log('🔄 [SPEC-GEN] Regenerating with feedback...');
      
      // Apply feedback to improve specifications
      let improvedSpecs = { ...specs };
      
      if (feedback.comments) {
        // Use feedback to improve documentation
        improvedSpecs.documentation = await this.improveDocumentation(
          specs.documentation,
          feedback.comments
        );
        
        // Update presentations based on feedback
        improvedSpecs.presentations = await this.improvePresentations(
          specs.presentations,
          feedback.comments
        );
      }

      // Update metadata
      improvedSpecs.metadata = {
        ...specs.metadata,
        generatedAt: new Date(),
        qualityScore: this.calculateSpecQuality(
          improvedSpecs.documentation,
          improvedSpecs.presentations
        )
      };

      console.log('✅ [SPEC-GEN] Specifications regenerated with improvements');
      return improvedSpecs;
      
    } catch (error) {
      console.error('❌ [SPEC-GEN] Error regenerating specifications:', error);
      throw error;
    }
  }

  // Private methods

  private async generateDocumentation(context: ProjectContext): Promise<DocumentationSet> {
    try {
      // Use agent orchestrator to generate comprehensive documentation
      const docResult = await this.agentOrchestrator.generateDocumentation(context);
      
      // If agent orchestrator returns the expected format, use it
      if (docResult && typeof docResult === 'object') {
        return {
          readme: docResult.readme || this.generateReadme(context),
          apiDocs: docResult.apiDocs || this.generateApiDocs(context),
          architectureDiagram: docResult.architectureDiagram || this.generateArchitectureDiagram(context),
          setupGuide: docResult.setupGuide || this.generateSetupGuide(context),
          contributionGuide: docResult.contributionGuide || this.generateContributionGuide(context),
          changelog: docResult.changelog || this.generateChangelog(context),
          license: docResult.license || this.generateLicense(context)
        };
      }
      
      // Fallback to individual generation methods
      return {
        readme: this.generateReadme(context),
        apiDocs: this.generateApiDocs(context),
        architectureDiagram: this.generateArchitectureDiagram(context),
        setupGuide: this.generateSetupGuide(context),
        contributionGuide: this.generateContributionGuide(context),
        changelog: this.generateChangelog(context),
        license: this.generateLicense(context)
      };
      
    } catch (error) {
      console.error('❌ [SPEC-GEN] Error generating documentation:', error);
      
      // Return fallback documentation
      return {
        readme: this.generateReadme(context),
        apiDocs: this.generateApiDocs(context),
        architectureDiagram: this.generateArchitectureDiagram(context),
        setupGuide: this.generateSetupGuide(context),
        contributionGuide: this.generateContributionGuide(context),
        changelog: this.generateChangelog(context),
        license: this.generateLicense(context)
      };
    }
  }

  private async generatePresentations(context: ProjectContext): Promise<PresentationSet> {
    try {
      // Use agent orchestrator for presentation generation
      const presResult = await this.agentOrchestrator.generateSlides(context);
      
      if (presResult && typeof presResult === 'object') {
        return presResult;
      }
      
      // Fallback to manual generation
      return {
        executiveOverview: this.generateExecutiveOverview(context),
        technicalDeepDive: this.generateTechnicalDeepDive(context),
        demoScript: this.generateDemoScript(context)
      };
      
    } catch (error) {
      console.error('❌ [SPEC-GEN] Error generating presentations:', error);
      
      // Return fallback presentations
      return {
        executiveOverview: this.generateExecutiveOverview(context),
        technicalDeepDive: this.generateTechnicalDeepDive(context),
        demoScript: this.generateDemoScript(context)
      };
    }
  }

  // Documentation generation methods
  private generateReadme(context: ProjectContext): string {
    const projectName = context.projectDiscovery.projectType.replace('-', ' ').toUpperCase();
    
    return `# ${projectName}

## Overview
${context.projectDiscovery.requirements.join(', ')} application built with ${context.projectDiscovery.techStack.join(', ')}.

## Features
${context.projectDiscovery.features.map(feature => `- ${feature}`).join('\n')}

## Tech Stack
${context.projectDiscovery.techStack.map(tech => `- ${tech}`).join('\n')}

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation
1. Clone the repository
2. Install dependencies: \`npm install\`
3. Start development server: \`npm run dev\`

## Usage
Detailed usage instructions will be provided in the setup guide.

## Contributing
See CONTRIBUTING.md for contribution guidelines.

## License
MIT License - see LICENSE file for details.
`;
  }

  private generateApiDocs(context: ProjectContext): string {
    return `# API Documentation

## Overview
RESTful API for ${context.projectDiscovery.projectType} application.

## Base URL
\`http://localhost:3000/api\`

## Authentication
Bearer token authentication required for protected endpoints.

## Endpoints

### Health Check
- **GET** \`/health\`
- Returns service health status

### User Management
- **POST** \`/users\` - Create user
- **GET** \`/users/:id\` - Get user by ID
- **PUT** \`/users/:id\` - Update user
- **DELETE** \`/users/:id\` - Delete user

## Error Handling
Standard HTTP status codes with JSON error responses.

## Rate Limiting
100 requests per minute per IP address.
`;
  }

  private generateArchitectureDiagram(context: ProjectContext): string {
    return `# Architecture Diagram

## System Overview
\`\`\`
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │────│   Backend   │────│  Database   │
│   (React)   │    │  (Node.js)  │    │(PostgreSQL)│
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       │                   │                   │
   ┌───────┐         ┌───────────┐      ┌──────────┐
   │ Users │         │  API      │      │  Data    │
   │       │         │  Layer    │      │  Layer   │
   └───────┘         └───────────┘      └──────────┘
\`\`\`

## Component Details
- **Frontend**: User interface built with React
- **Backend**: REST API built with Node.js/Express
- **Database**: Data persistence with PostgreSQL

## Data Flow
1. User interacts with frontend
2. Frontend sends API requests to backend
3. Backend processes requests and queries database
4. Response sent back through the chain
`;
  }

  private generateSetupGuide(context: ProjectContext): string {
    return `# Setup Guide

## Development Environment Setup

### Prerequisites
- Node.js v16+ 
- npm or yarn
- Git

### Local Development
1. **Clone Repository**
   \`\`\`bash
   git clone <repository-url>
   cd ${context.projectDiscovery.projectType}
   \`\`\`

2. **Install Dependencies**
   \`\`\`bash
   npm install
   \`\`\`

3. **Environment Configuration**
   \`\`\`bash
   cp .env.example .env
   # Edit .env with your configuration
   \`\`\`

4. **Start Development Server**
   \`\`\`bash
   npm run dev
   \`\`\`

5. **Run Tests**
   \`\`\`bash
   npm test
   \`\`\`

## Production Deployment
Deployment instructions for production environment.

## Troubleshooting
Common issues and solutions.
`;
  }

  private generateContributionGuide(context: ProjectContext): string {
    return `# Contributing Guide

## Getting Started
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Write/update tests
5. Submit a pull request

## Code Standards
- Follow ESLint configuration
- Write meaningful commit messages
- Include tests for new features

## Pull Request Process
1. Update documentation
2. Ensure tests pass
3. Request code review
4. Address feedback

## Reporting Issues
Use GitHub issues for bug reports and feature requests.
`;
  }

  private generateChangelog(context: ProjectContext): string {
    return `# Changelog

## [1.0.0] - ${new Date().toISOString().split('T')[0]}

### Added
- Initial project setup
- Core functionality
${context.projectDiscovery.features.map(feature => `- ${feature}`).join('\n')}

### Technical
- ${context.projectDiscovery.techStack.join(', ')} implementation
- Unit and integration tests
- Documentation
`;
  }

  private generateLicense(context: ProjectContext): string {
    return `MIT License

Copyright (c) ${new Date().getFullYear()} ${context.personalProfile.name || 'Project Owner'}

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
`;
  }

  // Presentation generation methods
  private generateExecutiveOverview(context: ProjectContext): any {
    const slides: PresentationSlide[] = [
      {
        title: `${context.projectDiscovery.projectType.toUpperCase()} Project Overview`,
        content: `# ${context.projectDiscovery.projectType.replace('-', ' ').toUpperCase()}

## Objective
${context.projectDiscovery.objective}

## Key Benefits
- Modern technology stack
- Scalable architecture
- User-focused design`,
        layout: 'title'
      },
      {
        title: 'Technical Stack',
        content: `## Technology Choices

${context.projectDiscovery.techStack.map(tech => `- **${tech}**: Industry-standard technology`).join('\n')}

## Why These Technologies?
- Proven reliability
- Active community support
- Excellent documentation`,
        layout: 'content'
      },
      {
        title: 'Features & Capabilities',
        content: `## Core Features

${context.projectDiscovery.features.map((feature, index) => `${index + 1}. ${feature}`).join('\n')}

## Future Enhancements
- Additional integrations
- Advanced analytics
- Mobile optimization`,
        layout: 'content'
      }
    ];

    return {
      title: 'Executive Overview',
      slides,
      duration: 10
    };
  }

  private generateTechnicalDeepDive(context: ProjectContext): any {
    const slides: PresentationSlide[] = [
      {
        title: 'Technical Architecture',
        content: `# System Architecture

## Component Overview
- Frontend: React-based user interface
- Backend: Node.js REST API
- Database: PostgreSQL data layer

## Design Patterns
- Component-based architecture
- RESTful API design
- Responsive UI patterns`,
        layout: 'content'
      },
      {
        title: 'Implementation Details',
        content: `## Development Approach

### Frontend Implementation
- React with TypeScript
- Component-based architecture
- Responsive design principles

### Backend Implementation
- Express.js framework
- RESTful API endpoints
- Database integration`,
        layout: 'two-column'
      }
    ];

    return {
      title: 'Technical Deep Dive',
      slides,
      duration: 20
    };
  }

  private generateDemoScript(context: ProjectContext): any {
    return {
      title: 'Demo Script',
      steps: [
        {
          step: 1,
          title: 'Application Launch',
          description: 'Start the application and show the main interface',
          code: 'npm run dev',
          expectedOutput: 'Development server starts on localhost:3000',
          tips: ['Ensure all dependencies are installed', 'Check environment variables']
        },
        {
          step: 2,
          title: 'Core Feature Demo',
          description: 'Demonstrate the main functionality',
          expectedOutput: 'Feature works as expected',
          tips: ['Prepare test data', 'Have backup scenarios ready']
        }
      ],
      duration: 15
    };
  }

  private async improveDocumentation(
    documentation: DocumentationSet,
    feedback: string
  ): Promise<DocumentationSet> {
    // Simple feedback incorporation
    // In a real implementation, this would use AI to improve documentation
    return {
      ...documentation,
      readme: documentation.readme + `\n\n## Updates\nImproved based on feedback: ${feedback}`
    };
  }

  private async improvePresentations(
    presentations: PresentationSet,
    feedback: string
  ): Promise<PresentationSet> {
    // Simple feedback incorporation
    return presentations;
  }

  private calculateSpecQuality(
    documentation: DocumentationSet,
    presentations: PresentationSet
  ): number {
    let score = 0;
    
    // Documentation quality scoring
    if (documentation.readme.length > 500) score += 20;
    if (documentation.apiDocs.length > 300) score += 15;
    if (documentation.setupGuide.length > 400) score += 15;
    if (documentation.architectureDiagram.length > 200) score += 10;
    
    // Presentation quality scoring
    if (presentations.executiveOverview.slides.length >= 3) score += 15;
    if (presentations.technicalDeepDive.slides.length >= 2) score += 15;
    if (presentations.demoScript.steps.length >= 2) score += 10;
    
    return Math.min(100, score);
  }
}