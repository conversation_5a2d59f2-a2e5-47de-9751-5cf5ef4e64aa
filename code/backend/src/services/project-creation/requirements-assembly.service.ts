/**
 * Requirements Assembly Service
 * Processes and assembles project requirements from personal profile and discovery data
 */

import { injectable } from 'tsyringe';
import {
  PersonalProfile,
  ProjectDiscoveryResult,
  ProjectContext,
  ValidationResult,
  UserFeedback
} from './types/project-context.types';

export interface AssembledRequirements {
  functionalRequirements: FunctionalRequirement[];
  nonFunctionalRequirements: NonFunctionalRequirement[];
  technicalConstraints: TechnicalConstraint[];
  userStories: UserStory[];
  acceptanceCriteria: AcceptanceCriteria[];
  architecture: ArchitectureRecommendation;
  timeline: TimelineEstimate;
}

export interface FunctionalRequirement {
  id: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  complexity: 'simple' | 'moderate' | 'complex';
  dependencies: string[];
  category: 'core' | 'feature' | 'integration' | 'ui';
}

export interface NonFunctionalRequirement {
  id: string;
  type: 'performance' | 'security' | 'usability' | 'scalability' | 'reliability';
  description: string;
  metrics: RequirementMetric[];
  priority: 'high' | 'medium' | 'low';
}

export interface RequirementMetric {
  name: string;
  target: string;
  measurement: string;
}

export interface TechnicalConstraint {
  id: string;
  type: 'technology' | 'platform' | 'integration' | 'deployment';
  description: string;
  justification: string;
  impact: 'high' | 'medium' | 'low';
}

export interface UserStory {
  id: string;
  role: string;
  goal: string;
  benefit: string;
  priority: number;
  estimatedPoints: number;
  acceptanceCriteria: string[];
}

export interface AcceptanceCriteria {
  storyId: string;
  criteria: string[];
  testScenarios: TestScenario[];
}

export interface TestScenario {
  given: string;
  when: string;
  then: string;
  priority: 'high' | 'medium' | 'low';
}

export interface ArchitectureRecommendation {
  pattern: string;
  components: ArchitectureComponent[];
  dataFlow: DataFlowDescription[];
  integrations: IntegrationPoint[];
  reasoning: string;
}

export interface ArchitectureComponent {
  name: string;
  type: 'frontend' | 'backend' | 'database' | 'service' | 'utility';
  purpose: string;
  technologies: string[];
  interfaces: ComponentInterface[];
}

export interface ComponentInterface {
  name: string;
  type: 'api' | 'event' | 'data' | 'ui';
  description: string;
}

export interface DataFlowDescription {
  from: string;
  to: string;
  data: string;
  method: string;
}

export interface IntegrationPoint {
  name: string;
  type: 'external-api' | 'third-party' | 'internal-service';
  purpose: string;
  requirements: string[];
}

export interface TimelineEstimate {
  totalEstimate: string;
  phases: PhaseEstimate[];
  risks: RiskFactor[];
  assumptions: string[];
}

export interface PhaseEstimate {
  phase: string;
  duration: string;
  confidence: 'high' | 'medium' | 'low';
  deliverables: string[];
}

export interface RiskFactor {
  risk: string;
  impact: 'high' | 'medium' | 'low';
  probability: 'high' | 'medium' | 'low';
  mitigation: string;
}

@injectable()
export class RequirementsAssemblyService {
  
  /**
   * Assemble comprehensive requirements from profile and discovery data
   */
  async assembleRequirements(
    personalProfile: PersonalProfile,
    projectDiscovery: ProjectDiscoveryResult
  ): Promise<AssembledRequirements> {
    try {
      console.log('📋 [REQ-ASSEMBLY] Starting requirements assembly...');
      
      // Generate functional requirements based on project type and features
      const functionalRequirements = this.generateFunctionalRequirements(projectDiscovery);
      
      // Generate non-functional requirements based on experience level and constraints
      const nonFunctionalRequirements = this.generateNonFunctionalRequirements(
        personalProfile,
        projectDiscovery
      );
      
      // Identify technical constraints
      const technicalConstraints = this.identifyTechnicalConstraints(
        personalProfile,
        projectDiscovery
      );
      
      // Create user stories from requirements
      const userStories = this.generateUserStories(functionalRequirements, projectDiscovery);
      
      // Generate acceptance criteria
      const acceptanceCriteria = this.generateAcceptanceCriteria(userStories);
      
      // Recommend architecture
      const architecture = this.recommendArchitecture(
        personalProfile,
        projectDiscovery,
        functionalRequirements
      );
      
      // Estimate timeline
      const timeline = this.estimateTimeline(
        functionalRequirements,
        nonFunctionalRequirements,
        personalProfile
      );

      const assembled: AssembledRequirements = {
        functionalRequirements,
        nonFunctionalRequirements,
        technicalConstraints,
        userStories,
        acceptanceCriteria,
        architecture,
        timeline
      };

      console.log(`✅ [REQ-ASSEMBLY] Requirements assembled: ${functionalRequirements.length} functional, ${userStories.length} user stories`);
      return assembled;
      
    } catch (error) {
      console.error('❌ [REQ-ASSEMBLY] Error assembling requirements:', error);
      throw new Error(`Requirements assembly failed: ${error.message}`);
    }
  }

  /**
   * Validate assembled requirements for completeness and consistency
   */
  async validateRequirements(requirements: AssembledRequirements): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Check for minimum functional requirements
    if (requirements.functionalRequirements.length < 3) {
      errors.push('Minimum 3 functional requirements needed for viable project');
    }

    // Check for high priority requirements
    const highPriorityReqs = requirements.functionalRequirements.filter(r => r.priority === 'high');
    if (highPriorityReqs.length === 0) {
      warnings.push('No high priority requirements identified - consider priority review');
    }

    // Check for security requirements
    const securityReqs = requirements.nonFunctionalRequirements.filter(r => r.type === 'security');
    if (securityReqs.length === 0) {
      warnings.push('No security requirements specified - consider adding authentication/authorization');
    }

    // Check architecture completeness
    if (requirements.architecture.components.length < 2) {
      errors.push('Architecture needs at least frontend and backend components');
    }

    // Suggest improvements
    if (requirements.userStories.length > 20) {
      suggestions.push('Consider breaking down into smaller releases - large scope detected');
    }

    const confidence = Math.max(0, 1 - (errors.length * 0.3) - (warnings.length * 0.1));

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      confidence
    };
  }

  /**
   * Refine requirements based on user feedback
   */
  async refineRequirements(
    requirements: AssembledRequirements,
    feedback: UserFeedback
  ): Promise<AssembledRequirements> {
    try {
      console.log('🔄 [REQ-ASSEMBLY] Refining requirements based on feedback...');
      
      let refinedRequirements = { ...requirements };

      if (feedback.type === 'modify' && feedback.modifications) {
        // Apply specific modifications
        if (feedback.modifications.priority) {
          refinedRequirements.functionalRequirements = this.adjustPriorities(
            refinedRequirements.functionalRequirements,
            feedback.modifications.priority
          );
        }

        if (feedback.modifications.scope) {
          refinedRequirements = this.adjustScope(refinedRequirements, feedback.modifications.scope);
        }

        if (feedback.modifications.technology) {
          refinedRequirements.architecture = this.adjustTechnology(
            refinedRequirements.architecture,
            feedback.modifications.technology
          );
        }
      }

      console.log('✅ [REQ-ASSEMBLY] Requirements refined based on feedback');
      return refinedRequirements;
      
    } catch (error) {
      console.error('❌ [REQ-ASSEMBLY] Error refining requirements:', error);
      throw error;
    }
  }

  // Private helper methods

  private generateFunctionalRequirements(discovery: ProjectDiscoveryResult): FunctionalRequirement[] {
    const requirements: FunctionalRequirement[] = [];
    let reqId = 1;

    // Core requirements based on project type
    if (discovery.projectType === 'web-app') {
      requirements.push({
        id: `FR-${reqId++}`,
        title: 'User Interface',
        description: 'Responsive web interface for user interaction',
        priority: 'high',
        complexity: 'moderate',
        dependencies: [],
        category: 'ui'
      });

      requirements.push({
        id: `FR-${reqId++}`,
        title: 'Navigation System',
        description: 'Clear navigation between different sections/pages',
        priority: 'high',
        complexity: 'simple',
        dependencies: ['FR-1'],
        category: 'ui'
      });
    }

    // Feature-based requirements
    discovery.features.forEach(feature => {
      requirements.push({
        id: `FR-${reqId++}`,
        title: this.formatFeatureTitle(feature),
        description: `Implementation of ${feature} functionality`,
        priority: this.determinePriority(feature),
        complexity: this.determineComplexity(feature),
        dependencies: this.identifyDependencies(feature, requirements),
        category: this.categorizeFeature(feature)
      });
    });

    // Standard requirements
    if (discovery.requirements.includes('data persistence')) {
      requirements.push({
        id: `FR-${reqId++}`,
        title: 'Data Persistence',
        description: 'Store and retrieve application data reliably',
        priority: 'high',
        complexity: 'moderate',
        dependencies: [],
        category: 'core'
      });
    }

    return requirements;
  }

  private generateNonFunctionalRequirements(
    profile: PersonalProfile,
    discovery: ProjectDiscoveryResult
  ): NonFunctionalRequirement[] {
    const requirements: NonFunctionalRequirement[] = [];
    let reqId = 1;

    // Performance requirements
    requirements.push({
      id: `NFR-${reqId++}`,
      type: 'performance',
      description: 'Application should load within acceptable time limits',
      metrics: [
        { name: 'Page Load Time', target: '< 3 seconds', measurement: 'Lighthouse audit' },
        { name: 'API Response Time', target: '< 500ms', measurement: 'Server monitoring' }
      ],
      priority: 'high'
    });

    // Security requirements based on project complexity
    if (discovery.complexity !== 'simple') {
      requirements.push({
        id: `NFR-${reqId++}`,
        type: 'security',
        description: 'Secure handling of user data and authentication',
        metrics: [
          { name: 'Authentication', target: 'JWT or OAuth 2.0', measurement: 'Security audit' },
          { name: 'Data Encryption', target: 'HTTPS/TLS 1.3', measurement: 'SSL certificate' }
        ],
        priority: 'high'
      });
    }

    // Usability requirements based on experience level
    if (profile.experienceLevel !== 'beginner') {
      requirements.push({
        id: `NFR-${reqId++}`,
        type: 'usability',
        description: 'Intuitive user interface with good user experience',
        metrics: [
          { name: 'Accessibility', target: 'WCAG 2.1 AA', measurement: 'Accessibility audit' },
          { name: 'Mobile Responsiveness', target: 'Works on mobile', measurement: 'Cross-device testing' }
        ],
        priority: 'medium'
      });
    }

    return requirements;
  }

  private identifyTechnicalConstraints(
    profile: PersonalProfile,
    discovery: ProjectDiscoveryResult
  ): TechnicalConstraint[] {
    const constraints: TechnicalConstraint[] = [];
    let constraintId = 1;

    // Technology stack constraints
    discovery.techStack.forEach(tech => {
      constraints.push({
        id: `TC-${constraintId++}`,
        type: 'technology',
        description: `Must use ${tech} as specified in requirements`,
        justification: 'User-specified technology preference',
        impact: 'high'
      });
    });

    // Experience level constraints
    if (profile.experienceLevel === 'beginner') {
      constraints.push({
        id: `TC-${constraintId++}`,
        type: 'technology',
        description: 'Use well-documented, beginner-friendly technologies',
        justification: 'Match user experience level for maintainability',
        impact: 'medium'
      });
    }

    // Timeline constraints
    if (discovery.timeline) {
      constraints.push({
        id: `TC-${constraintId++}`,
        type: 'deployment',
        description: `Project must be completed within ${discovery.timeline}`,
        justification: 'User-specified timeline requirement',
        impact: 'high'
      });
    }

    return constraints;
  }

  private generateUserStories(
    functionalReqs: FunctionalRequirement[],
    discovery: ProjectDiscoveryResult
  ): UserStory[] {
    const userStories: UserStory[] = [];
    let storyId = 1;

    functionalReqs.forEach(req => {
      const role = this.determineUserRole(req, discovery);
      const story: UserStory = {
        id: `US-${storyId++}`,
        role,
        goal: this.convertToGoal(req.description),
        benefit: this.identifyBenefit(req, discovery),
        priority: this.convertPriorityToNumber(req.priority),
        estimatedPoints: this.estimateStoryPoints(req),
        acceptanceCriteria: this.generateBasicCriteria(req)
      };
      userStories.push(story);
    });

    return userStories;
  }

  private generateAcceptanceCriteria(userStories: UserStory[]): AcceptanceCriteria[] {
    return userStories.map(story => ({
      storyId: story.id,
      criteria: story.acceptanceCriteria,
      testScenarios: this.generateTestScenarios(story)
    }));
  }

  private recommendArchitecture(
    profile: PersonalProfile,
    discovery: ProjectDiscoveryResult,
    functionalReqs: FunctionalRequirement[]
  ): ArchitectureRecommendation {
    const pattern = this.selectArchitecturePattern(discovery, functionalReqs);
    const components = this.generateComponents(discovery, pattern);
    const dataFlow = this.generateDataFlow(components);
    const integrations = this.identifyIntegrations(discovery);

    return {
      pattern,
      components,
      dataFlow,
      integrations,
      reasoning: this.explainArchitectureChoice(pattern, discovery, profile)
    };
  }

  private estimateTimeline(
    functionalReqs: FunctionalRequirement[],
    nonFunctionalReqs: NonFunctionalRequirement[],
    profile: PersonalProfile
  ): TimelineEstimate {
    const baseHours = functionalReqs.reduce((total, req) => {
      const complexityMultiplier = { simple: 4, moderate: 8, complex: 16 };
      return total + complexityMultiplier[req.complexity];
    }, 0);

    // Adjust for experience level
    const experienceMultiplier = profile.experienceLevel === 'beginner' ? 1.5 : 
                                 profile.experienceLevel === 'advanced' ? 0.8 : 1.0;
    
    const adjustedHours = Math.ceil(baseHours * experienceMultiplier);
    
    return {
      totalEstimate: `${Math.ceil(adjustedHours / 8)} days`,
      phases: this.generatePhaseEstimates(adjustedHours),
      risks: this.identifyTimelineRisks(functionalReqs, profile),
      assumptions: [
        'Developer works 8 hours per day',
        'No major technical blockers',
        'Requirements remain stable'
      ]
    };
  }

  // Additional helper methods (simplified implementations)
  private formatFeatureTitle(feature: string): string {
    return feature.split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  private determinePriority(feature: string): 'high' | 'medium' | 'low' {
    const highPriorityKeywords = ['auth', 'login', 'core', 'main', 'primary'];
    return highPriorityKeywords.some(keyword => 
      feature.toLowerCase().includes(keyword)
    ) ? 'high' : 'medium';
  }

  private determineComplexity(feature: string): 'simple' | 'moderate' | 'complex' {
    const complexKeywords = ['integration', 'payment', 'real-time', 'analytics'];
    const simpleKeywords = ['display', 'show', 'list', 'view'];
    
    if (complexKeywords.some(keyword => feature.toLowerCase().includes(keyword))) {
      return 'complex';
    }
    if (simpleKeywords.some(keyword => feature.toLowerCase().includes(keyword))) {
      return 'simple';
    }
    return 'moderate';
  }

  private identifyDependencies(feature: string, existingReqs: FunctionalRequirement[]): string[] {
    // Simplified dependency identification
    return [];
  }

  private categorizeFeature(feature: string): 'core' | 'feature' | 'integration' | 'ui' {
    if (feature.toLowerCase().includes('ui') || feature.toLowerCase().includes('interface')) {
      return 'ui';
    }
    if (feature.toLowerCase().includes('api') || feature.toLowerCase().includes('integration')) {
      return 'integration';
    }
    return 'feature';
  }

  private determineUserRole(req: FunctionalRequirement, discovery: ProjectDiscoveryResult): string {
    if (req.category === 'ui') return 'user';
    if (req.category === 'integration') return 'system administrator';
    return 'user';
  }

  private convertToGoal(description: string): string {
    return `I want to ${description.toLowerCase()}`;
  }

  private identifyBenefit(req: FunctionalRequirement, discovery: ProjectDiscoveryResult): string {
    return `so that I can effectively use the ${discovery.projectType}`;
  }

  private convertPriorityToNumber(priority: 'high' | 'medium' | 'low'): number {
    return { high: 1, medium: 2, low: 3 }[priority];
  }

  private estimateStoryPoints(req: FunctionalRequirement): number {
    const complexityPoints = { simple: 2, moderate: 5, complex: 8 };
    return complexityPoints[req.complexity];
  }

  private generateBasicCriteria(req: FunctionalRequirement): string[] {
    return [
      `The ${req.title.toLowerCase()} functionality is implemented`,
      `The feature works as described in requirements`,
      `No critical errors occur during normal usage`
    ];
  }

  private generateTestScenarios(story: UserStory): TestScenario[] {
    return [{
      given: `I am a ${story.role}`,
      when: `I ${story.goal}`,
      then: `I should ${story.benefit}`,
      priority: 'high'
    }];
  }

  private selectArchitecturePattern(
    discovery: ProjectDiscoveryResult,
    functionalReqs: FunctionalRequirement[]
  ): string {
    if (discovery.projectType === 'web-app') {
      return functionalReqs.length > 10 ? 'microservices' : 'layered';
    }
    return 'component-based';
  }

  private generateComponents(discovery: ProjectDiscoveryResult, pattern: string): ArchitectureComponent[] {
    const components: ArchitectureComponent[] = [];
    
    if (discovery.projectType === 'web-app') {
      components.push({
        name: 'Frontend',
        type: 'frontend',
        purpose: 'User interface and user experience',
        technologies: discovery.techStack.filter(tech => 
          ['React', 'Vue', 'Angular'].includes(tech)
        ),
        interfaces: [{ name: 'API Client', type: 'api', description: 'Communicates with backend' }]
      });

      components.push({
        name: 'Backend API',
        type: 'backend',
        purpose: 'Business logic and data processing',
        technologies: discovery.techStack.filter(tech => 
          ['Node.js', 'Express', 'FastAPI'].includes(tech)
        ),
        interfaces: [{ name: 'REST API', type: 'api', description: 'Serves frontend requests' }]
      });
    }

    return components;
  }

  private generateDataFlow(components: ArchitectureComponent[]): DataFlowDescription[] {
    const dataFlow: DataFlowDescription[] = [];
    
    if (components.length >= 2) {
      dataFlow.push({
        from: 'Frontend',
        to: 'Backend API',
        data: 'User requests',
        method: 'HTTP/REST'
      });

      dataFlow.push({
        from: 'Backend API',
        to: 'Frontend',
        data: 'Response data',
        method: 'HTTP/JSON'
      });
    }

    return dataFlow;
  }

  private identifyIntegrations(discovery: ProjectDiscoveryResult): IntegrationPoint[] {
    const integrations: IntegrationPoint[] = [];
    
    if (discovery.requirements.includes('authentication')) {
      integrations.push({
        name: 'Authentication Service',
        type: 'third-party',
        purpose: 'User authentication and authorization',
        requirements: ['OAuth 2.0 support', 'JWT token handling']
      });
    }

    return integrations;
  }

  private explainArchitectureChoice(
    pattern: string,
    discovery: ProjectDiscoveryResult,
    profile: PersonalProfile
  ): string {
    return `Selected ${pattern} architecture because it suits the ${discovery.projectType} project type, ` +
           `matches the ${profile.experienceLevel} experience level, and provides good scalability for ` +
           `the specified requirements.`;
  }

  private generatePhaseEstimates(totalHours: number): PhaseEstimate[] {
    return [
      {
        phase: 'Planning & Design',
        duration: `${Math.ceil(totalHours * 0.2)} hours`,
        confidence: 'high',
        deliverables: ['Requirements', 'Architecture', 'UI mockups']
      },
      {
        phase: 'Development',
        duration: `${Math.ceil(totalHours * 0.6)} hours`,
        confidence: 'medium',
        deliverables: ['Working application', 'Tests', 'Documentation']
      },
      {
        phase: 'Testing & Deployment',
        duration: `${Math.ceil(totalHours * 0.2)} hours`,
        confidence: 'medium',
        deliverables: ['Deployed application', 'Test results', 'User guide']
      }
    ];
  }

  private identifyTimelineRisks(
    functionalReqs: FunctionalRequirement[],
    profile: PersonalProfile
  ): RiskFactor[] {
    const risks: RiskFactor[] = [];

    const complexRequirements = functionalReqs.filter(req => req.complexity === 'complex');
    if (complexRequirements.length > 0) {
      risks.push({
        risk: 'Complex requirements may take longer than estimated',
        impact: 'high',
        probability: 'medium',
        mitigation: 'Break down complex features into smaller tasks'
      });
    }

    if (profile.experienceLevel === 'beginner') {
      risks.push({
        risk: 'Learning curve may extend development time',
        impact: 'medium',
        probability: 'high',
        mitigation: 'Allocate extra time for learning and provide good documentation'
      });
    }

    return risks;
  }

  private adjustPriorities(
    requirements: FunctionalRequirement[],
    priorityChanges: any
  ): FunctionalRequirement[] {
    // Implementation for adjusting priorities based on feedback
    return requirements;
  }

  private adjustScope(
    requirements: AssembledRequirements,
    scopeChanges: any
  ): AssembledRequirements {
    // Implementation for adjusting scope based on feedback
    return requirements;
  }

  private adjustTechnology(
    architecture: ArchitectureRecommendation,
    technologyChanges: any
  ): ArchitectureRecommendation {
    // Implementation for adjusting technology choices based on feedback
    return architecture;
  }
}