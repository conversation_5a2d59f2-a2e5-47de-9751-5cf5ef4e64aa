/**
 * Code Generation Service
 * Generates complete codebase from specifications and tests
 */

import { injectable } from 'tsyringe';
import { ProjectContext } from './types/project-context.types';
import { 
  SpecificationResult,
  TestSuiteResult,
  CodeGenerationResult,
  CodeBase,
  CodeFile,
  ProjectStructure,
  DependencyManifest,
  ProjectConfiguration
} from './types/generation-results.types';

@injectable()
export class CodeGenerationService {

  /**
   * Generate complete code implementation from specifications and tests
   */
  async generateCodeImplementation(
    approvedSpecs: SpecificationResult,
    approvedTests: TestSuiteResult,
    projectContext: ProjectContext
  ): Promise<CodeGenerationResult> {
    try {
      console.log('💻 [CODE-GEN] Starting code generation...');
      
      const structure = this.generateProjectStructure(projectContext);
      const files = await this.generateCodeFiles(projectContext, approvedSpecs, approvedTests);
      const dependencies = this.generateDependencyManifest(projectContext, approvedTests);
      const configuration = this.generateProjectConfiguration(projectContext);
      const deployment = this.generateDeploymentConfig(projectContext);

      const codeBase: CodeBase = {
        structure,
        files,
        dependencies,
        configuration,
        deployment
      };

      const result: CodeGenerationResult = {
        codeBase,
        metadata: {
          generatedAt: new Date(),
          linesOfCode: this.calculateLinesOfCode(files),
          complexity: this.calculateCodeComplexity(files),
          estimatedDevelopmentTime: this.estimateDevelopmentTime(files)
        }
      };

      console.log(`✅ [CODE-GEN] Code generated: ${files.length} files, ${result.metadata.linesOfCode} LOC`);
      return result;
      
    } catch (error) {
      console.error('❌ [CODE-GEN] Error generating code:', error);
      throw new Error(`Code generation failed: ${error.message}`);
    }
  }

  // Private methods

  private generateProjectStructure(context: ProjectContext): ProjectStructure {
    const directories = this.createDirectoryStructure(context);
    const totalFiles = this.estimateFileCount(context);
    const estimatedSize = this.estimateProjectSize(totalFiles);
    const architecture = this.determineArchitecturePattern(context);

    return {
      directories,
      totalFiles,
      estimatedSize,
      architecture
    };
  }

  private createDirectoryStructure(context: ProjectContext): any[] {
    const baseStructure = [
      {
        path: 'src',
        purpose: 'Source code',
        files: [],
        subdirectories: []
      },
      {
        path: 'tests',
        purpose: 'Test files',
        files: [],
        subdirectories: []
      },
      {
        path: 'docs',
        purpose: 'Documentation',
        files: ['README.md'],
        subdirectories: []
      }
    ];

    if (context.projectDiscovery.projectType === 'web-app') {
      baseStructure[0].subdirectories = [
        {
          path: 'src/components',
          purpose: 'React components',
          files: [],
          subdirectories: []
        },
        {
          path: 'src/services',
          purpose: 'Business logic services',
          files: [],
          subdirectories: []
        },
        {
          path: 'src/utils',
          purpose: 'Utility functions',
          files: [],
          subdirectories: []
        },
        {
          path: 'src/types',
          purpose: 'TypeScript type definitions',
          files: [],
          subdirectories: []
        }
      ];
    }

    return baseStructure;
  }

  private async generateCodeFiles(
    context: ProjectContext,
    specs: SpecificationResult,
    tests: TestSuiteResult
  ): Promise<CodeFile[]> {
    const files: CodeFile[] = [];

    // Generate configuration files
    files.push(...this.generateConfigFiles(context));

    // Generate main application files
    if (context.projectDiscovery.projectType === 'web-app') {
      files.push(...this.generateWebAppFiles(context));
    }

    // Generate feature-specific files
    files.push(...this.generateFeatureFiles(context));

    // Generate service files
    files.push(...this.generateServiceFiles(context));

    // Generate utility files
    files.push(...this.generateUtilityFiles(context));

    // Generate type definition files
    if (context.projectDiscovery.techStack.includes('TypeScript')) {
      files.push(...this.generateTypeFiles(context));
    }

    return files;
  }

  private generateConfigFiles(context: ProjectContext): CodeFile[] {
    const files: CodeFile[] = [];

    // Package.json
    if (context.projectDiscovery.techStack.includes('Node.js') || 
        context.projectDiscovery.techStack.includes('React')) {
      files.push({
        path: 'package.json',
        type: 'config',
        language: 'json',
        content: this.generatePackageJson(context),
        imports: [],
        exports: [],
        description: 'Node.js package configuration',
        complexity: 'low'
      });
    }

    // TypeScript config
    if (context.projectDiscovery.techStack.includes('TypeScript')) {
      files.push({
        path: 'tsconfig.json',
        type: 'config',
        language: 'json',
        content: this.generateTsConfig(context),
        imports: [],
        exports: [],
        description: 'TypeScript configuration',
        complexity: 'low'
      });
    }

    // Environment configuration
    files.push({
      path: '.env.example',
      type: 'config',
      language: 'text',
      content: this.generateEnvExample(context),
      imports: [],
      exports: [],
      description: 'Environment variables template',
      complexity: 'low'
    });

    return files;
  }

  private generateWebAppFiles(context: ProjectContext): CodeFile[] {
    const files: CodeFile[] = [];

    if (context.projectDiscovery.techStack.includes('React')) {
      // Main App component
      files.push({
        path: 'src/App.tsx',
        type: 'component',
        language: 'typescript',
        content: this.generateReactApp(context),
        imports: ['react'],
        exports: ['App'],
        description: 'Main React application component',
        complexity: 'medium'
      });

      // Index file
      files.push({
        path: 'src/index.tsx',
        type: 'component',
        language: 'typescript',
        content: this.generateReactIndex(context),
        imports: ['react', 'react-dom'],
        exports: [],
        description: 'React application entry point',
        complexity: 'low'
      });

      // Generate components for each feature
      context.projectDiscovery.features.forEach(feature => {
        files.push({
          path: `src/components/${this.pascalCase(feature)}/${this.pascalCase(feature)}.tsx`,
          type: 'component',
          language: 'typescript',
          content: this.generateReactComponent(feature, context),
          imports: ['react'],
          exports: [this.pascalCase(feature)],
          description: `React component for ${feature}`,
          complexity: 'medium'
        });
      });
    }

    return files;
  }

  private generateFeatureFiles(context: ProjectContext): CodeFile[] {
    const files: CodeFile[] = [];

    // Generate API service files for each requirement
    context.projectDiscovery.requirements.forEach(requirement => {
      if (requirement !== 'basic functionality') {
        files.push({
          path: `src/services/${this.camelCase(requirement)}.service.ts`,
          type: 'service',
          language: 'typescript',
          content: this.generateServiceClass(requirement, context),
          imports: [],
          exports: [this.pascalCase(requirement) + 'Service'],
          description: `Service for ${requirement}`,
          complexity: 'medium'
        });
      }
    });

    return files;
  }

  private generateServiceFiles(context: ProjectContext): CodeFile[] {
    const files: CodeFile[] = [];

    // API client service
    files.push({
      path: 'src/services/api.service.ts',
      type: 'service',
      language: 'typescript',
      content: this.generateApiService(context),
      imports: [],
      exports: ['ApiService'],
      description: 'Main API service for HTTP requests',
      complexity: 'medium'
    });

    // Configuration service
    files.push({
      path: 'src/services/config.service.ts',
      type: 'service',
      language: 'typescript',
      content: this.generateConfigService(context),
      imports: [],
      exports: ['ConfigService'],
      description: 'Configuration management service',
      complexity: 'low'
    });

    return files;
  }

  private generateUtilityFiles(context: ProjectContext): CodeFile[] {
    const files: CodeFile[] = [];

    // Validation utilities
    files.push({
      path: 'src/utils/validation.ts',
      type: 'util',
      language: 'typescript',
      content: this.generateValidationUtils(context),
      imports: [],
      exports: ['validateEmail', 'validateInput'],
      description: 'Input validation utilities',
      complexity: 'low'
    });

    // Date utilities
    files.push({
      path: 'src/utils/date.ts',
      type: 'util',
      language: 'typescript',
      content: this.generateDateUtils(),
      imports: [],
      exports: ['formatDate', 'parseDate'],
      description: 'Date manipulation utilities',
      complexity: 'low'
    });

    return files;
  }

  private generateTypeFiles(context: ProjectContext): CodeFile[] {
    const files: CodeFile[] = [];

    // Main types file
    files.push({
      path: 'src/types/index.ts',
      type: 'model',
      language: 'typescript',
      content: this.generateMainTypes(context),
      imports: [],
      exports: ['User', 'ApiResponse'],
      description: 'Main TypeScript type definitions',
      complexity: 'low'
    });

    return files;
  }

  // Content generation methods
  private generatePackageJson(context: ProjectContext): string {
    const dependencies: Record<string, string> = {};
    const devDependencies: Record<string, string> = {};

    // Add dependencies based on tech stack
    if (context.projectDiscovery.techStack.includes('React')) {
      dependencies['react'] = '^18.0.0';
      dependencies['react-dom'] = '^18.0.0';
    }

    if (context.projectDiscovery.techStack.includes('TypeScript')) {
      devDependencies['typescript'] = '^4.9.0';
      devDependencies['@types/react'] = '^18.0.0';
      devDependencies['@types/react-dom'] = '^18.0.0';
    }

    // Add testing dependencies
    devDependencies['jest'] = '^29.0.0';
    devDependencies['@testing-library/react'] = '^13.0.0';
    devDependencies['@testing-library/jest-dom'] = '^5.16.0';

    const packageJson = {
      name: context.projectDiscovery.projectType,
      version: '1.0.0',
      description: `A ${context.projectDiscovery.projectType} built with ${context.projectDiscovery.techStack.join(', ')}`,
      main: 'src/index.tsx',
      scripts: {
        start: 'react-scripts start',
        build: 'react-scripts build',
        test: 'jest',
        'test:watch': 'jest --watch',
        'test:coverage': 'jest --coverage'
      },
      dependencies,
      devDependencies,
      author: context.personalProfile.name || 'Developer',
      license: 'MIT'
    };

    return JSON.stringify(packageJson, null, 2);
  }

  private generateTsConfig(context: ProjectContext): string {
    const tsConfig = {
      compilerOptions: {
        target: 'es5',
        lib: ['dom', 'dom.iterable', 'es6'],
        allowJs: true,
        skipLibCheck: true,
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
        strict: true,
        forceConsistentCasingInFileNames: true,
        moduleResolution: 'node',
        resolveJsonModule: true,
        isolatedModules: true,
        noEmit: true,
        jsx: 'react-jsx'
      },
      include: ['src'],
      exclude: ['node_modules']
    };

    return JSON.stringify(tsConfig, null, 2);
  }

  private generateEnvExample(context: ProjectContext): string {
    return `# Environment Configuration
NODE_ENV=development
PORT=3000

# API Configuration
API_BASE_URL=http://localhost:3000/api

# Database Configuration (if needed)
DATABASE_URL=postgresql://localhost:5432/myapp

# Authentication (if needed)
JWT_SECRET=your-secret-key
`;
  }

  private generateReactApp(context: ProjectContext): string {
    const components = context.projectDiscovery.features
      .map(feature => this.pascalCase(feature))
      .join(', ');

    return `import React from 'react';
import './App.css';
${context.projectDiscovery.features.map(feature => 
  `import ${this.pascalCase(feature)} from './components/${this.pascalCase(feature)}/${this.pascalCase(feature)}';`
).join('\n')}

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>${context.projectDiscovery.projectType.replace('-', ' ').toUpperCase()}</h1>
        <p>Welcome to your new application!</p>
      </header>
      <main>
        ${context.projectDiscovery.features.map(feature => 
          `<${this.pascalCase(feature)} />`
        ).join('\n        ')}
      </main>
    </div>
  );
}

export default App;
`;
  }

  private generateReactIndex(context: ProjectContext): string {
    return `import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
`;
  }

  private generateReactComponent(feature: string, context: ProjectContext): string {
    const componentName = this.pascalCase(feature);
    
    return `import React, { useState, useEffect } from 'react';

interface ${componentName}Props {
  // Add props as needed
}

const ${componentName}: React.FC<${componentName}Props> = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize component
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Implement data loading logic
      const result = await fetch('/api/${feature.toLowerCase()}');
      const data = await result.json();
      setData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="${feature.toLowerCase()}-container">
      <h2>${feature}</h2>
      <div className="${feature.toLowerCase()}-content">
        {/* Implement ${feature} UI */}
        <p>This is the ${feature} component.</p>
        {data && (
          <pre>{JSON.stringify(data, null, 2)}</pre>
        )}
      </div>
    </div>
  );
};

export default ${componentName};
`;
  }

  private generateServiceClass(requirement: string, context: ProjectContext): string {
    const serviceName = this.pascalCase(requirement) + 'Service';
    
    return `/**
 * ${serviceName}
 * Handles ${requirement} operations
 */

export class ${serviceName} {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.API_BASE_URL || 'http://localhost:3000/api';
  }

  /**
   * Execute ${requirement} operation
   */
  async execute(data: any): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const response = await fetch(\`\${this.baseUrl}/${requirement.toLowerCase().replace(' ', '-')}\`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error) {
      console.error('${serviceName} error:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  /**
   * Get ${requirement} data
   */
  async getData(id?: string): Promise<any> {
    try {
      const url = id 
        ? \`\${this.baseUrl}/${requirement.toLowerCase().replace(' ', '-')}/\${id}\`
        : \`\${this.baseUrl}/${requirement.toLowerCase().replace(' ', '-')}\`;
      
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(\`HTTP error! status: \${response.status}\`);
      }

      return await response.json();
    } catch (error) {
      console.error('${serviceName} getData error:', error);
      throw error;
    }
  }
}

export default ${serviceName};
`;
  }

  private generateApiService(context: ProjectContext): string {
    return `/**
 * API Service
 * Centralized HTTP client for API communication
 */

export class ApiService {
  private baseUrl: string;
  private token: string | null = null;

  constructor() {
    this.baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';
  }

  setAuthToken(token: string) {
    this.token = token;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = \`Bearer \${this.token}\`;
    }

    return headers;
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(\`GET \${endpoint} failed: \${response.statusText}\`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(\`POST \${endpoint} failed: \${response.statusText}\`);
    }

    return response.json();
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(\`PUT \${endpoint} failed: \${response.statusText}\`);
    }

    return response.json();
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(\`\${this.baseUrl}\${endpoint}\`, {
      method: 'DELETE',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(\`DELETE \${endpoint} failed: \${response.statusText}\`);
    }

    return response.json();
  }
}

export default new ApiService();
`;
  }

  private generateConfigService(context: ProjectContext): string {
    return `/**
 * Configuration Service
 * Manages application configuration
 */

interface AppConfig {
  apiUrl: string;
  environment: string;
  version: string;
  features: {
    [key: string]: boolean;
  };
}

export class ConfigService {
  private config: AppConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): AppConfig {
    return {
      apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:3000/api',
      environment: process.env.NODE_ENV || 'development',
      version: process.env.REACT_APP_VERSION || '1.0.0',
      features: {
        ${context.projectDiscovery.features.map(feature => 
          `${this.camelCase(feature)}: true`
        ).join(',\n        ')}
      }
    };
  }

  get(key: keyof AppConfig): any {
    return this.config[key];
  }

  getFeatureFlag(feature: string): boolean {
    return this.config.features[feature] || false;
  }

  isProduction(): boolean {
    return this.config.environment === 'production';
  }

  isDevelopment(): boolean {
    return this.config.environment === 'development';
  }
}

export default new ConfigService();
`;
  }

  private generateValidationUtils(context: ProjectContext): string {
    return `/**
 * Validation Utilities
 * Common validation functions
 */

export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
  return emailRegex.test(email);
};

export const validateInput = (input: any): boolean => {
  if (!input) return false;
  if (typeof input === 'string') return input.trim().length > 0;
  if (typeof input === 'object') {
    return Object.values(input).every(value => validateInput(value));
  }
  return true;
};

export const validateRequired = (value: any): boolean => {
  return value !== null && value !== undefined && value !== '';
};

export const validateLength = (value: string, min: number, max?: number): boolean => {
  if (!value) return false;
  const length = value.length;
  if (length < min) return false;
  if (max && length > max) return false;
  return true;
};

export const validatePassword = (password: string): boolean => {
  // At least 8 characters, one uppercase, one lowercase, one number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};
`;
  }

  private generateDateUtils(): string {
    return `/**
 * Date Utilities
 * Common date manipulation functions
 */

export const formatDate = (date: Date | string, format = 'YYYY-MM-DD'): string => {
  const d = new Date(date);
  
  if (isNaN(d.getTime())) {
    throw new Error('Invalid date');
  }

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  
  switch (format) {
    case 'YYYY-MM-DD':
      return \`\${year}-\${month}-\${day}\`;
    case 'MM/DD/YYYY':
      return \`\${month}/\${day}/\${year}\`;
    case 'DD/MM/YYYY':
      return \`\${day}/\${month}/\${year}\`;
    default:
      return d.toISOString().split('T')[0];
  }
};

export const parseDate = (dateString: string): Date => {
  return new Date(dateString);
};

export const addDays = (date: Date, days: number): Date => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

export const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.toDateString() === today.toDateString();
};
`;
  }

  private generateMainTypes(context: ProjectContext): string {
    return `/**
 * Main Type Definitions
 */

export interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AppState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

${context.projectDiscovery.features.map(feature => {
  const typeName = this.pascalCase(feature);
  return `export interface ${typeName} {
  id: string;
  name: string;
  // Add ${feature}-specific properties
}`;
}).join('\n\n')}
`;
  }

  // Helper methods
  private generateDependencyManifest(
    context: ProjectContext,
    tests: TestSuiteResult
  ): DependencyManifest {
    const production: any[] = [];
    const development: any[] = [];

    // Add production dependencies based on tech stack
    if (context.projectDiscovery.techStack.includes('React')) {
      production.push(
        { name: 'react', version: '^18.0.0', purpose: 'UI library', category: 'framework' },
        { name: 'react-dom', version: '^18.0.0', purpose: 'React DOM bindings', category: 'framework' }
      );
    }

    // Add development dependencies
    development.push(
      { name: 'jest', version: '^29.0.0', purpose: 'Testing framework', category: 'tool' },
      { name: '@testing-library/react', version: '^13.0.0', purpose: 'React testing utilities', category: 'tool' }
    );

    if (context.projectDiscovery.techStack.includes('TypeScript')) {
      development.push(
        { name: 'typescript', version: '^4.9.0', purpose: 'Type checking', category: 'tool' }
      );
    }

    return {
      production,
      development,
      packageManager: 'npm',
      lockFile: 'package-lock.json'
    };
  }

  private generateProjectConfiguration(context: ProjectContext): ProjectConfiguration {
    return {
      buildSystem: 'create-react-app',
      entryPoints: ['src/index.tsx'],
      outputDirectory: 'build',
      environment: {
        NODE_ENV: 'development',
        REACT_APP_VERSION: '1.0.0'
      },
      scripts: {
        start: 'react-scripts start',
        build: 'react-scripts build',
        test: 'jest',
        eject: 'react-scripts eject'
      },
      linting: {
        enabled: true,
        rules: {},
        ignorePatterns: ['build/', 'node_modules/']
      },
      formatting: {
        enabled: true,
        style: 'prettier',
        config: {}
      }
    };
  }

  private generateDeploymentConfig(context: ProjectContext): any {
    return {
      platforms: [
        {
          name: 'vercel' as const,
          config: {
            buildCommand: 'npm run build',
            outputDirectory: 'build',
            nodeVersion: '18.x'
          },
          status: 'pending' as const
        }
      ],
      environment: 'production' as const,
      scaling: {
        autoScale: true,
        minInstances: 1,
        maxInstances: 10,
        targetMetric: 'cpu'
      },
      monitoring: {
        enabled: true,
        metrics: ['response_time', 'error_rate', 'throughput'],
        alerts: []
      }
    };
  }

  private estimateFileCount(context: ProjectContext): number {
    let count = 5; // Base files (package.json, README, etc.)
    
    count += context.projectDiscovery.features.length; // One component per feature
    count += context.projectDiscovery.requirements.length; // One service per requirement
    count += 3; // Utility files
    
    if (context.projectDiscovery.techStack.includes('TypeScript')) {
      count += 2; // Type definition files
    }
    
    return count;
  }

  private estimateProjectSize(fileCount: number): string {
    const avgFileSize = 50; // Average 50 lines per file
    const totalLines = fileCount * avgFileSize;
    
    if (totalLines < 1000) return 'Small (< 1K LOC)';
    if (totalLines < 5000) return 'Medium (1K-5K LOC)';
    return 'Large (> 5K LOC)';
  }

  private determineArchitecturePattern(context: ProjectContext): any {
    if (context.projectDiscovery.features.length > 10) {
      return 'microservices';
    } else if (context.projectDiscovery.projectType === 'web-app') {
      return 'component-based';
    }
    return 'layered';
  }

  private calculateLinesOfCode(files: CodeFile[]): number {
    return files.reduce((total, file) => {
      return total + file.content.split('\n').length;
    }, 0);
  }

  private calculateCodeComplexity(files: CodeFile[]): number {
    // Simple complexity calculation based on file types and content
    let complexity = 0;
    
    files.forEach(file => {
      switch (file.complexity) {
        case 'low': complexity += 1; break;
        case 'medium': complexity += 3; break;
        case 'high': complexity += 5; break;
      }
    });
    
    return Math.min(10, Math.max(1, Math.round(complexity / files.length)));
  }

  private estimateDevelopmentTime(files: CodeFile[]): string {
    const hoursPerFile = {
      'low': 2,
      'medium': 4,
      'high': 8
    };
    
    const totalHours = files.reduce((total, file) => {
      return total + hoursPerFile[file.complexity];
    }, 0);
    
    const days = Math.ceil(totalHours / 8);
    return `${days} days`;
  }

  // Utility methods
  private pascalCase(str: string): string {
    return str.split(/[\s-_]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }

  private camelCase(str: string): string {
    const pascal = this.pascalCase(str);
    return pascal.charAt(0).toLowerCase() + pascal.slice(1);
  }
}