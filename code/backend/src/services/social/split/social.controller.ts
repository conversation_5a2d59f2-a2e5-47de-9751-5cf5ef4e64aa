/**
 * Social controller for handling social-related API requests
 * Updated to use the split social services
 */
import { Response } from 'express';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../types';
import {
  ChannelService,
  MessageService,
  NotificationService,
} from '.';
import { logger } from '../../../common/logger';
import {
  CreateChannelDto,
  UpdateChannelDto,
  ChannelMemberDto,
  ChannelFilterDto,
  CreateMessageDto,
  UpdateMessageDto,
  MessageReactionDto,
  MessageFilterDto,
  NotificationFilterDto,
} from '../dto';
import { AuthenticatedRequest } from '../../../common/types';

/**
 * Controller for social operations
 */
@injectable()
export class SocialController {
  constructor(
    @inject(TYPES.ChannelService) private channelService: ChannelService,
    @inject(TYPES.MessageService) private messageService: MessageService,
    @inject(TYPES.NotificationService) private notificationService: NotificationService,
  ) {}

  /**
   * Get channels for the current user
   */
  async getUserChannels(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as ChannelFilterDto;
      const channels = await this.channelService.getUserChannels(req.userId, {
        skip: filter.skip,
        take: filter.take,
      });

      res.status(200).json(channels);
    } catch (error) {
      logger.error('Error getting user channels:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get public channels
   */
  async getPublicChannels(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as ChannelFilterDto;
      const channels = await this.channelService.getPublicChannels({
        skip: filter.skip,
        take: filter.take,
      });

      res.status(200).json(channels);
    } catch (error) {
      logger.error('Error getting public channels:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get a channel by ID
   */
  async getChannelById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const channel = await this.channelService.getChannelById(channelId);
      if (!channel) {
        res.status(404).json({ error: 'Channel not found' });
        return;
      }

      res.status(200).json(channel);
    } catch (error) {
      logger.error('Error getting channel by ID:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new channel
   */
  async createChannel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelData: CreateChannelDto = req.body;
      const result = await this.channelService.createChannel({
        name: channelData.name,
        description: channelData.description,
        ownerId: req.userId,
        isPrivate: channelData.isPrivate,
        memberIds: channelData.memberIds,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating channel:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update a channel
   */
  async updateChannel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const channelData: UpdateChannelDto = req.body;
      const result = await this.channelService.updateChannel(channelId, req.userId, {
        name: channelData.name,
        description: channelData.description,
        isPrivate: channelData.isPrivate,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error updating channel:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete a channel
   */
  async deleteChannel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const result = await this.channelService.deleteChannel(channelId, req.userId);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting channel:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Add a member to a channel
   */
  async addChannelMember(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const memberData: ChannelMemberDto = req.body;
      const result = await this.channelService.addChannelMember(
        channelId,
        req.userId,
        memberData.userId,
      );
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error adding channel member:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Remove a member from a channel
   */
  async removeChannelMember(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const userId = parseInt(req.params.userId, 10);
      if (isNaN(userId)) {
        res.status(400).json({ error: 'Invalid user ID' });
        return;
      }

      const result = await this.channelService.removeChannelMember(channelId, req.userId, userId);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error removing channel member:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get messages for a channel
   */
  async getChannelMessages(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const filter = req.query as unknown as MessageFilterDto;
      const result = await this.messageService.getChannelMessages(channelId, req.userId, {
        skip: filter.skip,
        take: filter.take,
        parentOnly: filter.parentOnly,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting channel messages:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get a message by ID
   */
  async getMessageById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        res.status(400).json({ error: 'Invalid message ID' });
        return;
      }

      const result = await this.messageService.getMessageById(messageId, req.userId);
      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error getting message by ID:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Create a new message
   */
  async createMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const channelId = parseInt(req.params.id, 10);
      if (isNaN(channelId)) {
        res.status(400).json({ error: 'Invalid channel ID' });
        return;
      }

      const messageData: CreateMessageDto = req.body;
      const result = await this.messageService.createMessage(channelId, req.userId, {
        content: messageData.content,
        parentId: messageData.parentId,
        attachments: messageData.attachments,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(201).json(result);
    } catch (error) {
      logger.error('Error creating message:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Update a message
   */
  async updateMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        res.status(400).json({ error: 'Invalid message ID' });
        return;
      }

      const messageData: UpdateMessageDto = req.body;
      const result = await this.messageService.updateMessage(messageId, req.userId, {
        content: messageData.content,
        attachments: messageData.attachments,
      });

      if ('statusCode' in result) {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json(result);
    } catch (error) {
      logger.error('Error updating message:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete a message
   */
  async deleteMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        res.status(400).json({ error: 'Invalid message ID' });
        return;
      }

      const result = await this.messageService.deleteMessage(messageId, req.userId);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting message:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Add a reaction to a message
   */
  async addReaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        res.status(400).json({ error: 'Invalid message ID' });
        return;
      }

      const reactionData: MessageReactionDto = req.body;
      const result = await this.messageService.addReaction(
        messageId,
        req.userId,
        reactionData.emoji,
      );
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error adding reaction:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Remove a reaction from a message
   */
  async removeReaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const messageId = parseInt(req.params.id, 10);
      if (isNaN(messageId)) {
        res.status(400).json({ error: 'Invalid message ID' });
        return;
      }

      const emoji = req.params.emoji;
      if (!emoji) {
        res.status(400).json({ error: 'Emoji is required' });
        return;
      }

      const result = await this.messageService.removeReaction(messageId, req.userId, emoji);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error removing reaction:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Get notifications for the current user
   */
  async getUserNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filter = req.query as unknown as NotificationFilterDto;
      const notifications = await this.notificationService.getUserNotifications(req.userId, {
        skip: filter.skip,
        take: filter.take,
        unreadOnly: filter.unreadOnly,
      });

      res.status(200).json(notifications);
    } catch (error) {
      logger.error('Error getting user notifications:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Mark a notification as read
   */
  async markNotificationAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const notificationId = parseInt(req.params.id, 10);
      if (isNaN(notificationId)) {
        res.status(400).json({ error: 'Invalid notification ID' });
        return;
      }

      const result = await this.notificationService.markNotificationAsRead(
        notificationId,
        req.userId,
      );
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllNotificationsAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const result = await this.notificationService.markAllNotificationsAsRead(req.userId);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(200).json({ success: true });
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete a notification
   */
  async deleteNotification(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const notificationId = parseInt(req.params.id, 10);
      if (isNaN(notificationId)) {
        res.status(400).json({ error: 'Invalid notification ID' });
        return;
      }

      const result = await this.notificationService.deleteNotification(notificationId, req.userId);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting notification:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Delete all notifications for the current user
   */
  async deleteAllNotifications(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const result = await this.notificationService.deleteAllNotifications(req.userId);
      if (typeof result !== 'boolean') {
        res.status(result.statusCode).json({ error: result.detail });
        return;
      }

      res.status(204).end();
    } catch (error) {
      logger.error('Error deleting all notifications:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
