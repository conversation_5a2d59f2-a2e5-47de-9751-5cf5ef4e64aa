import { injectable } from 'inversify';
import { BaseService } from './base.service';
import { CodeHealthReport, CodeIssue, FileMetrics } from './code-analysis.service';
import { GitAnalysis } from './agent/git.service';
import { MermaidDiagram } from './mermaid-diagram.service';

export interface BrutalHonestyMessage {
  category: 'security' | 'performance' | 'quality' | 'documentation' | 'error_handling' | 'complexity';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  brutalmessage: string;
  helpfulGuidance: string;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  emoji: string;
  readinessImpact: number; // Points that improve readiness when fixed
}

export interface DetailedMetrics {
  complexityScore: number;
  maintainability: number;
  technicalDebt: number;
  testCoverage: number;
  securityScore: number;
  performanceScore: number;
}

export interface BrutalHonestyReport {
  productionReadiness: number;
  overallGrade: string;
  messages: BrutalHonestyMessage[];
  encouragement: string;
  nextSteps: string[];
  timeToFix: string;
  funFacts: string[];
  detailedMetrics: DetailedMetrics;
  architectureDiagram?: string;
  complexityDiagram?: string;
  gitFlowDiagram?: string;
}

@injectable()
export class BrutalHonestyMessagingService extends BaseService {
  
  private securityMessages = [
    {
      pattern: 'hardcoded_credentials',
      messages: [
        "Your API keys are more exposed than a nudist at a polar bear convention",
        "I found your secret keys. Spoiler alert: they're not that secret anymore",
        "Your authentication is like a screen door on a submarine",
        "SQL injection vulnerability detected. The 1990s called, they want their security flaws back",
        "Your JWT secret is 'secret'. I can't even...",
        "Password stored in plain text? What's next, writing your PIN on your forehead?"
      ]
    },
    {
      pattern: 'missing_validation',
      messages: [
        "Input validation is optional, just like wearing pants to a job interview",
        "Your API accepts anything. It's like a digital buffet with no quality control",
        "No input sanitization? That's a bold strategy, Cotton"
      ]
    }
  ];

  private performanceMessages = [
    {
      pattern: 'large_bundle',
      messages: [
        "Your bundle size could sink the Titanic",
        "Loading 50MB for a todo app? Did you include the entire internet?",
        "This loads slower than dial-up internet. Remember that?",
        "You're importing 200KB of lodash to use one function. That's like buying a cow for a glass of milk"
      ]
    },
    {
      pattern: 'inefficient_loops',
      messages: [
        "Your loops are more nested than Russian dolls",
        "O(n²) complexity? In this economy?",
        "Your algorithm is slower than government bureaucracy"
      ]
    }
  ];

  private qualityMessages = [
    {
      pattern: 'high_complexity',
      messages: [
        "I've seen spaghetti code before, but this is the whole Italian restaurant",
        "This function does 17 things. That's 16 too many",
        "Your variable names look like someone fell asleep on the keyboard",
        "This code is more tangled than Christmas lights in storage",
        "Function complexity: 45. My brain complexity: also 45 after reading this"
      ]
    },
    {
      pattern: 'poor_naming',
      messages: [
        "Variable names like 'x1', 'temp', 'data'? Did you run out of creativity?",
        "I've seen more descriptive names on anonymous functions",
        "Your variable names are about as helpful as a chocolate teapot"
      ]
    }
  ];

  private documentationMessages = [
    {
      pattern: 'missing_docs',
      messages: [
        "Documentation coverage: 5%. That's not coverage, that's a rounding error",
        "Your README promises OAuth, but you built basic auth. Truth in advertising much?",
        "API docs are writing checks your code can't cash",
        "README says 'blazing fast' - reality says 'glacial pace'",
        "You documented 5 endpoints. You built 12. Math is hard.",
        "Your code comments are rarer than honest politicians"
      ]
    }
  ];

  private errorHandlingMessages = [
    {
      pattern: 'missing_error_handling',
      messages: [
        "Error handling? What error handling? Your app crashes faster than my hopes and dreams",
        "23 unhandled promise rejections. That's not a bug, that's a feature",
        "Your app will crash if someone sneezes near the server",
        "Error boundaries? Never heard of them. Apparently neither have you",
        "Your try-catch coverage is like my dating life: non-existent"
      ]
    }
  ];

  private encouragementMessages = [
    "Don't worry, we'll fix this together in about 15 minutes",
    "I've seen worse. Let me show you how to make it better",
    "This is actually a common mistake. Here's the fix...",
    "Every expert was once a beginner. You're just... really beginning",
    "Your code has potential. Like a caterpillar has potential to be a butterfly",
    "Rome wasn't built in a day, but they were laying bricks every hour",
    "Think of this as a chance to become a better developer. Character building!"
  ];

  private celebrationMessages = [
    "Your app is now more production-ready than 73% of apps in the wild!",
    "You've gone from 'please don't crash' to 'actually deployable'!",
    "Congratulations! Your code no longer makes me want to hide under a blanket",
    "From dumpster fire to campfire - nice work!",
    "You've transformed spaghetti code into a well-organized Italian restaurant"
  ];

  generateBrutalHonestyReport(
    healthReport: CodeHealthReport,
    fileMetrics: FileMetrics[],
    mermaidDiagrams?: MermaidDiagram[]
  ): BrutalHonestyReport {
    const messages = this.generateBrutalMessages(healthReport, fileMetrics);
    const readinessScore = this.calculateReadinessScore(healthReport, messages);
    const overallGrade = this.calculateOverallGrade(readinessScore);
    
    // Extract diagram content
    const architectureDiagram = mermaidDiagrams?.find(d => d.type === 'architecture')?.diagram;
    const complexityDiagram = mermaidDiagrams?.find(d => d.type === 'complexity')?.diagram;
    const gitFlowDiagram = mermaidDiagrams?.find(d => d.type === 'git_flow')?.diagram;
    
    return {
      productionReadiness: readinessScore,
      overallGrade,
      messages,
      encouragement: this.getRandomEncouragement(),
      nextSteps: this.generateNextSteps(messages, healthReport.gitAnalysis),
      timeToFix: this.estimateTimeToFix(messages),
      funFacts: this.generateFunFacts(healthReport, fileMetrics),
      detailedMetrics: this.generateDetailedMetrics(healthReport, fileMetrics, messages),
      architectureDiagram,
      complexityDiagram,
      gitFlowDiagram
    };
  }

  private generateBrutalMessages(
    healthReport: CodeHealthReport,
    fileMetrics: FileMetrics[]
  ): BrutalHonestyMessage[] {
    const messages: BrutalHonestyMessage[] = [];

    // Git/Version Control issues
    if (healthReport.gitAnalysis) {
      const gitMessage = this.generateGitMessage(healthReport.gitAnalysis);
      if (gitMessage) {
        messages.push(gitMessage);
      }
    }

    // Security issues
    if (this.hasSecurityIssues(healthReport)) {
      messages.push(this.generateSecurityMessage(healthReport));
    }

    // Performance issues
    if (this.hasPerformanceIssues(fileMetrics)) {
      messages.push(this.generatePerformanceMessage(fileMetrics));
    }

    // Code quality issues
    if (this.hasQualityIssues(healthReport)) {
      messages.push(this.generateQualityMessage(healthReport));
    }

    // Documentation issues
    if (this.hasDocumentationIssues(healthReport)) {
      messages.push(this.generateDocumentationMessage(healthReport));
    }

    // Error handling issues
    if (this.hasErrorHandlingIssues(healthReport)) {
      messages.push(this.generateErrorHandlingMessage(healthReport));
    }

    // If everything is good, add a positive message
    if (messages.length === 0) {
      messages.push({
        category: 'quality',
        severity: 'info',
        title: 'Code Quality',
        brutalmessage: "Hey, at least this part works! You've got a solid foundation here.",
        helpfulGuidance: "Your code is in good shape. Consider adding some advanced patterns or optimization.",
        grade: 'B',
        emoji: '🟢',
        readinessImpact: 0
      });
    }

    return messages;
  }

  private hasSecurityIssues(healthReport: CodeHealthReport): boolean {
    return healthReport.issues.some(issue => 
      issue.patternType.includes('security') || 
      issue.patternType.includes('credential') ||
      issue.patternType.includes('injection')
    );
  }

  private hasPerformanceIssues(fileMetrics: FileMetrics[]): boolean {
    // Check for large files or high complexity
    return fileMetrics.some(file => 
      file.lineCount > 1000 || 
      file.complexity > 30 ||
      file.functions.some(func => func.complexity > 20)
    );
  }

  private hasQualityIssues(healthReport: CodeHealthReport): boolean {
    return healthReport.complexityScore < 70 || 
           healthReport.maintainability < 60 ||
           healthReport.issues.some(issue => issue.severity === 'critical');
  }

  private hasDocumentationIssues(healthReport: CodeHealthReport): boolean {
    // Assume documentation issues if maintainability is low
    return healthReport.maintainability < 50;
  }

  private hasErrorHandlingIssues(healthReport: CodeHealthReport): boolean {
    return healthReport.issues.some(issue => 
      issue.patternType.includes('error') ||
      issue.patternType.includes('exception') ||
      issue.patternType.includes('missing_error_handling')
    );
  }

  private generateSecurityMessage(healthReport: CodeHealthReport): BrutalHonestyMessage {
    const messages = this.securityMessages[0].messages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    return {
      category: 'security',
      severity: 'critical',
      title: 'Security',
      brutalmessage: randomMessage,
      helpfulGuidance: "Move sensitive data to environment variables, add input validation, and implement proper authentication.",
      grade: 'F',
      emoji: '🔴',
      readinessImpact: 30
    };
  }

  private generatePerformanceMessage(fileMetrics: FileMetrics[]): BrutalHonestyMessage {
    const messages = this.performanceMessages[0].messages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    return {
      category: 'performance',
      severity: 'warning',
      title: 'Performance',
      brutalmessage: randomMessage,
      helpfulGuidance: "Optimize your imports, reduce bundle size, and simplify complex algorithms.",
      grade: 'D',
      emoji: '🟡',
      readinessImpact: 20
    };
  }

  private generateQualityMessage(healthReport: CodeHealthReport): BrutalHonestyMessage {
    const messages = this.qualityMessages[0].messages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    const severity = healthReport.complexityScore < 30 ? 'critical' : 
                    healthReport.complexityScore < 50 ? 'error' : 'warning';
    
    return {
      category: 'quality',
      severity: severity as any,
      title: 'Code Quality',
      brutalmessage: randomMessage,
      helpfulGuidance: "Break down complex functions, improve variable names, and follow coding standards.",
      grade: healthReport.complexityScore < 30 ? 'F' : healthReport.complexityScore < 50 ? 'D' : 'C',
      emoji: healthReport.complexityScore < 30 ? '🔴' : healthReport.complexityScore < 50 ? '🟠' : '🟡',
      readinessImpact: 25
    };
  }

  private generateDocumentationMessage(healthReport: CodeHealthReport): BrutalHonestyMessage {
    const messages = this.documentationMessages[0].messages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    return {
      category: 'documentation',
      severity: 'warning',
      title: 'Documentation',
      brutalmessage: randomMessage,
      helpfulGuidance: "Add function documentation, update your README, and keep docs in sync with code.",
      grade: 'D',
      emoji: '🟠',
      readinessImpact: 15
    };
  }

  private generateErrorHandlingMessage(healthReport: CodeHealthReport): BrutalHonestyMessage {
    const messages = this.errorHandlingMessages[0].messages;
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    
    return {
      category: 'error_handling',
      severity: 'error',
      title: 'Error Handling',
      brutalmessage: randomMessage,
      helpfulGuidance: "Add try-catch blocks, implement error boundaries, and handle promise rejections.",
      grade: 'F',
      emoji: '🔴',
      readinessImpact: 35
    };
  }

  private generateGitMessage(gitAnalysis: GitAnalysis): BrutalHonestyMessage | null {
    if (!gitAnalysis?.driftAnalysis?.hasUncommittedChanges) {
      return null; // No git issues to report
    }

    const { uncommittedChanges, driftAnalysis } = gitAnalysis;
    const { riskLevel, changedFiles } = driftAnalysis;

    const gitMessages = {
      low: [
        `${uncommittedChanges} uncommitted files. Not terrible, but let's clean up the workspace`,
        "You've got some uncommitted changes floating around. Time to commit or abandon ship",
        "Your git status is messier than a teenager's bedroom"
      ],
      medium: [
        `${uncommittedChanges} uncommitted files? That's a lot of loose ends`,
        "Your workspace is busier than a beehive. Time to commit some changes",
        "Git status looking like a grocery list. Let's organize this chaos"
      ],
      high: [
        `${uncommittedChanges} uncommitted files! Your workspace is in full rebellion`,
        "Your git status is more chaotic than a food fight. Core files are modified!",
        "This many uncommitted changes? Your future self will not thank you"
      ]
    };

    const messages = gitMessages[riskLevel];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];

    const severity = riskLevel === 'high' ? 'error' : riskLevel === 'medium' ? 'warning' : 'info';
    const grade = riskLevel === 'high' ? 'F' : riskLevel === 'medium' ? 'D' : 'C';
    const emoji = riskLevel === 'high' ? '🔴' : riskLevel === 'medium' ? '🟠' : '🟡';
    const impact = riskLevel === 'high' ? 25 : riskLevel === 'medium' ? 15 : 5;

    return {
      category: 'documentation', // Git issues affect documentation/drift
      severity: severity as any,
      title: 'Version Control',
      brutalmessage: randomMessage,
      helpfulGuidance: `Commit your changes, review modified files, and clean up your workspace. Focus on: ${changedFiles.slice(0, 3).join(', ')}`,
      grade: grade as any,
      emoji,
      readinessImpact: impact
    };
  }

  private calculateReadinessScore(
    healthReport: CodeHealthReport, 
    messages: BrutalHonestyMessage[]
  ): number {
    // Start with base health score
    let score = healthReport.overallScore;
    
    // Apply penalties for critical issues
    const criticalIssues = messages.filter(m => m.severity === 'critical').length;
    const errorIssues = messages.filter(m => m.severity === 'error').length;
    const warningIssues = messages.filter(m => m.severity === 'warning').length;
    
    score -= (criticalIssues * 30);
    score -= (errorIssues * 20);
    score -= (warningIssues * 10);
    
    // Ensure score is within bounds
    return Math.max(0, Math.min(100, score));
  }

  private calculateOverallGrade(readinessScore: number): string {
    if (readinessScore >= 90) return 'A';
    if (readinessScore >= 80) return 'B';
    if (readinessScore >= 70) return 'C';
    if (readinessScore >= 60) return 'D';
    return 'F';
  }

  private getRandomEncouragement(): string {
    return this.encouragementMessages[Math.floor(Math.random() * this.encouragementMessages.length)];
  }

  private generateNextSteps(messages: BrutalHonestyMessage[], gitAnalysis?: GitAnalysis): string[] {
    const steps: string[] = [];
    
    // Prioritize by severity and impact
    const sortedMessages = messages.sort((a, b) => {
      const severityOrder = { critical: 4, error: 3, warning: 2, info: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });

    let stepNumber = 1;
    for (const message of sortedMessages.slice(0, 4)) {
      steps.push(`${stepNumber}️⃣ ${message.title}: ${message.helpfulGuidance}`);
      stepNumber++;
    }

    // Add git-specific step if there are uncommitted changes
    if (gitAnalysis?.driftAnalysis?.hasUncommittedChanges) {
      const { uncommittedChanges, driftAnalysis } = gitAnalysis;
      const timeEstimate = uncommittedChanges > 20 ? '30 min' : uncommittedChanges > 10 ? '15 min' : '5 min';
      steps.push(`🔄 Git Cleanup (${timeEstimate}): Review and commit ${uncommittedChanges} uncommitted files`);
    }

    if (steps.length === 0) {
      steps.push("🎉 Your code is looking good! Consider adding advanced optimizations.");
    }

    return steps;
  }

  private estimateTimeToFix(messages: BrutalHonestyMessage[]): string {
    const totalImpact = messages.reduce((sum, msg) => sum + msg.readinessImpact, 0);
    
    if (totalImpact < 30) return "1-2 hours";
    if (totalImpact < 60) return "2-4 hours";
    if (totalImpact < 90) return "4-6 hours";
    return "6+ hours (but worth it!)";
  }

  private generateFunFacts(
    healthReport: CodeHealthReport, 
    fileMetrics: FileMetrics[]
  ): string[] {
    const facts: string[] = [];
    
    const totalLines = healthReport.metrics?.totalLines || 0;
    const totalFunctions = healthReport.metrics?.totalFunctions || 0;
    
    if (totalLines > 1000) {
      facts.push(`You've written ${totalLines} lines of code. That's ${Math.round(totalLines / 50)} pages of a book!`);
    }
    
    if (totalFunctions > 20) {
      facts.push(`${totalFunctions} functions! You could start your own function library.`);
    }
    
    if (healthReport.complexityScore > 80) {
      facts.push("Your code complexity is better than 80% of developers. Nice work!");
    }
    
    const asyncFunctions = fileMetrics.reduce((sum, file) => 
      sum + file.functions.filter(func => func.isAsync).length, 0
    );
    
    if (asyncFunctions > 5) {
      facts.push(`${asyncFunctions} async functions. You're living in the future!`);
    }
    
    if (facts.length === 0) {
      facts.push("Every line of code is a step towards greatness!");
    }
    
    return facts;
  }

  generateProgressMessage(
    oldScore: number, 
    newScore: number, 
    fixedIssues: string[]
  ): string {
    const improvement = newScore - oldScore;
    const messages = [
      `Nice! Your readiness improved by ${improvement}% (+${improvement}% today!)`,
      `You're on fire! Code health up ${improvement}%`,
      `Progress! Your app is ${improvement}% more awesome`,
      `Boom! Another ${improvement}% towards production readiness`
    ];
    
    return messages[Math.floor(Math.random() * messages.length)];
  }

  generateCelebrationMessage(finalScore: number): string {
    if (finalScore >= 90) {
      return "🎉 You're a code wizard! This is production-ready stuff!";
    }
    if (finalScore >= 80) {
      return this.celebrationMessages[Math.floor(Math.random() * this.celebrationMessages.length)];
    }
    if (finalScore >= 70) {
      return "Good progress! You're well on your way to production-ready code.";
    }
    return "Every improvement counts! Keep up the good work!";
  }

  private generateDetailedMetrics(
    healthReport: CodeHealthReport,
    fileMetrics: FileMetrics[],
    messages: BrutalHonestyMessage[]
  ): DetailedMetrics {
    // Calculate test coverage based on project structure and common test patterns
    const testCoverage = this.calculateTestCoverage(fileMetrics, healthReport);
    
    // Calculate security score based on security issues found
    const securityScore = this.calculateSecurityScore(healthReport, messages);
    
    // Calculate performance score based on file sizes and complexity
    const performanceScore = this.calculatePerformanceScore(fileMetrics, healthReport);
    
    // Use existing health report scores or calculate defaults
    const complexityScore = healthReport.complexityScore || this.calculateComplexityScore(fileMetrics);
    const maintainability = healthReport.maintainability || this.calculateMaintainabilityScore(healthReport, messages);
    const technicalDebt = Math.max(0, 100 - healthReport.overallScore);
    
    return {
      complexityScore: Math.round(complexityScore),
      maintainability: Math.round(maintainability),
      technicalDebt: Math.round(technicalDebt),
      testCoverage: Math.round(testCoverage),
      securityScore: Math.round(securityScore),
      performanceScore: Math.round(performanceScore)
    };
  }

  private calculateTestCoverage(fileMetrics: FileMetrics[], healthReport: CodeHealthReport): number {
    // Look for test files and calculate estimated coverage
    const totalFiles = fileMetrics.length;
    if (totalFiles === 0) return 0;
    
    // Count test files (files containing 'test', 'spec', or in test directories)
    const testFileCount = fileMetrics.filter(file => {
      const fileName = file.filePath.toLowerCase();
      return fileName.includes('test') || 
             fileName.includes('spec') || 
             fileName.includes('__tests__') ||
             fileName.includes('.test.') ||
             fileName.includes('.spec.');
    }).length;
    
    // Estimate coverage based on test file ratio
    const testRatio = testFileCount / totalFiles;
    let estimatedCoverage = Math.min(testRatio * 200, 100); // Generous estimation
    
    // Reduce coverage if there are missing error handling issues
    const errorHandlingIssues = healthReport.issues.filter(issue => 
      issue.patternType.includes('error') || issue.patternType.includes('exception')
    ).length;
    
    if (errorHandlingIssues > 0) {
      estimatedCoverage = Math.max(0, estimatedCoverage - (errorHandlingIssues * 5));
    }
    
    return Math.max(5, estimatedCoverage); // Minimum 5% if any tests exist
  }

  private calculateSecurityScore(healthReport: CodeHealthReport, messages: BrutalHonestyMessage[]): number {
    let securityScore = 85; // Start with good baseline
    
    // Reduce score for security issues
    const securityIssues = healthReport.issues.filter(issue => 
      issue.patternType.includes('security') || 
      issue.patternType.includes('credential') ||
      issue.patternType.includes('injection')
    );
    
    // Reduce score based on issue severity
    securityIssues.forEach(issue => {
      switch (issue.severity) {
        case 'critical': securityScore -= 25; break;
        case 'error': securityScore -= 15; break;
        case 'warning': securityScore -= 10; break;
        default: securityScore -= 5;
      }
    });
    
    // Check if there's a security message indicating issues
    const hasSecurityMessage = messages.some(msg => msg.category === 'security');
    if (hasSecurityMessage) {
      securityScore = Math.min(securityScore, 60); // Cap at 60% if there are security messages
    }
    
    return Math.max(10, securityScore);
  }

  private calculatePerformanceScore(fileMetrics: FileMetrics[], healthReport: CodeHealthReport): number {
    let performanceScore = 80; // Start with good baseline
    
    // Check for large files
    const largeFiles = fileMetrics.filter(file => file.lineCount > 1000).length;
    if (largeFiles > 0) {
      performanceScore -= Math.min(largeFiles * 10, 30);
    }
    
    // Check for high complexity functions
    const highComplexityFunctions = fileMetrics.reduce((count, file) => 
      count + file.functions.filter(func => func.complexity > 20).length, 0
    );
    
    if (highComplexityFunctions > 0) {
      performanceScore -= Math.min(highComplexityFunctions * 5, 25);
    }
    
    // Factor in overall complexity score
    if (healthReport.complexityScore < 50) {
      performanceScore -= 20;
    } else if (healthReport.complexityScore < 70) {
      performanceScore -= 10;
    }
    
    return Math.max(20, performanceScore);
  }

  private calculateComplexityScore(fileMetrics: FileMetrics[]): number {
    if (fileMetrics.length === 0) return 50;
    
    const avgComplexity = fileMetrics.reduce((sum, file) => {
      const fileAvgComplexity = file.functions.length > 0 
        ? file.functions.reduce((fSum, func) => fSum + func.complexity, 0) / file.functions.length
        : file.complexity || 1;
      return sum + fileAvgComplexity;
    }, 0) / fileMetrics.length;
    
    // Convert complexity to score (lower complexity = higher score)
    return Math.max(10, Math.min(100, 100 - (avgComplexity * 2)));
  }

  private calculateMaintainabilityScore(healthReport: CodeHealthReport, messages: BrutalHonestyMessage[]): number {
    let maintainabilityScore = 75; // Start with reasonable baseline
    
    // Reduce score for quality issues
    const qualityMessages = messages.filter(msg => 
      msg.category === 'quality' || msg.category === 'documentation'
    );
    
    qualityMessages.forEach(msg => {
      maintainabilityScore -= msg.readinessImpact;
    });
    
    // Factor in overall health
    if (healthReport.overallScore < 50) {
      maintainabilityScore -= 20;
    } else if (healthReport.overallScore < 70) {
      maintainabilityScore -= 10;
    }
    
    return Math.max(15, maintainabilityScore);
  }
}

export default BrutalHonestyMessagingService;