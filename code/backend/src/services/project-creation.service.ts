/**
 * Project Creation Service - Refactored
 * Now delegates to the new ProjectCreationOrchestrator while maintaining API compatibility
 */

import { injectable } from 'tsyringe';
import { ProjectCreationOrchestrator, ProjectResult } from './project-creation-orchestrator.service';

// Keep existing interfaces for API compatibility
export interface ProjectCreationResult {
  project: GeneratedProject;
  qualityScore: number;
  timeToComplete: string;
  readyForDeployment: boolean;
}

export interface GeneratedProject {
  name: string;
  type: string;
  description: string;
  path: string;
  structure: ProjectStructure;
  quality: QualityMetrics;
  deployment: DeploymentConfig;
}

export interface ProjectStructure {
  documentation: string[];
  slides: string[];
  tests: string[];
  code: {
    backend: string[];
    frontend: string[];
    database: string[];
  };
  config: string[];
  deployment: string[];
}

export interface QualityMetrics {
  codeQuality: number;
  testCoverage: number;
  documentation: number;
  security: number;
  performance: number;
}

export interface DeploymentConfig {
  docker: boolean;
  kubernetes: boolean;
  serverless: boolean;
  platforms: string[];
}

@injectable()
export class ProjectCreationService {
  constructor(
    private orchestrator: ProjectCreationOrchestrator
  ) {}

  /**
   * Main project creation method - now uses orchestrated approach
   */
  async createProject(userId: string, input: string): Promise<ProjectCreationResult> {
    try {
      console.log('🎯 [PROJECT-CREATION] Using new orchestrated approach...');
      
      // Delegate to the new orchestrator
      const result = await this.orchestrator.createProject(userId, input);
      
      // Convert to legacy format for API compatibility
      return this.convertToLegacyFormat(result);
      
    } catch (error) {
      console.error('❌ [PROJECT-CREATION] Error creating project:', error);
      throw new Error(`Project creation failed: ${error.message}`);
    }
  }

  /**
   * Resume project creation
   */
  async resumeProject(userId: string): Promise<any> {
    try {
      const resumeResult = await this.orchestrator.resumeProject(userId);
      
      if (!resumeResult.resumed) {
        return null;
      }
      
      return {
        canResume: true,
        resumePoint: resumeResult.resumePoint,
        context: resumeResult.context,
        nextPhase: resumeResult.nextPhase
      };
      
    } catch (error) {
      console.error('❌ [PROJECT-CREATION] Error resuming project:', error);
      return null;
    }
  }

  /**
   * Get project creation progress
   */
  async getProgress(projectId: string): Promise<any> {
    try {
      return await this.orchestrator.getProjectProgress(projectId);
    } catch (error) {
      console.error('❌ [PROJECT-CREATION] Error getting progress:', error);
      return null;
    }
  }

  /**
   * Cancel project creation
   */
  async cancelProject(projectId: string): Promise<boolean> {
    try {
      return await this.orchestrator.cancelProject(projectId);
    } catch (error) {
      console.error('❌ [PROJECT-CREATION] Error cancelling project:', error);
      return false;
    }
  }

  // Legacy methods for backward compatibility - now deprecated
  async generateDocumentation(): Promise<any> {
    console.warn('⚠️ [DEPRECATED] generateDocumentation is deprecated. Use createProject() instead.');
    return { message: 'This method is deprecated. Use createProject() for full project generation.' };
  }

  async generateSlides(): Promise<any> {
    console.warn('⚠️ [DEPRECATED] generateSlides is deprecated. Use createProject() instead.');
    return { message: 'This method is deprecated. Use createProject() for full project generation.' };
  }

  async generateTests(): Promise<any> {
    console.warn('⚠️ [DEPRECATED] generateTests is deprecated. Use createProject() instead.');
    return { message: 'This method is deprecated. Use createProject() for full project generation.' };
  }

  async generateCode(): Promise<any> {
    console.warn('⚠️ [DEPRECATED] generateCode is deprecated. Use createProject() instead.');
    return { message: 'This method is deprecated. Use createProject() for full project generation.' };
  }

  // Private helper methods
  private convertToLegacyFormat(result: ProjectResult): ProjectCreationResult {
    return {
      project: {
        name: result.projectId,
        type: 'AI Generated Project',
        description: 'Project created using KAPI backwards build methodology',
        path: '/generated-project',
        structure: this.extractProjectStructure(result),
        quality: this.extractQualityMetrics(result),
        deployment: this.extractDeploymentConfig(result)
      },
      qualityScore: result.results.qualityScore || 85,
      timeToComplete: this.formatDuration(result.metadata.totalDuration),
      readyForDeployment: result.status === 'completed' && (result.results.qualityScore || 0) >= 80
    };
  }

  private extractProjectStructure(result: ProjectResult): ProjectStructure {
    const structure: ProjectStructure = {
      documentation: [],
      slides: [],
      tests: [],
      code: {
        backend: [],
        frontend: [],
        database: []
      },
      config: [],
      deployment: []
    };

    // Extract from specifications
    if (result.results.specifications) {
      structure.documentation = [
        'README.md',
        'API_DOCS.md',
        'ARCHITECTURE.md',
        'SETUP_GUIDE.md',
        'CONTRIBUTION_GUIDE.md'
      ];
      
      structure.slides = [
        'executive-overview.slides',
        'technical-deep-dive.slides',
        'demo-script.md'
      ];
    }

    // Extract from tests
    if (result.results.tests) {
      const testSuite = result.results.tests.testSuite;
      structure.tests = [
        `unit-tests/ (${testSuite.unitTests.count} tests)`,
        `integration-tests/ (${testSuite.integrationTests.count} tests)`,
        `e2e-tests/ (${testSuite.e2eTests.count} tests)`,
        'test-config/'
      ];
    }

    // Extract from code
    if (result.results.code) {
      const files = result.results.code.codeBase.files;
      
      structure.code.frontend = files
        .filter(f => f.type === 'component')
        .map(f => f.path);
        
      structure.code.backend = files
        .filter(f => f.type === 'service')
        .map(f => f.path);
        
      structure.config = files
        .filter(f => f.type === 'config')
        .map(f => f.path);
    }

    // Add deployment files
    structure.deployment = [
      'docker-compose.yml',
      'Dockerfile',
      '.env.example',
      'railway.toml',
      'vercel.json'
    ];

    return structure;
  }

  private extractQualityMetrics(result: ProjectResult): QualityMetrics {
    const baseScore = result.results.qualityScore || 85;
    
    return {
      codeQuality: baseScore,
      testCoverage: result.results.tests ? 
        result.results.tests.testSuite.coverage.estimated : 80,
      documentation: 90, // High due to backwards build approach
      security: Math.max(70, baseScore - 10), // Security typically slightly lower
      performance: Math.max(75, baseScore - 5) // Performance baseline
    };
  }

  private extractDeploymentConfig(result: ProjectResult): DeploymentConfig {
    const hasDeployment = !!result.results.deploymentUrl;
    
    return {
      docker: true,
      kubernetes: false,
      serverless: true,
      platforms: hasDeployment ? 
        ['Railway', 'Vercel'] : 
        ['Railway', 'Vercel', 'Netlify', 'AWS', 'GCP']
    };
  }

  private formatDuration(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }
}