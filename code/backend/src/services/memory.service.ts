/**
 * Memory Service for KAPI's AI Memory System
 * 
 * This service manages the five-layer memory architecture:
 * 1. Personal Context (localStorage) - User preferences across projects
 * 2. Business Context (.kapi/memory) - Project business goals and context
 * 3. Technical Context (.kapi/memory) - Architecture, stack, patterns
 * 4. Code Context (.kapi/memory) - File structure, functions, changes
 * 5. Task Context (.kapi/memory) - Current work, progress, blockers
 */

import { injectable, inject } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { logger } from '../common/logger';
import { TYPES } from '../types';
import UnifiedConversationService from './unified-conversation.service';
import { modelUsageService } from './model-usage.service';
import path from 'path';
import fs from 'fs/promises';
import { existsSync } from 'fs';

// Memory interfaces
export interface PersonalContext {
  userId: string;
  codingStyle: {
    preferred_patterns: string[];
    language_preferences: string[];
    framework_preferences: string[];
    code_style_notes: string;
  };
  communication_style: {
    feedback_preference: 'direct' | 'detailed' | 'encouraging';
    explanation_level: 'brief' | 'detailed' | 'comprehensive';
    code_examples: boolean;
  };
  learning_focus: string[];
  motivation_mode: 'explorer' | 'builder' | 'optimizer' | 'learner';
  updated_at: string;
}

export interface BusinessContext {
  project_id: number;
  purpose: string;
  target_audience: string[];
  success_metrics: string[];
  key_differentiators: string[];
  constraints: string[];
  timeline: {
    start_date?: string;
    target_completion?: string;
    milestones: { name: string; date: string; status: 'pending' | 'in_progress' | 'completed' }[];
  };
  updated_at: string;
}

export interface TechnicalContext {
  project_id: number;
  tech_stack: {
    frontend: string[];
    backend: string[];
    database: string[];
    infrastructure: string[];
    tools: string[];
  };
  architecture_patterns: string[];
  design_decisions: {
    decision: string;
    rationale: string;
    alternatives_considered: string[];
    date: string;
  }[];
  dependencies: {
    name: string;
    version: string;
    purpose: string;
    criticality: 'low' | 'medium' | 'high';
  }[];
  performance_requirements: {
    response_time: string;
    throughput: string;
    scalability: string;
    availability: string;
  };
  updated_at: string;
}

export interface CodeContext {
  project_id: number;
  file_structure: {
    path: string;
    type: 'file' | 'directory';
    purpose: string;
    last_modified: string;
  }[];
  key_functions: {
    name: string;
    file_path: string;
    purpose: string;
    complexity: number;
    dependencies: string[];
  }[];
  recent_changes: {
    file_path: string;
    change_type: 'created' | 'modified' | 'deleted';
    summary: string;
    date: string;
  }[];
  code_patterns: {
    pattern: string;
    frequency: number;
    files: string[];
  }[];
  updated_at: string;
}

export interface TaskContext {
  project_id: number;
  active_tasks: {
    id: string;
    title: string;
    description: string;
    status: 'pending' | 'in_progress' | 'completed' | 'blocked';
    priority: 'low' | 'medium' | 'high';
    created_at: string;
    updated_at: string;
  }[];
  current_focus: string;
  progress_notes: string[];
  blockers: {
    description: string;
    type: 'technical' | 'resource' | 'decision' | 'external';
    created_at: string;
    resolved: boolean;
  }[];
  next_steps: string[];
  updated_at: string;
}

export interface ContextAssemblyOptions {
  userRequest: string;
  taskType: string;
  projectId?: number;
  userId?: string;
  tokenBudget?: number;
  includeLearning?: boolean;
}

export interface AssembledContext {
  personal: Partial<PersonalContext>;
  business: Partial<BusinessContext>;
  technical: Partial<TechnicalContext>;
  code: Partial<CodeContext>;
  task: Partial<TaskContext>;
  tokenUsage: {
    personal: number;
    business: number;
    technical: number;
    code: number;
    task: number;
    total: number;
  };
  relevanceScores: {
    personal: number;
    business: number;
    technical: number;
    code: number;
    task: number;
  };
}

@injectable()
@injectable()\nexport class MemoryService {
  private static readonly MEMORY_MODEL = 'nova-lite'; // Use efficient model for memory operations
  private static readonly KAPI_MEMORY_DIR = '.kapi/memory';
  
  constructor(
    @inject(TYPES.PrismaService) private prisma: PrismaClient,
    @inject(TYPES.UnifiedConversationService) private conversationService: typeof UnifiedConversationService
  ) {}

  /**
   * Assemble context for an AI request based on relevance and token budget
   */
  async assembleContext(options: ContextAssemblyOptions): Promise<AssembledContext> {
    const startTime = Date.now();
    const tokenBudget = options.tokenBudget || 5000;
    
    try {
      // Load all memory components
      const [personal, business, technical, code, task] = await Promise.all([
        this.getPersonalContext(options.userId),
        options.projectId ? this.getBusinessContext(options.projectId) : null,
        options.projectId ? this.getTechnicalContext(options.projectId) : null,
        options.projectId ? this.getCodeContext(options.projectId) : null,
        options.projectId ? this.getTaskContext(options.projectId) : null
      ]);

      // Calculate relevance scores
      const relevanceScores = await this.calculateRelevanceScores(
        options.userRequest,
        options.taskType,
        { personal, business, technical, code, task }
      );

      // Prioritize and assemble context within token budget
      const assembledContext = this.prioritizeContextComponents(
        { personal, business, technical, code, task },
        relevanceScores,
        tokenBudget
      );

      const assemblyTime = Date.now() - startTime;
      logger.info(`Context assembled in ${assemblyTime}ms for task type: ${options.taskType}`);

      return assembledContext;
    } catch (error) {
      logger.error('Error assembling context:', error);
      // Return minimal context on error
      return {
        personal: {},
        business: {},
        technical: {},
        code: {},
        task: {},
        tokenUsage: { personal: 0, business: 0, technical: 0, code: 0, task: 0, total: 0 },
        relevanceScores: { personal: 0, business: 0, technical: 0, code: 0, task: 0 }
      };
    }
  }

  /**
   * Update memory components after an AI interaction
   */
  async recordInteraction(interaction: {
    userId?: string;
    projectId?: number;
    request: string;
    response: string;
    taskType: string;
    feedback?: 'positive' | 'negative' | 'neutral';
    outcome: 'success' | 'partial' | 'failure';
    codeChanges?: string[];
    learningPoints?: string[];
  }): Promise<void> {
    try {
      // Update personal context based on interaction patterns
      if (interaction.userId) {
        await this.updatePersonalContext(interaction.userId, {
          interaction_patterns: interaction,
          feedback: interaction.feedback,
          task_type: interaction.taskType
        });
      }

      // Update project-specific contexts
      if (interaction.projectId) {
        await Promise.all([
          this.updateTaskContext(interaction.projectId, {
            request: interaction.request,
            response: interaction.response,
            outcome: interaction.outcome,
            timestamp: new Date().toISOString()
          }),
          interaction.codeChanges ? this.updateCodeContext(interaction.projectId, {
            changes: interaction.codeChanges,
            timestamp: new Date().toISOString()
          }) : Promise.resolve()
        ]);
      }

      // Extract and store learning points
      if (interaction.learningPoints && interaction.learningPoints.length > 0) {
        await this.recordLearningPoints(interaction.projectId, interaction.learningPoints);
      }

      // Log usage for memory operations
      await modelUsageService.logLlmUsage({
        userId: parseInt(interaction.userId || '0'),
        modelRequested: MemoryService.MEMORY_MODEL,
        modelUsed: MemoryService.MEMORY_MODEL,
        taskType: 'memory_update',
        promptTokens: this.estimatePromptTokens(interaction.request),
        completionTokens: this.estimateCompletionTokens(interaction.response),
        status: 'success'
      });

    } catch (error) {
      logger.error('Error recording interaction:', error);
    }
  }

  /**
   * Get personal context from localStorage (simulated with database for backend)
   */
  private async getPersonalContext(userId?: string): Promise<PersonalContext | null> {
    if (!userId) return null;

    try {
      // In a real implementation, this would interface with a user preferences table
      // For now, we'll return a default structure
      return {
        userId,
        codingStyle: {
          preferred_patterns: ['functional', 'modular'],
          language_preferences: ['typescript', 'javascript'],
          framework_preferences: ['react', 'nodejs'],
          code_style_notes: 'Prefers clean, well-documented code'
        },
        communication_style: {
          feedback_preference: 'direct',
          explanation_level: 'detailed',
          code_examples: true
        },
        learning_focus: ['ai-integration', 'performance-optimization'],
        motivation_mode: 'builder',
        updated_at: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Error getting personal context for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Get business context from .kapi/memory directory
   */
  private async getBusinessContext(projectId: number): Promise<BusinessContext | null> {
    try {
      const project = await this.prisma.projects.findUnique({
        where: { id: projectId },
        select: { local_path: true }
      });

      if (!project?.local_path) return null;

      const memoryPath = path.join(project.local_path, MemoryService.KAPI_MEMORY_DIR, 'business-context.json');
      
      if (!existsSync(memoryPath)) {
        return this.createDefaultBusinessContext(projectId);
      }

      const content = await fs.readFile(memoryPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      logger.error(`Error getting business context for project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * Get technical context from .kapi/memory directory
   */
  private async getTechnicalContext(projectId: number): Promise<TechnicalContext | null> {
    try {
      const project = await this.prisma.projects.findUnique({
        where: { id: projectId },
        select: { local_path: true }
      });

      if (!project?.local_path) return null;

      const memoryPath = path.join(project.local_path, MemoryService.KAPI_MEMORY_DIR, 'technical-context.json');
      
      if (!existsSync(memoryPath)) {
        return this.createDefaultTechnicalContext(projectId);
      }

      const content = await fs.readFile(memoryPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      logger.error(`Error getting technical context for project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * Get code context from .kapi/memory directory
   */
  private async getCodeContext(projectId: number): Promise<CodeContext | null> {
    try {
      const project = await this.prisma.projects.findUnique({
        where: { id: projectId },
        select: { local_path: true }
      });

      if (!project?.local_path) return null;

      const memoryPath = path.join(project.local_path, MemoryService.KAPI_MEMORY_DIR, 'code-context.json');
      
      if (!existsSync(memoryPath)) {
        return this.createDefaultCodeContext(projectId);
      }

      const content = await fs.readFile(memoryPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      logger.error(`Error getting code context for project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * Get task context from .kapi/memory directory
   */
  private async getTaskContext(projectId: number): Promise<TaskContext | null> {
    try {
      const project = await this.prisma.projects.findUnique({
        where: { id: projectId },
        select: { local_path: true }
      });

      if (!project?.local_path) return null;

      const memoryPath = path.join(project.local_path, MemoryService.KAPI_MEMORY_DIR, 'task-context.json');
      
      if (!existsSync(memoryPath)) {
        return this.createDefaultTaskContext(projectId);
      }

      const content = await fs.readFile(memoryPath, 'utf-8');
      return JSON.parse(content);
    } catch (error) {
      logger.error(`Error getting task context for project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * Calculate relevance scores for context components using AI
   */
  private async calculateRelevanceScores(
    userRequest: string,
    taskType: string,
    contexts: {
      personal: PersonalContext | null;
      business: BusinessContext | null;
      technical: TechnicalContext | null;
      code: CodeContext | null;
      task: TaskContext | null;
    }
  ): Promise<AssembledContext['relevanceScores']> {
    try {
      const prompt = this.buildRelevanceAnalysisPrompt(userRequest, taskType, contexts);
      
      // Create a temporary conversation for relevance analysis
      const conversation = await this.conversationService.createConversation(0, {
        title: 'Memory Relevance Analysis'
      });

      const response = await this.conversationService.addUserMessageAndGetResponse(
        conversation.id,
        prompt,
        {
          modelId: MemoryService.MEMORY_MODEL,
          maxTokens: 500,
          temperature: 0.1 // Low temperature for consistent scoring
        }
      );

      const content = response.message?.content || '';
      return this.parseRelevanceScores(content);
    } catch (error) {
      logger.error('Error calculating relevance scores:', error);
      // Return default scores that prioritize task and code context
      return {
        personal: 0.3,
        business: 0.5,
        technical: 0.7,
        code: 0.8,
        task: 0.9
      };
    }
  }

  /**
   * Prioritize context components based on relevance and token budget
   */
  private prioritizeContextComponents(
    contexts: {
      personal: PersonalContext | null;
      business: BusinessContext | null;
      technical: TechnicalContext | null;
      code: CodeContext | null;
      task: TaskContext | null;
    },
    relevanceScores: AssembledContext['relevanceScores'],
    tokenBudget: number
  ): AssembledContext {
    const components = [
      { name: 'task', context: contexts.task, relevance: relevanceScores.task },
      { name: 'code', context: contexts.code, relevance: relevanceScores.code },
      { name: 'technical', context: contexts.technical, relevance: relevanceScores.technical },
      { name: 'business', context: contexts.business, relevance: relevanceScores.business },
      { name: 'personal', context: contexts.personal, relevance: relevanceScores.personal }
    ];

    // Sort by relevance score
    components.sort((a, b) => b.relevance - a.relevance);

    const result: AssembledContext = {
      personal: {},
      business: {},
      technical: {},
      code: {},
      task: {},
      tokenUsage: { personal: 0, business: 0, technical: 0, code: 0, task: 0, total: 0 },
      relevanceScores
    };

    let remainingTokens = tokenBudget;

    // Allocate tokens to components based on priority and relevance
    for (const component of components) {
      if (remainingTokens <= 0 || !component.context) continue;

      const componentTokens = this.estimateContextTokens(component.context);
      const allocatedTokens = Math.min(componentTokens, remainingTokens);

      if (allocatedTokens > 0) {
        const truncatedContext = this.truncateContext(component.context, allocatedTokens);
        (result as any)[component.name] = truncatedContext;
        (result.tokenUsage as any)[component.name] = allocatedTokens;
        remainingTokens -= allocatedTokens;
      }
    }

    result.tokenUsage.total = tokenBudget - remainingTokens;
    return result;
  }

  /**
   * Update personal context based on interaction patterns
   */
  private async updatePersonalContext(userId: string, updates: any): Promise<void> {
    try {
      // In a real implementation, this would update user preferences in the database
      logger.info(`Updating personal context for user ${userId}:`, updates);
    } catch (error) {
      logger.error('Error updating personal context:', error);
    }
  }

  /**
   * Update task context in .kapi/memory directory
   */
  private async updateTaskContext(projectId: number, updates: any): Promise<void> {
    try {
      const project = await this.prisma.projects.findUnique({
        where: { id: projectId },
        select: { local_path: true }
      });

      if (!project?.local_path) return;

      const memoryDir = path.join(project.local_path, MemoryService.KAPI_MEMORY_DIR);
      const memoryPath = path.join(memoryDir, 'task-context.json');

      // Ensure directory exists
      await fs.mkdir(memoryDir, { recursive: true });

      // Load existing context or create new one
      let taskContext = await this.getTaskContext(projectId) || this.createDefaultTaskContext(projectId);

      // Apply updates
      if (updates.request) {
        taskContext.progress_notes.push(`Request: ${updates.request}`);
      }
      if (updates.outcome) {
        taskContext.progress_notes.push(`Outcome: ${updates.outcome}`);
      }

      taskContext.updated_at = new Date().toISOString();

      // Save updated context
      await fs.writeFile(memoryPath, JSON.stringify(taskContext, null, 2));
    } catch (error) {
      logger.error('Error updating task context:', error);
    }
  }

  /**
   * Update code context in .kapi/memory directory
   */
  private async updateCodeContext(projectId: number, updates: any): Promise<void> {
    try {
      const project = await this.prisma.projects.findUnique({
        where: { id: projectId },
        select: { local_path: true }
      });

      if (!project?.local_path) return;

      const memoryDir = path.join(project.local_path, MemoryService.KAPI_MEMORY_DIR);
      const memoryPath = path.join(memoryDir, 'code-context.json');

      // Ensure directory exists
      await fs.mkdir(memoryDir, { recursive: true });

      // Load existing context or create new one
      let codeContext = await this.getCodeContext(projectId) || this.createDefaultCodeContext(projectId);

      // Apply updates
      if (updates.changes) {
        for (const change of updates.changes) {
          codeContext.recent_changes.push({
            file_path: change,
            change_type: 'modified',
            summary: 'AI-assisted modification',
            date: updates.timestamp
          });
        }
      }

      codeContext.updated_at = new Date().toISOString();

      // Save updated context
      await fs.writeFile(memoryPath, JSON.stringify(codeContext, null, 2));
    } catch (error) {
      logger.error('Error updating code context:', error);
    }
  }

  /**
   * Record learning points for future reference
   */
  private async recordLearningPoints(projectId: number | undefined, learningPoints: string[]): Promise<void> {
    try {
      logger.info(`Recording learning points for project ${projectId}:`, learningPoints);
      // In a real implementation, this would store learning points in a dedicated table
      // or append to a learning log file
    } catch (error) {
      logger.error('Error recording learning points:', error);
    }
  }

  // Helper methods for creating default contexts
  private createDefaultBusinessContext(projectId: number): BusinessContext {
    return {
      project_id: projectId,
      purpose: 'Project purpose to be defined',
      target_audience: ['developers', 'end-users'],
      success_metrics: ['user adoption', 'performance', 'maintainability'],
      key_differentiators: ['to be defined'],
      constraints: ['time', 'resources'],
      timeline: {
        milestones: []
      },
      updated_at: new Date().toISOString()
    };
  }

  private createDefaultTechnicalContext(projectId: number): TechnicalContext {
    return {
      project_id: projectId,
      tech_stack: {
        frontend: ['to be identified'],
        backend: ['to be identified'],
        database: ['to be identified'],
        infrastructure: ['to be identified'],
        tools: ['to be identified']
      },
      architecture_patterns: [],
      design_decisions: [],
      dependencies: [],
      performance_requirements: {
        response_time: 'to be defined',
        throughput: 'to be defined',
        scalability: 'to be defined',
        availability: 'to be defined'
      },
      updated_at: new Date().toISOString()
    };
  }

  private createDefaultCodeContext(projectId: number): CodeContext {
    return {
      project_id: projectId,
      file_structure: [],
      key_functions: [],
      recent_changes: [],
      code_patterns: [],
      updated_at: new Date().toISOString()
    };
  }

  private createDefaultTaskContext(projectId: number): TaskContext {
    return {
      project_id: projectId,
      active_tasks: [],
      current_focus: 'Project initialization',
      progress_notes: [],
      blockers: [],
      next_steps: ['Define project requirements', 'Set up project structure'],
      updated_at: new Date().toISOString()
    };
  }

  // Helper methods for AI analysis
  private buildRelevanceAnalysisPrompt(
    userRequest: string,
    taskType: string,
    contexts: any
  ): string {
    return `
Analyze the relevance of different context types for this request:

User Request: "${userRequest}"
Task Type: ${taskType}

Rate the relevance of each context type from 0.0 to 1.0:
- Personal: User coding style and preferences
- Business: Project goals and requirements
- Technical: Architecture and tech stack
- Code: File structure and recent changes
- Task: Current work and progress

Respond with scores in this format:
PERSONAL: 0.X
BUSINESS: 0.X
TECHNICAL: 0.X
CODE: 0.X
TASK: 0.X

Focus on what context would be most helpful for completing this specific request.
`;
  }

  private parseRelevanceScores(content: string): AssembledContext['relevanceScores'] {
    const scores = {
      personal: 0.3,
      business: 0.5,
      technical: 0.7,
      code: 0.8,
      task: 0.9
    };

    try {
      const lines = content.split('\n');
      for (const line of lines) {
        if (line.includes('PERSONAL:')) {
          scores.personal = parseFloat(line.split(':')[1].trim()) || 0.3;
        } else if (line.includes('BUSINESS:')) {
          scores.business = parseFloat(line.split(':')[1].trim()) || 0.5;
        } else if (line.includes('TECHNICAL:')) {
          scores.technical = parseFloat(line.split(':')[1].trim()) || 0.7;
        } else if (line.includes('CODE:')) {
          scores.code = parseFloat(line.split(':')[1].trim()) || 0.8;
        } else if (line.includes('TASK:')) {
          scores.task = parseFloat(line.split(':')[1].trim()) || 0.9;
        }
      }
    } catch (error) {
      logger.warn('Error parsing relevance scores, using defaults:', error);
    }

    return scores;
  }

  // Token estimation methods
  private estimateContextTokens(context: any): number {
    if (!context) return 0;
    const jsonString = JSON.stringify(context);
    // Rough estimation: 1 token per 4 characters
    return Math.ceil(jsonString.length / 4);
  }

  private estimatePromptTokens(prompt: string): number {
    return Math.ceil(prompt.length / 4);
  }

  private estimateCompletionTokens(completion: string): number {
    return Math.ceil(completion.length / 4);
  }

  private truncateContext(context: any, maxTokens: number): any {
    if (!context) return context;
    
    const jsonString = JSON.stringify(context);
    const maxLength = maxTokens * 4; // Convert tokens to characters
    
    if (jsonString.length <= maxLength) {
      return context;
    }

    // Truncate string and parse back to object
    const truncated = jsonString.substring(0, maxLength);
    try {
      return JSON.parse(truncated);
    } catch (error) {
      // If truncation broke JSON, return a simplified version
      return { ...context, _truncated: true };
    }
  }
}

// Export a singleton instance
export const memoryService = new MemoryService(
  {} as PrismaClient, // Will be injected by DI container
  {} as typeof UnifiedConversationService // Will be injected by DI container
);

export default memoryService;