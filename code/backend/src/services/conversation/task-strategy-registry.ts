/**
 * Task Strategy Registry
 *
 * This class manages the registration and retrieval of task strategies.
 */

import { TaskStrategy } from './task-strategy.interface';
import { ChatTaskStrategy } from './strategies/chat-task-strategy';
import { OnboardingTaskStrategy } from './strategies/onboarding-task-strategy';
import { UserOnboardingTaskStrategy } from './strategies/user-onboarding-task-strategy';
import { ProjectOnboardingTaskStrategy } from './strategies/project-onboarding-task-strategy';
import { CodeGenerationTaskStrategy } from './strategies/code-generation-task-strategy';
import { CodePlanningTaskStrategy } from './strategies/code-planning-task-strategy';
import { SlideGenerationTaskStrategy } from './strategies/slide-generation-task-strategy';
import { SvgMockupTaskStrategy } from './strategies/svg-mockup-task-strategy';
import { TestCasesTaskStrategy } from './strategies/test-cases-task-strategy';
import { MultimodalTaskStrategy } from './strategies/multimodal-task-strategy';
import ProgressiveImprovementTaskStrategy from './strategies/progressive-improvement-task-strategy';
import { logger } from '../../common/logger';

export class TaskStrategyRegistry {
  private strategies: Map<string, TaskStrategy> = new Map();
  private defaultStrategy: TaskStrategy;

  constructor() {
    // Register built-in strategies
    this.registerStrategy(new ChatTaskStrategy());
    this.registerStrategy(new OnboardingTaskStrategy());
    this.registerStrategy(new UserOnboardingTaskStrategy());
    this.registerStrategy(new ProjectOnboardingTaskStrategy());
    this.registerStrategy(new CodeGenerationTaskStrategy());
    this.registerStrategy(new CodePlanningTaskStrategy());
    this.registerStrategy(new SlideGenerationTaskStrategy());
    this.registerStrategy(new SvgMockupTaskStrategy());
    this.registerStrategy(new TestCasesTaskStrategy());
    this.registerStrategy(new MultimodalTaskStrategy());
    this.registerStrategy(new ProgressiveImprovementTaskStrategy());

    // Set default strategy
    this.defaultStrategy = new ChatTaskStrategy();
  }

  /**
   * Register a new task strategy
   */
  registerStrategy(strategy: TaskStrategy): void {
    this.strategies.set(strategy.getTaskType(), strategy);
    logger.debug(`Registered task strategy for type: ${strategy.getTaskType()}`);
  }

  /**
   * Get a strategy for a specific task type
   */
  getStrategy(taskType: string): TaskStrategy {
    const strategy = this.strategies.get(taskType);

    if (!strategy) {
      logger.warn(`No strategy found for task type: ${taskType}, using default strategy`);
      return this.defaultStrategy;
    }

    return strategy;
  }

  /**
   * Get all registered strategies
   */
  getAllStrategies(): TaskStrategy[] {
    return Array.from(this.strategies.values());
  }
}

// Create and export a singleton instance
export const taskStrategyRegistry = new TaskStrategyRegistry();
