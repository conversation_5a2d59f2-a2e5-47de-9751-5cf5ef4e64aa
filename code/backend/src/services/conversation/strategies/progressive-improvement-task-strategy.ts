import { TaskStrategy } from '../task-strategy.interface';
import { BaseTaskStrategy } from '../base-task-strategy';

/**
 * Progressive Improvement Task Strategy
 * 
 * Analyzes historical project data and provides improvement insights over time.
 * Uses AI to identify trends, celebrate achievements, and recommend next steps.
 */
export class ProgressiveImprovementTaskStrategy extends BaseTaskStrategy implements TaskStrategy {
  readonly strategyName = 'progressive-improvement';
  readonly description = 'Analyzes project improvement over time and provides smart recommendations';
  readonly supportedProviders = ['gemini', 'claude', 'azure'] as const;
  readonly defaultProvider = 'gemini';

  getTaskType(): string {
    return 'progressive-improvement';
  }

  async execute(context: any): Promise<any> {
    // Mock implementation for now
    return {
      content: 'Progressive improvement analysis completed',
      role: 'assistant',
      model: this.getDefaultModel(),
      tokens: { input: 100, output: 200 }
    };
  }

  async* streamExecute(context: any): AsyncGenerator<any, void, unknown> {
    // Mock streaming implementation
    yield { content: 'Progressive improvement analysis completed', role: 'assistant' };
  }

  protected buildSystemPrompt(): string {
    return `You are KAPI's Progressive Improvement Analyst, an expert at tracking and analyzing software project improvements over time.

Your role is to:
1. Analyze historical project data to identify improvement trends and patterns
2. Recognize achievements and celebrate progress made by developers  
3. Identify areas of concern or regression that need attention
4. Provide smart, prioritized recommendations for continued improvement
5. Motivate developers with encouraging insights about their progress

Guidelines:
- Be encouraging and positive while being honest about areas needing work
- Focus on actionable insights that developers can implement
- Recognize patterns across time periods to provide meaningful trends
- Use data-driven insights but explain them in human-friendly terms
- Prioritize recommendations based on impact and feasibility
- Celebrate wins and progress to keep developers motivated

Response format:
- Start with a brief summary of overall progress
- Highlight key improvements and achievements
- Identify concerning trends or areas needing attention
- Provide 3-5 prioritized, actionable recommendations
- End with encouraging words about the development journey

Remember: Your goal is to help developers stay motivated and focused on continuous improvement of their projects.`;
  }

  protected buildUserPrompt(content: string, context: any): string {
    return `Analyze this project's historical improvement data and provide insights:

${content}

Please provide a comprehensive analysis that includes:

1. **Progress Summary**: Overall trajectory and key improvements
2. **Achievement Recognition**: Specific wins and positive changes to celebrate  
3. **Trend Analysis**: What's improving, what's stable, what needs attention
4. **Priority Recommendations**: Top 3-5 actionable next steps
5. **Motivation**: Encouraging perspective on the improvement journey

Focus on being both honest and encouraging. Help the developer understand their progress and feel motivated to continue improving.`;
  }

  protected getContextualData(context: any): Record<string, any> {
    return {
      projectPath: context.projectPath || '',
      analysisHistory: context.historicalData || [],
      userGoals: context.userObjectives || {},
      currentMetrics: context.currentAnalysis || {},
      timeframe: context.timeframe || 'all-time'
    };
  }

  protected shouldUseProvider(provider: string, context: any): boolean {
    // Gemini is preferred for analytical tasks with large context
    if (provider === 'gemini') return true;
    
    // Claude is good for detailed analysis
    if (provider === 'claude') return true;
    
    // Azure can handle structured data well
    if (provider === 'azure') return true;
    
    return false;
  }

  protected estimateTokenUsage(content: string): { input: number; output: number } {
    // Historical data can be quite large
    const inputTokens = Math.ceil(content.length / 4) + 500; // Include system prompt
    const outputTokens = 800; // Comprehensive analysis response
    
    return { input: inputTokens, output: outputTokens };
  }

  protected validateInput(content: string, context: any): { isValid: boolean; error?: string } {
    if (!content || content.trim().length === 0) {
      return { isValid: false, error: 'Historical data content is required' };
    }

    try {
      // Try to parse as JSON to validate structure
      const data = JSON.parse(content);
      if (!Array.isArray(data) && typeof data !== 'object') {
        return { isValid: false, error: 'Historical data must be in valid JSON format' };
      }
    } catch (error) {
      // If not JSON, assume it's a formatted string which is also valid
    }

    return { isValid: true };
  }

  protected getProviderSpecificOptions(provider: string): Record<string, any> {
    const baseOptions = {
      temperature: 0.3, // Lower temperature for more consistent analysis
      maxTokens: 1000,
    };

    switch (provider) {
      case 'gemini':
        return {
          ...baseOptions,
          model: 'gemini-1.5-pro', // Better for analytical tasks
          maxTokens: 1200,
        };
      
      case 'claude':
        return {
          ...baseOptions,
          model: 'claude-3-5-sonnet',
          maxTokens: 1000,
        };
      
      case 'azure':
        return {
          ...baseOptions,
          model: 'gpt-4o',
          maxTokens: 1000,
        };
      
      default:
        return baseOptions;
    }
  }
}

export default ProgressiveImprovementTaskStrategy;