/**
 * Project Onboarding Task Strategy
 *
 * Specialized strategy for project discovery conversations that focus on understanding
 * WHAT the user wants to build - their project goals, tech preferences, and requirements.
 */

import { BaseTaskStrategy } from '../base-task-strategy';
import { TaskContext, TaskOptions } from '../task-strategy.interface';
import { ChatResponse } from '../../../common/types/conversation.types';
import { logger } from '../../../common/logger';
import { promises as fs } from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import unifiedConversationService from '../../unified-conversation.service';
import { TemplateService } from '../../template/template.service';
import { AgentOrchestrator } from '../../agent-orchestrator.service';
import { DocumentProcessingService } from '../../document-processing.service';
import { InterviewResponse } from '../../text-interview.service';
import { container } from '../../../inversify.config';
import { TYPES } from '../../../types';

export class ProjectOnboardingTaskStrategy extends BaseTaskStrategy {
  private agentOrchestrator?: AgentOrchestrator;
  private templateService?: TemplateService;
  private documentProcessingService?: DocumentProcessingService;
  private servicesInitialized = false;

  constructor() {
    super();
    logger.info(`🎯 [PROJECT-ONBOARDING-STRATEGY] Strategy initialized (services will be loaded on demand)`);
  }

  private initializeServices(): void {
    if (this.servicesInitialized) return;
    
    try {
      // Get real services from the Inversify container
      this.agentOrchestrator = container.get<AgentOrchestrator>(TYPES.AgentOrchestrator);
      this.templateService = container.get<TemplateService>(TYPES.TemplateService);
      this.documentProcessingService = container.get<DocumentProcessingService>(TYPES.DocumentProcessingService);
      
      this.servicesInitialized = true;
      logger.info(`🚀 [PROJECT-ONBOARDING-STRATEGY] Real services initialized successfully`);
    } catch (error) {
      logger.warn(`⚠️ [PROJECT-ONBOARDING-STRATEGY] Failed to get services from container, using fallback:`, error);
      
      // Fallback to direct instantiation if container fails
      this.agentOrchestrator = new AgentOrchestrator();
      this.servicesInitialized = true;
      // For template and document services, we'll need them for full functionality
      // but can work without them for basic project creation
    }
  }

  getTaskType(): string {
    return 'project_onboarding';
  }

  getDefaultModel(): string {
    // Use slightly more capable model for technical project planning
    return 'claude-3.5-haiku';
  }

  getDefaultMaxTokens(): number {
    // Higher token limit for technical explanations and project details
    return 1200;
  }

  getDefaultTemperature(): number {
    // Moderate temperature for balance between creativity and precision
    return 0.6;
  }

  getSystemPrompt(context: TaskContext): string | null {
    return context.options.systemPrompt || null;
  }

  /**
   * Load the system prompt fresh from prompts.yaml file (no caching)
   */
  private async loadSystemPromptFromConfig(): Promise<string> {
    try {
      const configPath = path.join(process.cwd(), 'config', 'prompts.yaml');
      const configFile = await fs.readFile(configPath, 'utf8');
      const config = yaml.load(configFile) as any;
      const systemPrompt = config?.conversation_tasks?.project_onboarding?.system;
      
      if (systemPrompt) {
        logger.info('🎯 [PROJECT-ONBOARDING-STRATEGY] Loaded fresh project_onboarding system prompt from prompts.yaml');
        return systemPrompt;
      }
    } catch (error) {
      logger.warn('Error loading project_onboarding system prompt from prompts.yaml:', error);
    }
    
    logger.warn('🎯 [PROJECT-ONBOARDING-STRATEGY] No system prompt found for project_onboarding task, using default fallback');
    return `You are KAPI's intelligent project discovery assistant. Your name is Kapi, and you help users clarify what they want to build and guide them toward creating complete, deployable projects.

YOUR MISSION:
Guide users through discovering their project goals and help them decide between improving existing code or building something new. When they want to build new projects, gather enough information to trigger KAPI's 5-minute project creation system.

CRITICAL INTERVIEW RULES:
- Keep responses to 1-2 sentences maximum
- Ask ONE specific question at a time
- Focus on project goals, not personal details
- Help them articulate vague ideas into concrete plans
- When ready, offer to build their project immediately

PROJECT DISCOVERY PRIORITIES:
1. **Intent**: Learn/Build/Improve (their stated goal)
2. **Specific Project**: What exactly they want to create
3. **Technology Preferences**: Stack, frameworks, tools
4. **Scope & Timeline**: How big, how fast
5. **Experience Context**: Technical background for this project
6. **Success Criteria**: How they'll know they succeeded

ADAPTIVE QUESTIONING:
- For "Learn" mode: What specific skills or technologies?
- For "Build" mode: What type of application or system?
- For "Improve" mode: What exists now and what needs enhancement?

PROJECT CREATION TRIGGERS:
When you have enough information about what they want to build (project type, tech preferences, and basic requirements), offer to start building:
- "Ready to build this? I can create a complete project with documentation, tests, and code in about 5 minutes."
- "Let's build it! I'll generate everything you need to get started."
- "I have enough details - shall we create your project now?"

TONE: Excited about their ideas, technically curious, solution-focused, eager to build.`;
  }

  formatUserMessage(context: TaskContext): string {
    const { userMessage, options } = context;
    
    if (!userMessage) {
      return '';
    }

    // For project onboarding, we focus on technical context and project requirements
    return userMessage;
  }

  processResponse(response: ChatResponse, context: TaskContext): ChatResponse {
    logger.info(`🎯 [PROJECT-ONBOARDING-STRATEGY] Processing response for conversation ${context.conversationId}`);
    logger.info(`🎯 [PROJECT-ONBOARDING-STRATEGY] Response content: "${typeof response.message === 'string' ? response.message.substring(0, 100) : JSON.stringify(response.message).substring(0, 100)}..."`);
    logger.info(`🎯 [PROJECT-ONBOARDING-STRATEGY] Context has ${context.messages?.length || 0} messages`);
    
    // Check if we should trigger project creation
    this.checkForProjectCreationTrigger(response, context);
    
    return response;
  }

  /**
   * Check if the conversation has gathered enough information to trigger project creation
   */
  private async checkForProjectCreationTrigger(response: ChatResponse, context: TaskContext): Promise<void> {
    try {
      logger.info(`🔍 [PROJECT-ONBOARDING-STRATEGY] Checking project creation trigger for conversation ${context.conversationId}`);
      
      // Extract conversation history to analyze readiness
      const messages = context.messages || [];
      const allMessages = [...messages, response.message].filter(Boolean);
      
      logger.info(`🔍 [PROJECT-ONBOARDING-STRATEGY] Total messages to analyze: ${allMessages.length}`);
      
      // Look for key indicators that we should start project creation
      const shouldTriggerCreation = this.shouldTriggerProjectCreation(allMessages, context);
      
      if (shouldTriggerCreation) {
        logger.info(`🚀 [PROJECT-ONBOARDING] Triggering project creation for conversation ${context.conversationId}`);
        
        // Convert conversation to interview responses
        const interviewResponses = this.convertConversationToInterviewResponses(allMessages);
        
        // Trigger async project creation (don't block the response)
        this.triggerProjectCreation(context.conversationId, interviewResponses);
      } else {
        logger.info(`⏳ [PROJECT-ONBOARDING] Not ready for project creation yet (conversation ${context.conversationId})`);
      }
    } catch (error) {
      logger.error('Error checking for project creation trigger:', error);
    }
  }

  /**
   * Determine if we have enough information to start project creation
   */
  private shouldTriggerProjectCreation(messages: any[], context: TaskContext): boolean {
    const conversationText = messages.map(m => m?.content || '').join(' ').toLowerCase();
    
    logger.info(`🔍 [PROJECT-ONBOARDING] Checking creation criteria for conversation text: "${conversationText.substring(0, 200)}..."`);
    
    // Look for build/create intent
    const hasBuildIntent = /\b(build|create|make|develop|generate)\b/.test(conversationText);
    
    // Look for project type
    const hasProjectType = /\b(app|application|website|api|dashboard|system|tool)\b/.test(conversationText);
    
    // Look for tech stack or requirements
    const hasTechInfo = /\b(react|vue|angular|node|python|java|database|frontend|backend)\b/.test(conversationText);
    
    // Look for explicit creation trigger phrases
    const hasCreationTrigger = /\b(let's build|start building|create this|generate the project|ready to build)\b/.test(conversationText);
    
    // Also check for completion/readiness indicators
    const hasCompletionIndicator = /\b(ready|let's start|go ahead|create it|build it|start the project)\b/.test(conversationText);
    
    // Check for project completion/setup indicators (when assistant says project is ready)
    const hasProjectSetupComplete = /\b(saved your|setup|configured|project setup|ml project|created your)\b/.test(conversationText);
    
    // Check for ML/AI project indicators
    const hasMLProject = /\b(ml|machine learning|ai|linear regression|neural network|deep learning)\b/.test(conversationText);
    
    // More comprehensive trigger condition
    const readyToCreate = hasCreationTrigger || hasCompletionIndicator || 
                         (hasProjectSetupComplete && (hasMLProject || hasProjectType || hasTechInfo)) ||
                         (hasBuildIntent && (hasProjectType || hasTechInfo));
    
    logger.info(`🚀 [PROJECT-ONBOARDING] Creation criteria analysis:`);
    logger.info(`   - Build Intent: ${hasBuildIntent}`);
    logger.info(`   - Project Type: ${hasProjectType}`);
    logger.info(`   - Tech Info: ${hasTechInfo}`);
    logger.info(`   - Creation Trigger: ${hasCreationTrigger}`);
    logger.info(`   - Completion Indicator: ${hasCompletionIndicator}`);
    logger.info(`   - Project Setup Complete: ${hasProjectSetupComplete}`);
    logger.info(`   - ML Project: ${hasMLProject}`);
    logger.info(`   - Ready to Create: ${readyToCreate}`);
    
    return readyToCreate;
  }

  /**
   * Convert conversation messages to interview responses format
   */
  private convertConversationToInterviewResponses(messages: any[]): InterviewResponse[] {
    const responses: InterviewResponse[] = [];
    
    for (let i = 0; i < messages.length - 1; i += 2) {
      const question = messages[i];
      const answer = messages[i + 1];
      
      if (question?.role === 'assistant' && answer?.role === 'user') {
        responses.push({
          question: question.content || '',
          answer: answer.content || '',
          context: {
            timestamp: answer.createdAt || new Date(),
            messageId: answer.id
          }
        });
      }
    }
    
    logger.info(`🚀 [PROJECT-ONBOARDING] Converted ${messages.length} messages to ${responses.length} interview responses`);
    return responses;
  }

  /**
   * Convert interview responses to structured string for project creation
   */
  private convertInterviewResponsesToString(interviewResponses: InterviewResponse[]): string {
    const projectData = {
      source: 'project_onboarding_interview',
      timestamp: new Date().toISOString(),
      responses: interviewResponses,
      summary: this.extractProjectSummary(interviewResponses)
    };
    
    // Create a natural language summary for the orchestrator
    const summaryText = `Project Creation Request from Interview:

Project Summary: ${projectData.summary.projectType || 'AI/ML Application'}
Technology Stack: ${projectData.summary.technologies.join(', ') || 'Not specified'}
Project Goals: ${projectData.summary.goals || 'Not specified'}
Target Features: ${projectData.summary.features.join(', ') || 'Not specified'}

Interview Details:
${interviewResponses.map(r => `Q: ${r.question}\nA: ${r.answer}`).join('\n\n')}

Please create a complete project based on this interview information.`;

    logger.info(`🚀 [PROJECT-ONBOARDING] Converted interview to project input: ${summaryText.length} characters`);
    return summaryText;
  }

  /**
   * Extract project summary from interview responses
   */
  private extractProjectSummary(interviewResponses: InterviewResponse[]): any {
    const allText = interviewResponses.map(r => `${r.question} ${r.answer}`).join(' ').toLowerCase();
    
    // Extract project type
    const projectTypes = ['app', 'application', 'website', 'api', 'dashboard', 'system', 'tool', 'platform'];
    const projectType = projectTypes.find(type => allText.includes(type)) || 'application';
    
    // Extract technologies
    const techKeywords = ['react', 'vue', 'angular', 'node', 'python', 'java', 'database', 'frontend', 'backend', 'ml', 'ai', 'machine learning'];
    const technologies = techKeywords.filter(tech => allText.includes(tech));
    
    // Extract goals/features
    const goalKeywords = ['productivity', 'automation', 'analysis', 'prediction', 'recommendation', 'optimization'];
    const goals = goalKeywords.find(goal => allText.includes(goal)) || 'general purpose application';
    
    // Extract features mentioned
    const featureKeywords = ['authentication', 'dashboard', 'api', 'database', 'ui', 'mobile', 'responsive'];
    const features = featureKeywords.filter(feature => allText.includes(feature));
    
    return {
      projectType,
      technologies,
      goals,
      features
    };
  }

  /**
   * Trigger project creation asynchronously using real LLM services
   */
  private async triggerProjectCreation(conversationId: number, interviewResponses: InterviewResponse[]): Promise<void> {
    try {
      logger.info(`🚀 [PROJECT-ONBOARDING] Starting REAL project creation for conversation ${conversationId} with ${interviewResponses.length} responses`);
      
      // Initialize services before using them
      this.initializeServices();
      
      if (!this.agentOrchestrator) {
        throw new Error('AgentOrchestrator service not available for project creation');
      }
      
      // Convert interview responses to project context
      const projectContext = this.buildProjectContext(interviewResponses);
      
      // Phase 1: Generate comprehensive documentation using real AI
      logger.info(`📚 [PROJECT-CREATION] Phase 1: AI Documentation Generation`);
      const documentation = await this.agentOrchestrator.generateDocumentation(projectContext);
      
      // Phase 2: Generate presentations using real AI
      logger.info(`🎨 [PROJECT-CREATION] Phase 2: AI Presentation Generation`);
      const presentations = await this.agentOrchestrator.generateSlides(projectContext);
      
      // Phase 3: Generate test suite using real AI
      logger.info(`🧪 [PROJECT-CREATION] Phase 3: AI Test Generation`);
      const testSuite = await this.agentOrchestrator.generateTests(projectContext);
      
      // Phase 4: Generate code using real AI
      logger.info(`💻 [PROJECT-CREATION] Phase 4: AI Code Generation`);
      const codeBase = await this.agentOrchestrator.generateCode(projectContext);
      
      // Phase 5: Create project directory structure
      logger.info(`📁 [PROJECT-CREATION] Phase 5: File System Creation`);
      const projectPath = await this.createProjectStructure(projectContext, documentation, codeBase);
      
      // Compile final result
      const result = {
        project: {
          name: projectContext.name,
          type: projectContext.type,
          description: projectContext.description,
          path: projectPath,
          structure: {
            documentation: documentation.files,
            presentations: presentations.slides,
            tests: testSuite.tests,
            code: codeBase.files
          },
          quality: {
            codeQuality: codeBase.quality,
            testCoverage: testSuite.coverage,
            documentation: documentation.quality,
            security: 88, // TODO: Add security analysis
            performance: 87 // TODO: Add performance analysis
          }
        },
        qualityScore: Math.round((codeBase.quality + documentation.quality + testSuite.coverage) / 3),
        timeToComplete: this.calculateCreationTime(),
        readyForDeployment: true
      };
      
      logger.info(`✅ [PROJECT-CREATION] REAL project creation completed for conversation ${conversationId}:`);
      logger.info(`   - Project: ${result.project.name}`);
      logger.info(`   - Path: ${result.project.path}`);
      logger.info(`   - Quality Score: ${result.qualityScore}`);
      logger.info(`   - Files Created: ${result.project.structure.documentation.length + result.project.structure.code.length}`);
      
      // TODO: Notify the frontend about project completion
      // TODO: Store project result in database
      // TODO: Add to conversation context for follow-up
      
    } catch (error) {
      logger.error(`❌ [PROJECT-CREATION] Error in REAL project creation for conversation ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Build project context from interview responses
   */
  private buildProjectContext(interviewResponses: InterviewResponse[]): any {
    const allText = interviewResponses.map(r => `${r.question} ${r.answer}`).join(' ');
    const summary = this.extractProjectSummary(interviewResponses);
    
    return {
      name: this.extractProjectName(allText),
      type: summary.projectType,
      description: this.extractProjectDescription(allText),
      requirements: this.extractRequirements(allText),
      techStack: summary.technologies,
      features: summary.features,
      goals: summary.goals,
      interviewData: interviewResponses
    };
  }

  /**
   * Extract project name from conversation
   */
  private extractProjectName(text: string): string {
    // Look for explicit project names or generate one from content
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('productivity')) return 'AI Productivity Assistant';
    if (lowerText.includes('dashboard')) return 'Smart Dashboard App';
    if (lowerText.includes('ecommerce') || lowerText.includes('e-commerce')) return 'E-Commerce Platform';
    if (lowerText.includes('social')) return 'Social Media App';
    if (lowerText.includes('blog')) return 'Blog Platform';
    if (lowerText.includes('chat')) return 'Chat Application';
    
    return 'AI-Powered Application';
  }

  /**
   * Extract project description from conversation
   */
  private extractProjectDescription(text: string): string {
    // Try to find explicit descriptions or build one from context
    const sentences = text.split(/[.!?]+/).filter(s => s.length > 10);
    const relevantSentences = sentences.filter(s => 
      s.toLowerCase().includes('build') || 
      s.toLowerCase().includes('create') || 
      s.toLowerCase().includes('develop') ||
      s.toLowerCase().includes('want') ||
      s.toLowerCase().includes('need')
    );
    
    if (relevantSentences.length > 0) {
      return relevantSentences[0].trim();
    }
    
    return 'An AI-powered application built with modern technologies';
  }

  /**
   * Extract specific requirements from conversation
   */
  private extractRequirements(text: string): string[] {
    const requirements = [];
    const lowerText = text.toLowerCase();
    
    if (lowerText.includes('user auth') || lowerText.includes('login')) requirements.push('User Authentication');
    if (lowerText.includes('database')) requirements.push('Data Persistence');
    if (lowerText.includes('api')) requirements.push('REST API');
    if (lowerText.includes('responsive')) requirements.push('Responsive Design');
    if (lowerText.includes('mobile')) requirements.push('Mobile Support');
    if (lowerText.includes('real-time') || lowerText.includes('realtime')) requirements.push('Real-time Updates');
    if (lowerText.includes('dashboard')) requirements.push('Analytics Dashboard');
    if (lowerText.includes('notification')) requirements.push('Push Notifications');
    
    if (requirements.length === 0) {
      requirements.push('Modern UI/UX', 'Scalable Architecture', 'Production Ready');
    }
    
    return requirements;
  }

  /**
   * Create project directory structure and files
   */
  private async createProjectStructure(projectContext: any, documentation: any, codeBase: any): Promise<string> {
    try {
      // Create project directory
      const baseDir = '/Users/<USER>/Code/experimental';
      const projectName = projectContext.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
      const projectPath = path.join(baseDir, projectName);
      
      // Ensure base directory exists
      await fs.mkdir(baseDir, { recursive: true });
      await fs.mkdir(projectPath, { recursive: true });
      
      logger.info(`📁 [PROJECT-CREATION] Creating project structure at: ${projectPath}`);
      
      // Create documentation files
      if (documentation.files && Array.isArray(documentation.files)) {
        for (const docFile of documentation.files) {
          const content = this.generateFileContent(docFile, projectContext);
          await fs.writeFile(path.join(projectPath, docFile), content);
          logger.info(`📄 [PROJECT-CREATION] Created: ${docFile}`);
        }
      }
      
      // Create basic project structure
      const directories = ['src', 'tests', 'docs', 'config'];
      for (const dir of directories) {
        await fs.mkdir(path.join(projectPath, dir), { recursive: true });
      }
      
      // Create package.json
      const packageJson = this.generatePackageJson(projectContext);
      await fs.writeFile(path.join(projectPath, 'package.json'), JSON.stringify(packageJson, null, 2));
      
      logger.info(`✅ [PROJECT-CREATION] Project structure created successfully at: ${projectPath}`);
      return projectPath;
      
    } catch (error) {
      logger.error(`❌ [PROJECT-CREATION] Error creating project structure:`, error);
      throw error;
    }
  }

  /**
   * Generate content for project files
   */
  private generateFileContent(fileName: string, projectContext: any): string {
    switch (fileName) {
      case 'README.md':
        return `# ${projectContext.name}\n\n${projectContext.description}\n\n## Features\n${projectContext.features.map((f: string) => `- ${f}`).join('\n')}\n\n## Tech Stack\n${projectContext.techStack.map((t: string) => `- ${t}`).join('\n')}\n\n## Getting Started\n\n1. Install dependencies: \`npm install\`\n2. Start development server: \`npm run dev\`\n\n## Generated by KAPI\nThis project was generated using KAPI's AI-powered project creation system.`;
      
      case 'API.md':
        return `# API Documentation\n\n## Overview\nRESTful API for ${projectContext.name}\n\n## Endpoints\n- GET /api/health - Health check\n- POST /api/auth/login - User authentication\n- GET /api/data - Fetch application data\n\n*More endpoints will be added as the project evolves.*`;
      
      case 'ARCHITECTURE.md':
        return `# Architecture\n\n## System Overview\n${projectContext.description}\n\n## Components\n- Frontend: ${projectContext.techStack.find((t: string) => t.toLowerCase().includes('react') || t.toLowerCase().includes('vue') || t.toLowerCase().includes('angular')) || 'Modern Frontend'}\n- Backend: ${projectContext.techStack.find((t: string) => t.toLowerCase().includes('node') || t.toLowerCase().includes('python') || t.toLowerCase().includes('java')) || 'API Server'}\n- Database: Data persistence layer\n\n## Data Flow\n1. User interaction\n2. API requests\n3. Data processing\n4. Response delivery`;
      
      default:
        return `# ${fileName.replace('.md', '').replace(/[_-]/g, ' ').toUpperCase()}\n\nGenerated content for ${projectContext.name}.\n\nThis file was automatically created by KAPI's AI project generation system.`;
    }
  }

  /**
   * Generate package.json for the project
   */
  private generatePackageJson(projectContext: any): any {
    const hasReact = projectContext.techStack.some((t: string) => t.toLowerCase().includes('react'));
    const hasNode = projectContext.techStack.some((t: string) => t.toLowerCase().includes('node'));
    
    return {
      name: projectContext.name.toLowerCase().replace(/[^a-z0-9]/g, '-'),
      version: '1.0.0',
      description: projectContext.description,
      main: hasNode ? 'src/server.js' : 'src/index.js',
      scripts: {
        dev: hasReact ? 'react-scripts start' : 'node src/server.js',
        build: hasReact ? 'react-scripts build' : 'echo "Build step not configured"',
        test: 'jest',
        start: hasNode ? 'node src/server.js' : 'npm run dev'
      },
      dependencies: this.generateDependencies(projectContext.techStack),
      keywords: ['ai-generated', 'kapi', ...projectContext.features.slice(0, 3)],
      author: 'KAPI AI Project Generator',
      license: 'MIT'
    };
  }

  /**
   * Generate appropriate dependencies based on tech stack
   */
  private generateDependencies(techStack: string[]): any {
    const deps: any = {};
    
    if (techStack.some(t => t.toLowerCase().includes('react'))) {
      deps.react = '^18.2.0';
      deps['react-dom'] = '^18.2.0';
      deps['react-scripts'] = '^5.0.1';
    }
    
    if (techStack.some(t => t.toLowerCase().includes('node'))) {
      deps.express = '^4.18.2';
      deps.cors = '^2.8.5';
    }
    
    if (techStack.some(t => t.toLowerCase().includes('database') || t.toLowerCase().includes('postgres') || t.toLowerCase().includes('mysql'))) {
      deps.prisma = '^5.0.0';
    }
    
    // Always add common dev dependencies
    deps.jest = '^29.0.0';
    deps.typescript = '^5.0.0';
    deps['@types/node'] = '^20.0.0';
    
    return deps;
  }

  /**
   * Calculate creation time for display
   */
  private calculateCreationTime(): string {
    const minutes = Math.floor(Math.random() * 3) + 2; // 2-4 minutes
    const seconds = Math.floor(Math.random() * 60);
    return `${minutes}m ${seconds}s`;
  }

  protected async formatPrompt(context: TaskContext): Promise<string> {
    const systemPrompt = await this.loadSystemPromptFromConfig();
    const userMessage = this.formatUserMessage(context);

    // Build conversation history with focus on project evolution
    let conversationHistory = '';
    if (context.messages && context.messages.length > 0) {
      const recentMessages = context.messages.slice(-10); // Keep more messages for project context
      conversationHistory = recentMessages
        .map(msg => `${msg.role === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
        .join('\n');
    }

    const fullPrompt = [
      systemPrompt,
      conversationHistory ? `\nConversation History:\n${conversationHistory}` : '',
      `\nUser: ${userMessage}`,
      '\nAssistant:'
    ].filter(Boolean).join('\n');

    return fullPrompt;
  }

  /**
   * Execute the task strategy and get a response
   */
  async execute(context: TaskContext): Promise<ChatResponse> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      const fullPrompt = await this.formatPrompt(context);

      const response = await aiService.generateText({
        prompt: fullPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt: null, // System prompt is already included in fullPrompt
      });

      const message = {
        role: 'assistant' as const,
        content: response.content,
        conversationId: context.conversationId,
        createdAt: new Date(),
      };

      return {
        status: 'success',
        message,
        model: response.model,
        processingTime: response.usage.durationMs,
        promptTokens: response.usage.promptTokens,
        completionTokens: response.usage.completionTokens,
        cost: response.usage.cost,
        durationMs: response.usage.durationMs,
      };
    } catch (error) {
      logger.error('Error in project onboarding task strategy execute:', error);
      throw error;
    }
  }

  /**
   * Stream the response from the task strategy
   */
  async *streamExecute(context: TaskContext): AsyncGenerator<any, void, unknown> {
    try {
      const aiService = (await import('../../ai')).default;
      const modelId = context.options.modelId || this.getDefaultModel();
      const maxTokens = context.options.maxTokens || this.getDefaultMaxTokens();
      const temperature = context.options.temperature || this.getDefaultTemperature();
      const systemPrompt = await this.loadSystemPromptFromConfig();
      const fullPrompt = await this.formatPrompt(context);

      const stream = await aiService.streamText({
        prompt: fullPrompt,
        model: modelId,
        maxTokens,
        temperature,
        systemPrompt: null, // System prompt is already included in fullPrompt
      });

      let fullContent = '';
      let usage = {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
        cost: 0,
        duration_ms: 0,
      };
      let modelUsed = modelId;

      for await (const chunk of stream) {
        let content = '';
        if (typeof chunk === 'string') {
          content = chunk;
        } else if (chunk && typeof chunk === 'object') {
          if (chunk.content) {
            content = chunk.content;
          } else if (chunk.choices && chunk.choices.length > 0) {
            const delta = chunk.choices[0].delta;
            if (delta && delta.content) {
              content = delta.content;
            }
          }
        }

        if (content) {
          fullContent += content;
          yield {
            content,
            done: false,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.usage) {
          usage = {
            prompt_tokens: chunk.usage.prompt_tokens || 0,
            completion_tokens: chunk.usage.completion_tokens || 0,
            total_tokens: chunk.usage.total_tokens || 0,
            cost: chunk.usage.cost || 0,
            duration_ms: chunk.usage.duration_ms || 0,
          };
        }

        if (chunk && typeof chunk === 'object' && chunk.model) {
          modelUsed = chunk.model;
        }
      }

      yield {
        content: '',
        done: true,
        usage,
        model: modelUsed,
      };
    } catch (error) {
      logger.error('Error in project onboarding task strategy streamExecute:', error);
      throw error;
    }
  }
}