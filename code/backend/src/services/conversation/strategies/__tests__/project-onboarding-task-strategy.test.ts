/**
 * Project Onboarding Task Strategy Test
 * End-to-end test for project creation from interview responses
 */

import { ProjectOnboardingTaskStrategy } from '../project-onboarding-task-strategy';
import { TaskContext } from '../../task-strategy.interface';
import { ChatResponse } from '../../../../common/types/conversation.types';

describe('ProjectOnboardingTaskStrategy', () => {
  let strategy: ProjectOnboardingTaskStrategy;

  beforeEach(() => {
    strategy = new ProjectOnboardingTaskStrategy();
  });

  test('should create a complete project from interview responses', async () => {
    // Mock conversation messages with proper database-like structure
    const mockMessages = [
      {
        id: 1,
        conversation_id: 999,
        role: 'assistant',
        content: 'What kind of AI/ML project would you like to build?',
        model: 'claude-3.5-haiku',
        prompt_tokens: 50,
        completion_tokens: 20,
        cost: 0.001,
        duration_ms: 1000,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0
      },
      {
        id: 2,
        conversation_id: 999,
        role: 'user',
        content: 'I want to build a productivity app that helps users track their tasks and generate insights using AI',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0
      },
      {
        id: 3,
        conversation_id: 999,
        role: 'assistant',
        content: 'That sounds great! What technology stack would you prefer?',
        model: 'claude-3.5-haiku',
        prompt_tokens: 60,
        completion_tokens: 15,
        cost: 0.001,
        duration_ms: 800,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0
      },
      {
        id: 4,
        conversation_id: 999,
        role: 'user',
        content: 'I would like to use React for the frontend and Node.js for the backend, with a database to store user data',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0
      },
      {
        id: 5,
        conversation_id: 999,
        role: 'user',
        content: 'Yes, let\'s build this project! I\'m ready to create it.',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0
      }
    ];

    // Create mock context
    const mockContext: TaskContext = {
      conversationId: 999,
      messages: mockMessages.slice(0, -1), // All but the last message
      options: {
        strategy: 'project_onboarding'
      }
    };

    // Create mock response (the final user message that should trigger creation)
    const mockResponse: ChatResponse = {
      message: mockMessages[mockMessages.length - 1].content,
      conversationId: 999,
      messageId: mockMessages[mockMessages.length - 1].id.toString()
    };

    console.log('🧪 [TEST] Starting project creation test...');
    console.log('🧪 [TEST] Simulating completed interview with trigger phrase: "let\'s build this project"');

    // Process the response that should trigger project creation
    const result = strategy.processResponse(mockResponse, mockContext);

    // Verify the response is returned (synchronous part)
    expect(result).toBeDefined();
    expect(result.message).toBe(mockResponse.message);

    console.log('🧪 [TEST] Response processed, waiting for async project creation...');

    // Wait a bit for the async project creation to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🧪 [TEST] Test completed successfully');
  }, 60000); // 60 second timeout for full project creation

  test('should not trigger project creation for incomplete conversation', async () => {
    // Mock incomplete conversation
    const mockMessages = [
      {
        id: '1',
        role: 'assistant', 
        content: 'What kind of project would you like to build?',
        createdAt: new Date()
      },
      {
        id: '2',
        role: 'user',
        content: 'I\'m not sure yet, maybe something with AI',
        createdAt: new Date()
      }
    ];

    const mockContext: TaskContext = {
      conversationId: 998,
      messages: mockMessages.slice(0, -1),
      options: {
        strategy: 'project_onboarding'
      }
    };

    const mockResponse: ChatResponse = {
      message: mockMessages[mockMessages.length - 1].content,
      conversationId: 998,
      messageId: mockMessages[mockMessages.length - 1].id
    };

    console.log('🧪 [TEST] Testing incomplete conversation (should not trigger)...');

    const result = strategy.processResponse(mockResponse, mockContext);

    expect(result).toBeDefined();
    console.log('🧪 [TEST] Incomplete conversation handled correctly');
  });

  test('should trigger project creation with explicit build command', async () => {
    // Mock conversation with explicit build command
    const mockMessages = [
      {
        id: '1',
        role: 'user',
        content: 'I have a productivity app idea with React and Node.js. Can you build this project for me?',
        createdAt: new Date()
      }
    ];

    const mockContext: TaskContext = {
      conversationId: 997,
      messages: [],
      options: {
        strategy: 'project_onboarding'
      }
    };

    const mockResponse: ChatResponse = {
      message: mockMessages[0].content,
      conversationId: 997,
      messageId: mockMessages[0].id
    };

    console.log('🧪 [TEST] Testing explicit build command...');

    const result = strategy.processResponse(mockResponse, mockContext);

    expect(result).toBeDefined();

    // Wait for async processing
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('🧪 [TEST] Explicit build command handled');
  });
});