/**
 * Unified Conversation Service
 *
 * This service consolidates the functionality of the conversation service and conversation task service
 * using a strategy pattern to handle different task types.
 */

import {
  conversations,
  messages,
  ConversationStatus,
} from '../../generated/prisma/index';
import { getConversationRepository } from '../../db/repositories/conversation.repository';
import { getSimpleConversationRepository } from '../../db/repositories/conversation.repository.simple';
import aiService from '../ai/index';
import {
  ChatResponse,
  ConversationCreate,
  MessageResponse,
} from '../../common/types/conversation.types';
import { logger } from '../../common/logger';

import { promises as fs } from 'fs';
import path from 'path';
import yaml from 'js-yaml';

import MemoryModule from '../memory/memory.module';
import { taskStrategyRegistry } from './task-strategy-registry';
import { TaskContext, TaskOptions } from './task-strategy.interface';
import geminiService from '../ai/gemini.service';

interface TokenUsage {
  prompt_tokens: number;
  completion_tokens: number;
  total_tokens: number;
  cost?: number;
  duration_ms?: number;
}



class UnifiedConversationService {
  private _memoryService?: any;
  private _conversationRepository?: ReturnType<typeof getConversationRepository>;

  private get memoryService() {
    if (!this._memoryService) {
      this._memoryService = MemoryModule.getInstance().conversationMemory;
    }
    return this._memoryService;
  }

  constructor() {
    // Don't initialize the repository in the constructor
    // It will be initialized lazily when first accessed
  }

  // Lazy getter for conversation repository
  private get conversationRepository() {
    if (!this._conversationRepository) {
      try {
        // Try to get from the Inversify container first
        this._conversationRepository = getConversationRepository();
        logger.info('Using Inversify conversation repository');
      } catch (error) {
        logger.warn('Failed to get conversation repository from container, using simple instance:', error);
        // Use the simple repository that doesn't depend on Inversify
        this._conversationRepository = getSimpleConversationRepository();
        logger.info('Using simple conversation repository');
      }
    }
    return this._conversationRepository;
  }

  /**
   * Create a new conversation
   */
  async createConversation(userId: number, data: ConversationCreate = {}): Promise<conversations> {
    try {
      const now = new Date();
      return await this.conversationRepository.create({
        user_id: userId,
        title: data.title || 'New Conversation',
        settings: {},
        created_at: now,
        updated_at: now,
        category: data.category,
        key_objective: data.keyObjective,
        project_id: data.projectId,
        agent_status: data.agentStatus,
        agent_allowed_actions: data.agentAllowedActions,
        parent_id: data.parentId,
      });
    } catch (error) {
      logger.error('Error creating conversation', error);
      throw error;
    }
  }

  /**
   * Get a conversation by ID
   */
  async getConversation(conversationId: number, userId?: number): Promise<conversations | null> {
    try {
      if (userId) {
        const conversation = await this.conversationRepository.findMany({
          where: {
            id: conversationId,
            user_id: userId,
          },
        });
        return conversation[0] || null;
      }
      return await this.conversationRepository.findById(conversationId);
    } catch (error) {
      logger.error(`Error getting conversation ${conversationId}`, error);
      throw error;
    }
  }

  /**
   * Get all conversations (for debugging purposes)
   */
  async getAllConversations(): Promise<conversations[]> {
    try {
      return await this.conversationRepository.findAll({ take: 100 });
    } catch (error) {
      logger.error('Error getting all conversations', error);
      return [];
    }
  }

  /**
   * Get user conversations with pagination
   */
  async getUserConversations(
    userId: number,
    status?: ConversationStatus,
    skip: number = 0,
    limit: number = 10,
  ): Promise<{ conversations: conversations[]; total: number }> {
    try {
      // Debug logging for userId type
      logger.info(`getUserConversations called with userId: ${userId}, type: ${typeof userId}`);

      // Handle potential string userId (type safety)
      if (typeof userId === 'string') {
        logger.warn(`Converting string userId to number: ${userId}`);
        userId = parseInt(userId, 10);
      }

      // Ensure userId is valid
      if (isNaN(userId) || !userId) {
        logger.error(`Invalid userId: ${userId}`);
        return { conversations: [], total: 0 };
      }

      const whereClause: any = { user_id: userId };
      if (status) {
        whereClause.status = status;
      }

      const conversations = await this.conversationRepository.findMany({
        where: whereClause,
        orderBy: {
          updated_at: 'desc',
        },
        skip,
        take: limit,
      });

      const total = await this.conversationRepository.count(whereClause);

      return { conversations, total };
    } catch (error) {
      logger.error(`Error getting user conversations for user ${userId}`, error);
      return { conversations: [], total: 0 };
    }
  }

  /**
   * Add a message to a conversation
   */
  async addMessage(
    conversationId: number,
    role: string,
    content: string,
    options: {
      model?: string;
      promptTokens?: number;
      completionTokens?: number;
      cost?: number;
      durationMs?: number;
      userFeedback?: string;
      projectId?: number;
    } = {},
  ): Promise<messages> {
    try {
      logger.info(
        `Adding ${role} message to conversation ${conversationId}, content length: ${content.length}`,
      );

      // Ensure conversationId is a number
      const convId =
        typeof conversationId === 'string' ? parseInt(conversationId, 10) : conversationId;

      if (!convId || isNaN(convId)) {
        throw new Error(`Invalid conversation ID: ${conversationId}`);
      }

      // Create the message
      const message = await this.conversationRepository.addMessage(convId, {
        role,
        content,
        model: options.model,
        prompt_tokens: options.promptTokens,
        completion_tokens: options.completionTokens,
        cost: options.cost,
        duration_ms: options.durationMs,
        user_feedback: options.userFeedback,
        project_id: options.projectId,
      });

      // Update conversation's updatedAt
      await this.conversationRepository.update(convId, {
        updated_at: new Date(),
      });

      return message;
    } catch (error) {
      logger.error(`Error adding message to conversation ${conversationId}`, error);
      throw error;
    }
  }

  /**
   * Delete a conversation and all its messages
   */
  async deleteConversation(conversationId: number): Promise<boolean> {
    try {
      await this.conversationRepository.delete(conversationId);
      return true;
    } catch (error) {
      logger.error(`Error deleting conversation ${conversationId}`, error);
      return false;
    }
  }

  /**
   * Convert ConversationMessage to Prisma messages type
   */
  private convertToPrismaMessage(message: any): messages {
    return {
      id: message.id || 0,
      created_at: message.createdAt || new Date(),
      meta_data: message.meta_data || {},
      project_id: message.project_id || null,
      user_feedback: message.user_feedback || null,
      conversation_id: message.conversation_id || 0,
      role: message.role,
      content: message.content,
      model: message.model || null,
      prompt_tokens: message.prompt_tokens || null,
      completion_tokens: message.completion_tokens || null,
      cost: message.cost || null,
      duration_ms: message.duration_ms || null,
      line_start: message.line_start || null,
      line_end: message.line_end || null,
      code_language: message.code_language || null,
      code_snippet: message.code_snippet || null,
      file_path: message.file_path || null,
    };
  }

  /**
   * Convert Prisma message to MessageResponse
   */
  private convertToMessageResponse(message: messages): MessageResponse {
    return {
      id: message.id,
      conversationId: message.conversation_id,
      role: message.role,
      content: message.content,
      createdAt: message.created_at,
      model: message.model || undefined,
      promptTokens: message.prompt_tokens || undefined,
      completionTokens: message.completion_tokens || undefined,
      cost: message.cost || undefined,
      durationMs: message.duration_ms || undefined,
      userFeedback: message.user_feedback || undefined,
      projectId: message.project_id || undefined,
    };
  }

  /**
   * Add a user message and get AI response
   */
  async addUserMessageAndGetResponse(
    conversationId: number,
    userMessage: string,
    options: TaskOptions = {},
  ): Promise<ChatResponse> {
    try {
      // Add user message
      await this.addMessage(conversationId, 'user', userMessage);

      // Get optimized conversation context
      await this.memoryService.getContextWindowOptimizedMessages(
        conversationId,
        {
          targetTokens: options.maxTokens,
          mustIncludeLatest: 1, // Always include the latest user message
        },
      );

      // Get task strategy
      const strategy = taskStrategyRegistry.getStrategy(options.taskType || 'default');
      const context: TaskContext = {
        conversationId,
        userMessage,
        options,
      };

      // Get AI response using strategy
      const response = await strategy.execute(context);

      // Prepare message content - include SVG if available in structured data
      let messageContent = response.message?.content || '';
      
      // Check if we have SVG in structured data and include it in the message content
      if ((response as any).structuredData?.updatedMockup?.improvedSvg) {
        const svgContent = (response as any).structuredData.updatedMockup.improvedSvg;
        console.log('[UnifiedConversation] Including SVG in message content:', svgContent.length, 'characters');
        
        // If we have markdown analysis, append SVG after it
        if (messageContent && messageContent.trim()) {
          messageContent = messageContent + '\n\n' + svgContent;
        } else {
          // If no analysis, just use the SVG
          messageContent = svgContent;
        }
      }

      // Add assistant message
      const assistantMessage = await this.addMessage(
        conversationId,
        'assistant',
        messageContent,
        {
          model: response.model,
          promptTokens: response.promptTokens,
          completionTokens: response.completionTokens,
          cost: response.cost,
          durationMs: response.durationMs,
          // Note: structuredData not supported in addMessage options
        },
      );

      // Convert assistant message to MessageResponse
      const messageResponse = this.convertToMessageResponse(assistantMessage);

      return {
        ...response,
        message: messageResponse,
      };
    } catch (error) {
      logger.error(
        `Error in addUserMessageAndGetResponse for conversation ${conversationId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Create a new conversation with an initial message
   */
  async createConversationWithMessage(
    userId: number,
    initialMessage: string,
    options: TaskOptions = {},
  ): Promise<ChatResponse> {
    try {
      // Create conversation
      const conversation = await this.createConversation(userId, {
        title: options.title,
        category: options.category,
        keyObjective: options.keyObjective,
        projectId: options.projectId,
        agentStatus: options.agentStatus,
        agentAllowedActions: options.agentAllowedActions,
      });

      // Add initial message and get response
      return await this.addUserMessageAndGetResponse(conversation.id, initialMessage, options);
    } catch (error) {
      logger.error('Error in createConversationWithMessage:', error);
      throw error;
    }
  }

  /**
   * Generate a title for a conversation
   */
  async generateTitle(
    conversationId: number,
    initialMessage?: string,
  ): Promise<{ status: string; message?: string }> {
    try {
      // Get the conversation
      const conversation = await this.getConversation(conversationId);
      if (!conversation) {
        throw new Error(`Conversation ${conversationId} not found`);
      }

      // Get the messages
      const messages = await this.conversationRepository.getMessages(conversationId);
      if (messages.length === 0 && !initialMessage) {
        throw new Error(`No messages found for conversation ${conversationId}`);
      }

      // Use the first user message as context for title generation
      const firstUserMessage =
        initialMessage || messages.find((m: messages) => m.role === 'user')?.content || '';

      // Get title generation prompt from config
      const configPath = path.join(process.cwd(), 'config', 'prompts.yaml');
      const configFile = await fs.readFile(configPath, 'utf8');
      const config = yaml.load(configFile) as any;
      const titlePrompt =
        config.title_generation?.prompt ||
        'Generate a short, descriptive title (max 6 words) for a conversation that starts with this message: "{message}"';

      // Replace placeholder with actual message
      const prompt = titlePrompt.replace('{message}', firstUserMessage.substring(0, 500));

      // Generate title using a fast model
      const titleResponse = await aiService.generateText({
        prompt,
        provider: 'gemini',
        model: 'gemini-2.0-flash',
        maxTokens: 20,
        temperature: 0.3,
      });

      // Clean up the title (remove quotes, etc.)
      const cleanTitle = titleResponse.content.replace(/^["']|["']$/g, '').trim();

      // Update the conversation title
      await this.conversationRepository.update(conversationId, {
        title: cleanTitle,
      });

      return {
        status: 'success',
        message: cleanTitle,
      };
    } catch (error: any) {
      logger.error(`Error generating title for conversation ${conversationId}:`, error);
      return {
        status: 'error',
        message: `Error generating title: ${error.message}`,
      };
    }
  }

  /**
   * Get messages for a conversation with memory optimization
   */
  async getMessages(conversationId: number, skip: number = 0, limit?: number): Promise<messages[]> {
    try {
      // Use regular pagination if specific range is requested
      if (skip > 0 || limit) {
        return await this.conversationRepository.getMessages(conversationId, skip, limit);
      }

      // Get optimized messages if no pagination is requested
      const optimizedMessages = await this.memoryService.getOptimizedMessages(conversationId, {
        maxMessages: 50, // Default limit for optimized retrieval
      });

      // Convert optimized messages to Prisma messages type
      return optimizedMessages.map((msg) => this.convertToPrismaMessage(msg));
    } catch (error) {
      logger.error(`Error getting messages for conversation ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Update a conversation
   */
  async updateConversation(conversationId: number, data: any): Promise<conversations> {
    try {
      return await this.conversationRepository.update(conversationId, data);
    } catch (error) {
      logger.error(`Error updating conversation ${conversationId}`, error);
      throw error;
    }
  }

  /**
   * Stream response from AI
   */
  async streamResponse(conversationId: number, prompt: string, options: TaskOptions = {}) {
    try {
      // Get optimized conversation context based on memoryCount if provided
      let messages: any[] = [];

      // Check if this is a new conversation with no messages yet
      const existingMessages = await this.conversationRepository.getMessagesForLlm(conversationId);
      const isNewConversation = existingMessages.length === 0;

      if (isNewConversation) {
        logger.info(
          `New conversation ${conversationId} detected - no previous messages to include in memory`,
        );
        // For new conversations, we don't need to retrieve any previous messages
        messages = [];
      } else if (options.memoryCount !== undefined) {
        // If memoryCount is 0, don't include any previous messages
        if (options.memoryCount > 0) {
          logger.info(
            `Getting ${options.memoryCount} previous messages for conversation ${conversationId}`,
          );
          messages = await this.memoryService.getContextWindowOptimizedMessages(conversationId, {
            targetTokens: options.maxTokens,
            mustIncludeLatest: options.memoryCount, // Use memoryCount as mustIncludeLatest
          });
          logger.info(`Retrieved ${messages.length} messages for context window`);
        } else {
          logger.info(`Memory disabled (memoryCount=0) for conversation ${conversationId}`);
        }
      } else {
        // Default behavior - get optimized messages
        messages = await this.memoryService.getContextWindowOptimizedMessages(conversationId, {
          targetTokens: options.maxTokens,
          mustIncludeLatest: 10, // Default to including the 10 most recent messages
        });
        logger.info(`Using default memory optimization, retrieved ${messages.length} messages`);
      }

      // Get task strategy - use intelligent routing if no taskType provided
      let taskType = options.taskType;
      
      if (!taskType) {
        console.log('🔧 [UNIFIED-CONVERSATION] No taskType provided, using intelligent task classification...');
        taskType = await this.classifyTaskType(prompt);
        console.log('🔧 [UNIFIED-CONVERSATION] Intelligent classification result:', taskType);
      }
      
      console.log('🔧 [UNIFIED-CONVERSATION] Getting strategy for taskType:', taskType);
      const strategy = taskStrategyRegistry.getStrategy(taskType || 'chat');
      console.log('🔧 [UNIFIED-CONVERSATION] Strategy selected:', strategy.constructor.name);
      console.log('🔧 [UNIFIED-CONVERSATION] Strategy task type:', strategy.getTaskType());
      
      // Check if strategy has getDefaultModel method
      if (typeof strategy.getDefaultModel === 'function') {
        console.log('🔧 [UNIFIED-CONVERSATION] Strategy default model:', strategy.getDefaultModel());
      }
      
      const context: TaskContext = {
        conversationId,
        userMessage: prompt,
        messages: messages.map((msg) => this.convertToPrismaMessage(msg)),
        options,
      };
      
      console.log('🔧 [UNIFIED-CONVERSATION] Context options:', JSON.stringify(options, null, 2));

      // Create an enhanced stream that includes the classified task type
      const originalStream = strategy.streamExecute(context);
      
      return this.enhanceStreamWithTaskType(originalStream, taskType);
    } catch (error) {
      logger.error(`Error in streamResponse for conversation ${conversationId}:`, error);
      throw error;
    }
  }

  /**
   * Classify task type using Gemini LLM with function calling
   * Intelligently determines the appropriate task type based on user intent
   */
  private async classifyTaskType(prompt: string): Promise<string> {
    try {
      logger.info(`🔧 [TASK-CLASSIFIER] Classifying task with Gemini LLM: "${prompt.substring(0, 100)}..."`);

      // Define the function schema for task classification
      const classificationTool = {
        type: 'function',
        function: {
          name: 'classify_task',
          description: 'Classify the user request into one of the predefined task types',
          parameters: {
            type: 'object',
            properties: {
              task_type: {
                type: 'string',
                enum: ['slide_generation', 'svg_mockup', 'code_generation', 'test_cases', 'chat'],
                description: 'CRITICAL: Classify the task type based on these EXACT rules:\n' +
                           '- slide_generation: ANY request mentioning "slides", "presentation", "pitch", "slideshow" - MUST be slide_generation, NOT chat\n' +
                           '- svg_mockup: Creating UI mockups, wireframes, dashboards, designs\n' +
                           '- code_generation: Writing code, components, functions, applications\n' +
                           '- test_cases: Creating tests, test suites, unit tests\n' +
                           '- chat: ONLY for general questions, explanations, or pure conversation\n' +
                           'IMPORTANT: "Build slides" = slide_generation, NOT chat!'
              },
              confidence: {
                type: 'number',
                description: 'Confidence level from 0.0 to 1.0'
              },
              reasoning: {
                type: 'string',
                description: 'Brief explanation of why this task type was chosen'
              }
            },
            required: ['task_type', 'confidence', 'reasoning']
          }
        }
      };

      // Call Gemini with function calling
      const response = await geminiService.chatWithFunctionCalling({
        messages: [
          {
            role: 'system',
            content: `You are a task classifier for a development IDE. Analyze user requests and classify them into the appropriate task type. 

IMPORTANT CLASSIFICATION RULES:
- If the user mentions "slides", "presentation", "pitch deck", or wants to create slides: classify as "slide_generation"
- If the user wants to create UI mockups, wireframes, dashboards, or designs: classify as "svg_mockup"  
- If the user wants to write code, functions, components, or applications: classify as "code_generation"
- If the user wants to create tests, unit tests, or test cases: classify as "test_cases"
- Only use "chat" for general questions, explanations, or conversations

Be very specific about slide-related requests - they should always be "slide_generation", not "chat".`
          },
          {
            role: 'user',
            content: `Classify this request - be very specific about slide requests: "${prompt}"`
          }
        ],
        model: 'gemini-2.0-flash',
        tools: [classificationTool],
        temperature: 0.1, // Low temperature for consistent classification
        maxTokens: 1000
      });

      // Parse the function call response
      if (response?.choices?.[0]?.message?.tool_calls?.[0]) {
        const toolCall = response.choices[0].message.tool_calls[0];
        if (toolCall.function?.name === 'classify_task') {
          try {
            const args = JSON.parse(toolCall.function.arguments);
            const taskType = args.task_type;
            const confidence = args.confidence || 0.5;
            const reasoning = args.reasoning || 'No reasoning provided';

            logger.info(`🔧 [TASK-CLASSIFIER] Gemini classified as: ${taskType} (confidence: ${confidence})`);
            logger.info(`🔧 [TASK-CLASSIFIER] Reasoning: ${reasoning}`);

            // Validate the task type
            const validTaskTypes = ['slide_generation', 'svg_mockup', 'code_generation', 'test_cases', 'chat'];
            if (validTaskTypes.includes(taskType)) {
              return taskType;
            } else {
              logger.warn(`🔧 [TASK-CLASSIFIER] Invalid task type returned: ${taskType}, defaulting to chat`);
              return 'chat';
            }
          } catch (parseError) {
            logger.error('🔧 [TASK-CLASSIFIER] Error parsing function call arguments:', parseError);
            return 'chat';
          }
        }
      }

      // If no function call was made, extract from response content
      const responseContent = response?.choices?.[0]?.message?.content || '';
      logger.warn('🔧 [TASK-CLASSIFIER] No function call in response, attempting text parsing');
      logger.info('🔧 [TASK-CLASSIFIER] Response content:', responseContent);

      // Fallback: try to extract task type from text response
      const taskTypeMatch = responseContent.match(/task_type["']?\s*:\s*["']?(\w+)["']?/i);
      if (taskTypeMatch) {
        const extractedTaskType = taskTypeMatch[1];
        logger.info(`🔧 [TASK-CLASSIFIER] Extracted task type from text: ${extractedTaskType}`);
        const validTaskTypes = ['slide_generation', 'svg_mockup', 'code_generation', 'test_cases', 'chat'];
        if (validTaskTypes.includes(extractedTaskType)) {
          return extractedTaskType;
        }
      }

      // Final fallback: use simple heuristics
      logger.warn('🔧 [TASK-CLASSIFIER] Gemini function calling failed, using simple heuristics');
      const lowerPrompt = prompt.toLowerCase();
      
      if (lowerPrompt.includes('slide') || lowerPrompt.includes('presentation') || lowerPrompt.includes('pitch')) {
        logger.info('🔧 [TASK-CLASSIFIER] Fallback classified as slide_generation (keyword: slide/presentation/pitch)');
        return 'slide_generation';
      } else if (lowerPrompt.includes('mockup') || lowerPrompt.includes('dashboard') || lowerPrompt.includes('design')) {
        logger.info('🔧 [TASK-CLASSIFIER] Fallback classified as svg_mockup (keyword: mockup/dashboard/design)');
        return 'svg_mockup';
      } else if (lowerPrompt.includes('test') && (lowerPrompt.includes('case') || lowerPrompt.includes('unit'))) {
        logger.info('🔧 [TASK-CLASSIFIER] Fallback classified as test_cases (keyword: test case/unit)');
        return 'test_cases';
      } else if (lowerPrompt.includes('code') || lowerPrompt.includes('function') || lowerPrompt.includes('component')) {
        logger.info('🔧 [TASK-CLASSIFIER] Fallback classified as code_generation (keyword: code/function/component)');
        return 'code_generation';
      }

      logger.info('🔧 [TASK-CLASSIFIER] Fallback classified as chat (no specific keywords found)');
      return 'chat';

    } catch (error) {
      logger.error('🔧 [TASK-CLASSIFIER] Error in Gemini task classification:', error);
      
      // Emergency fallback to simple keyword matching
      logger.warn('🔧 [TASK-CLASSIFIER] Using emergency fallback classification');
      const lowerPrompt = prompt.toLowerCase();
      if (lowerPrompt.includes('slide') || lowerPrompt.includes('presentation')) {
        logger.info('🔧 [TASK-CLASSIFIER] Emergency fallback: slide_generation (slide/presentation keyword)');
        return 'slide_generation';
      } else if (lowerPrompt.includes('mockup') || lowerPrompt.includes('dashboard')) {
        logger.info('🔧 [TASK-CLASSIFIER] Emergency fallback: svg_mockup (mockup/dashboard keyword)');
        return 'svg_mockup';
      } else if (lowerPrompt.includes('test')) {
        logger.info('🔧 [TASK-CLASSIFIER] Emergency fallback: test_cases (test keyword)');
        return 'test_cases';
      } else if (lowerPrompt.includes('code') || lowerPrompt.includes('function')) {
        logger.info('🔧 [TASK-CLASSIFIER] Emergency fallback: code_generation (code/function keyword)');
        return 'code_generation';
      }
      
      logger.info('🔧 [TASK-CLASSIFIER] Emergency fallback: chat (default)');
      return 'chat';
    }
  }

  /**
   * Enhance the streaming response to include task type information
   */
  private async *enhanceStreamWithTaskType(originalStream: AsyncGenerator<any, void, unknown>, taskType: string) {
    try {
      for await (const chunk of originalStream) {
        if (chunk.done && chunk.usage) {
          // Include the classified task type in the final chunk
          yield {
            ...chunk,
            taskType: taskType,
            routedBy: 'gemini-llm-classification'
          };
        } else {
          // Pass through content chunks unchanged
          yield chunk;
        }
      }
    } catch (error) {
      logger.error('Error in enhanceStreamWithTaskType:', error);
      throw error;
    }
  }


}

// Create and export a singleton instance
export const unifiedConversationService = new UnifiedConversationService();

// Also export the class for dependency injection
export { UnifiedConversationService };
