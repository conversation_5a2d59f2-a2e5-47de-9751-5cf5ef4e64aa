/**
 * Quality Analysis Service
 * Analyzes generated code for quality, security, and performance metrics
 */

import { injectable } from 'tsyringe';
import { CodeGenerationResult } from '../project-creation/types/generation-results.types';

export interface QualityAnalysisResult {
  overallScore: number;
  categories: QualityCategory[];
  recommendations: QualityRecommendation[];
  securityScan: SecurityScanResult;
  performanceAnalysis: PerformanceAnalysisResult;
  metadata: {
    analyzedAt: Date;
    analysisVersion: string;
    analysisTime: number;
  };
}

export interface QualityCategory {
  name: string;
  score: number;
  weight: number;
  issues: QualityIssue[];
  suggestions: string[];
}

export interface QualityIssue {
  severity: 'critical' | 'high' | 'medium' | 'low';
  type: string;
  description: string;
  file?: string;
  line?: number;
  fix?: string;
}

export interface QualityRecommendation {
  category: string;
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  effort: 'low' | 'medium' | 'high';
  impact: 'low' | 'medium' | 'high';
}

export interface SecurityScanResult {
  score: number;
  vulnerabilities: SecurityVulnerability[];
  bestPractices: SecurityBestPractice[];
}

export interface SecurityVulnerability {
  severity: 'critical' | 'high' | 'medium' | 'low';
  type: string;
  description: string;
  file: string;
  line?: number;
  cwe?: string;
  fix: string;
}

export interface SecurityBestPractice {
  category: string;
  implemented: boolean;
  description: string;
  recommendation?: string;
}

export interface PerformanceAnalysisResult {
  score: number;
  metrics: PerformanceMetric[];
  optimizations: PerformanceOptimization[];
}

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  benchmark: number;
  status: 'good' | 'warning' | 'poor';
}

export interface PerformanceOptimization {
  type: string;
  description: string;
  expectedImprovement: string;
  complexity: 'low' | 'medium' | 'high';
}

@injectable()
export class QualityAnalysisService {

  /**
   * Analyze code quality comprehensively
   */
  async analyzeCodeQuality(generatedCode: CodeGenerationResult): Promise<QualityAnalysisResult> {
    const startTime = Date.now();
    
    try {
      console.log('🔍 [QUALITY] Starting comprehensive quality analysis...');
      
      // Run parallel analysis on different aspects
      const [
        codeQualityCategories,
        securityScan,
        performanceAnalysis
      ] = await Promise.all([
        this.analyzeCodeQualityCategories(generatedCode),
        this.runSecurityScan(generatedCode),
        this.analyzePerformance(generatedCode)
      ]);

      // Calculate overall score
      const overallScore = this.calculateOverallScore(codeQualityCategories, securityScan, performanceAnalysis);
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(
        codeQualityCategories,
        securityScan,
        performanceAnalysis
      );

      const result: QualityAnalysisResult = {
        overallScore,
        categories: codeQualityCategories,
        recommendations,
        securityScan,
        performanceAnalysis,
        metadata: {
          analyzedAt: new Date(),
          analysisVersion: '1.0.0',
          analysisTime: Date.now() - startTime
        }
      };

      console.log(`✅ [QUALITY] Analysis complete: Overall score ${overallScore}/100`);
      return result;
      
    } catch (error) {
      console.error('❌ [QUALITY] Error during quality analysis:', error);
      throw new Error(`Quality analysis failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Run security scan on generated code
   */
  async runSecurityScan(code: CodeGenerationResult): Promise<SecurityScanResult> {
    const vulnerabilities: SecurityVulnerability[] = [];
    const bestPractices: SecurityBestPractice[] = [];

    // Analyze each file for security issues
    for (const file of code.codeBase.files) {
      vulnerabilities.push(...this.scanFileForVulnerabilities(file));
    }

    // Check security best practices
    bestPractices.push(...this.checkSecurityBestPractices(code));

    // Calculate security score
    const score = this.calculateSecurityScore(vulnerabilities, bestPractices);

    return {
      score,
      vulnerabilities,
      bestPractices
    };
  }

  /**
   * Analyze performance characteristics
   */
  async analyzePerformance(code: CodeGenerationResult): Promise<PerformanceAnalysisResult> {
    const metrics = this.calculatePerformanceMetrics(code);
    const optimizations = this.identifyOptimizations(code, metrics);
    const score = this.calculatePerformanceScore(metrics);

    return {
      score,
      metrics,
      optimizations
    };
  }

  /**
   * Calculate overall quality score
   */
  calculateOverallScore(
    categories: QualityCategory[],
    security: SecurityScanResult,
    performance: PerformanceAnalysisResult
  ): number {
    // Weighted average of all categories
    const codeQualityWeight = 0.5;
    const securityWeight = 0.3;
    const performanceWeight = 0.2;

    const codeQualityScore = categories.reduce((total, cat) => 
      total + (cat.score * cat.weight), 0
    ) / categories.reduce((total, cat) => total + cat.weight, 0);

    const overallScore = (
      codeQualityScore * codeQualityWeight +
      security.score * securityWeight +
      performance.score * performanceWeight
    );

    return Math.round(Math.max(0, Math.min(100, overallScore)));
  }

  // Private analysis methods

  private async analyzeCodeQualityCategories(code: CodeGenerationResult): Promise<QualityCategory[]> {
    return [
      this.analyzeCodeStructure(code),
      this.analyzeDocumentation(code),
      this.analyzeTestCoverage(code),
      this.analyzeComplexity(code),
      this.analyzeMaintainability(code)
    ];
  }

  private analyzeCodeStructure(code: CodeGenerationResult): QualityCategory {
    const issues: QualityIssue[] = [];
    const suggestions: string[] = [];
    let score = 100;

    // Check file organization
    const hasProperStructure = code.codeBase.structure.directories.length >= 3;
    if (!hasProperStructure) {
      score -= 20;
      issues.push({
        severity: 'medium',
        type: 'structure',
        description: 'Project structure could be better organized',
        fix: 'Create separate directories for components, services, and utilities'
      });
    }

    // Check for consistent naming
    const inconsistentNaming = this.checkNamingConsistency(code.codeBase.files);
    if (inconsistentNaming > 0) {
      score -= inconsistentNaming * 5;
      issues.push({
        severity: 'low',
        type: 'naming',
        description: `Found ${inconsistentNaming} files with inconsistent naming`,
        fix: 'Use consistent naming conventions (camelCase for files, PascalCase for components)'
      });
    }

    // Check import organization
    const poorImports = this.checkImportOrganization(code.codeBase.files);
    if (poorImports > 0) {
      score -= poorImports * 3;
      suggestions.push('Organize imports: external libraries first, then internal modules');
    }

    return {
      name: 'Code Structure',
      score: Math.max(0, score),
      weight: 0.25,
      issues,
      suggestions
    };
  }

  private analyzeDocumentation(code: CodeGenerationResult): QualityCategory {
    const issues: QualityIssue[] = [];
    const suggestions: string[] = [];
    let score = 100;

    // Check for README
    const hasReadme = code.codeBase.files.some(f => f.path.toLowerCase().includes('readme'));
    if (!hasReadme) {
      score -= 30;
      issues.push({
        severity: 'high',
        type: 'documentation',
        description: 'Missing README file',
        fix: 'Add a comprehensive README with setup instructions'
      });
    }

    // Check for inline documentation
    const documentedFiles = code.codeBase.files.filter(f => 
      f.content.includes('/**') || f.content.includes('//')
    );
    const documentationRatio = documentedFiles.length / code.codeBase.files.length;
    
    if (documentationRatio < 0.5) {
      score -= 25;
      issues.push({
        severity: 'medium',
        type: 'comments',
        description: 'Low inline documentation coverage',
        fix: 'Add comments and JSDoc documentation to functions and components'
      });
    }

    if (documentationRatio < 0.8) {
      suggestions.push('Consider adding more inline documentation for better maintainability');
    }

    return {
      name: 'Documentation',
      score: Math.max(0, score),
      weight: 0.15,
      issues,
      suggestions
    };
  }

  private analyzeTestCoverage(code: CodeGenerationResult): QualityCategory {
    const issues: QualityIssue[] = [];
    const suggestions: string[] = [];
    let score = 85; // Default good score for generated tests

    // Check for test files
    const testFiles = code.codeBase.files.filter(f => 
      f.path.includes('.test.') || f.path.includes('.spec.')
    );
    
    const codeFiles = code.codeBase.files.filter(f => 
      !f.path.includes('.test.') && !f.path.includes('.spec.') && 
      f.type === 'component' || f.type === 'service'
    );

    const testCoverageRatio = testFiles.length / Math.max(1, codeFiles.length);
    
    if (testCoverageRatio < 0.6) {
      score -= 30;
      issues.push({
        severity: 'high',
        type: 'test_coverage',
        description: 'Low test coverage',
        fix: 'Add unit tests for components and services'
      });
    } else if (testCoverageRatio < 0.8) {
      score -= 15;
      suggestions.push('Consider adding more comprehensive test coverage');
    }

    return {
      name: 'Test Coverage',
      score: Math.max(0, score),
      weight: 0.2,
      issues,
      suggestions
    };
  }

  private analyzeComplexity(code: CodeGenerationResult): QualityCategory {
    const issues: QualityIssue[] = [];
    const suggestions: string[] = [];
    let score = 90;

    // Check for overly complex files
    const complexFiles = code.codeBase.files.filter(f => f.complexity === 'high');
    
    if (complexFiles.length > 0) {
      score -= complexFiles.length * 15;
      issues.push({
        severity: 'medium',
        type: 'complexity',
        description: `${complexFiles.length} files have high complexity`,
        fix: 'Break down complex files into smaller, focused modules'
      });
    }

    // Check average file size
    const avgFileSize = code.codeBase.files.reduce((total, file) => 
      total + file.content.split('\n').length, 0
    ) / code.codeBase.files.length;

    if (avgFileSize > 200) {
      score -= 10;
      suggestions.push('Consider breaking down large files for better maintainability');
    }

    return {
      name: 'Complexity',
      score: Math.max(0, score),
      weight: 0.2,
      issues,
      suggestions
    };
  }

  private analyzeMaintainability(code: CodeGenerationResult): QualityCategory {
    const issues: QualityIssue[] = [];
    const suggestions: string[] = [];
    let score = 85;

    // Check for proper error handling
    const filesWithErrorHandling = code.codeBase.files.filter(f => 
      f.content.includes('try') || f.content.includes('catch') || f.content.includes('throw')
    );
    
    const errorHandlingRatio = filesWithErrorHandling.length / Math.max(1, code.codeBase.files.length);
    
    if (errorHandlingRatio < 0.3) {
      score -= 20;
      issues.push({
        severity: 'medium',
        type: 'error_handling',
        description: 'Limited error handling implementation',
        fix: 'Add proper error handling with try-catch blocks'
      });
    }

    // Check for type safety (TypeScript usage)
    const typedFiles = code.codeBase.files.filter(f => 
      f.language === 'typescript' || f.content.includes('interface') || f.content.includes('type ')
    );
    
    const typeRatio = typedFiles.length / Math.max(1, code.codeBase.files.length);
    
    if (typeRatio > 0.8) {
      score += 5; // Bonus for good TypeScript usage
      suggestions.push('Excellent use of TypeScript for type safety');
    } else if (typeRatio < 0.5) {
      score -= 10;
      suggestions.push('Consider adding more type annotations for better code safety');
    }

    return {
      name: 'Maintainability',
      score: Math.max(0, score),
      weight: 0.2,
      issues,
      suggestions
    };
  }

  private scanFileForVulnerabilities(file: any): SecurityVulnerability[] {
    const vulnerabilities: SecurityVulnerability[] = [];

    // Check for common security issues
    if (file.content.includes('eval(')) {
      vulnerabilities.push({
        severity: 'critical',
        type: 'code_injection',
        description: 'Use of eval() function detected',
        file: file.path,
        cwe: 'CWE-95',
        fix: 'Avoid using eval(). Use JSON.parse() for data parsing or safer alternatives'
      });
    }

    if (file.content.includes('innerHTML') && !file.content.includes('sanitize')) {
      vulnerabilities.push({
        severity: 'high',
        type: 'xss',
        description: 'Potential XSS vulnerability with innerHTML',
        file: file.path,
        cwe: 'CWE-79',
        fix: 'Use textContent or sanitize HTML content before setting innerHTML'
      });
    }

    if (file.content.includes('process.env') && file.content.includes('console.log')) {
      vulnerabilities.push({
        severity: 'medium',
        type: 'information_disclosure',
        description: 'Environment variables may be logged',
        file: file.path,
        cwe: 'CWE-532',
        fix: 'Remove console.log statements with sensitive data in production'
      });
    }

    return vulnerabilities;
  }

  private checkSecurityBestPractices(code: CodeGenerationResult): SecurityBestPractice[] {
    const practices: SecurityBestPractice[] = [];

    // Check for HTTPS configuration
    const hasHttpsConfig = code.codeBase.files.some(f => 
      f.content.includes('https') || f.content.includes('secure: true')
    );
    
    practices.push({
      category: 'Transport Security',
      implemented: hasHttpsConfig,
      description: 'HTTPS configuration',
      recommendation: hasHttpsConfig ? undefined : 'Configure HTTPS for production deployment'
    });

    // Check for input validation
    const hasValidation = code.codeBase.files.some(f => 
      f.content.includes('validate') || f.path.includes('validation')
    );
    
    practices.push({
      category: 'Input Validation',
      implemented: hasValidation,
      description: 'Input validation implementation',
      recommendation: hasValidation ? undefined : 'Implement comprehensive input validation'
    });

    // Check for authentication
    const hasAuth = code.codeBase.files.some(f => 
      f.content.includes('auth') || f.content.includes('token') || f.content.includes('login')
    );
    
    practices.push({
      category: 'Authentication',
      implemented: hasAuth,
      description: 'Authentication mechanism',
      recommendation: hasAuth ? undefined : 'Implement proper authentication and authorization'
    });

    return practices;
  }

  private calculateSecurityScore(
    vulnerabilities: SecurityVulnerability[],
    practices: SecurityBestPractice[]
  ): number {
    let score = 100;

    // Deduct points for vulnerabilities
    vulnerabilities.forEach(vuln => {
      switch (vuln.severity) {
        case 'critical': score -= 25; break;
        case 'high': score -= 15; break;
        case 'medium': score -= 8; break;
        case 'low': score -= 3; break;
      }
    });

    // Deduct points for missing best practices
    const missingPractices = practices.filter(p => !p.implemented).length;
    score -= missingPractices * 10;

    return Math.max(0, score);
  }

  private calculatePerformanceMetrics(code: CodeGenerationResult): PerformanceMetric[] {
    const metrics: PerformanceMetric[] = [];

    // Bundle size estimation
    const estimatedBundleSize = code.codeBase.files.reduce((total, file) => {
      return total + file.content.length;
    }, 0) / 1024; // KB

    metrics.push({
      name: 'Estimated Bundle Size',
      value: Math.round(estimatedBundleSize),
      unit: 'KB',
      benchmark: 250, // 250KB benchmark
      status: estimatedBundleSize < 250 ? 'good' : estimatedBundleSize < 500 ? 'warning' : 'poor'
    });

    // Component count
    const componentCount = code.codeBase.files.filter(f => f.type === 'component').length;
    metrics.push({
      name: 'Component Count',
      value: componentCount,
      unit: 'components',
      benchmark: 20,
      status: componentCount < 20 ? 'good' : componentCount < 40 ? 'warning' : 'poor'
    });

    // Dependency count
    const depCount = code.codeBase.dependencies.production.length + 
                    code.codeBase.dependencies.development.length;
    metrics.push({
      name: 'Total Dependencies',
      value: depCount,
      unit: 'packages',
      benchmark: 50,
      status: depCount < 50 ? 'good' : depCount < 100 ? 'warning' : 'poor'
    });

    return metrics;
  }

  private identifyOptimizations(
    code: CodeGenerationResult,
    metrics: PerformanceMetric[]
  ): PerformanceOptimization[] {
    const optimizations: PerformanceOptimization[] = [];

    // Check bundle size
    const bundleMetric = metrics.find(m => m.name === 'Estimated Bundle Size');
    if (bundleMetric && bundleMetric.status !== 'good') {
      optimizations.push({
        type: 'Bundle Optimization',
        description: 'Implement code splitting and lazy loading',
        expectedImprovement: '30-50% reduction in initial bundle size',
        complexity: 'medium'
      });
    }

    // Check for unnecessary re-renders
    const hasReactMemo = code.codeBase.files.some(f => f.content.includes('React.memo'));
    if (!hasReactMemo && code.codeBase.files.some(f => f.type === 'component')) {
      optimizations.push({
        type: 'React Optimization',
        description: 'Add React.memo and useCallback for performance',
        expectedImprovement: '20-30% faster rendering',
        complexity: 'low'
      });
    }

    // Check for image optimization
    const hasImages = code.codeBase.files.some(f => 
      f.content.includes('.jpg') || f.content.includes('.png') || f.content.includes('.svg')
    );
    if (hasImages) {
      optimizations.push({
        type: 'Asset Optimization',
        description: 'Optimize images and implement lazy loading',
        expectedImprovement: '40-60% faster page load',
        complexity: 'low'
      });
    }

    return optimizations;
  }

  private calculatePerformanceScore(metrics: PerformanceMetric[]): number {
    const scores = metrics.map(metric => {
      switch (metric.status) {
        case 'good': return 100;
        case 'warning': return 70;
        case 'poor': return 40;
        default: return 70;
      }
    });

    return Math.round(scores.reduce((a, b) => a + b, 0) / scores.length);
  }

  private generateRecommendations(
    categories: QualityCategory[],
    security: SecurityScanResult,
    performance: PerformanceAnalysisResult
  ): QualityRecommendation[] {
    const recommendations: QualityRecommendation[] = [];

    // Add recommendations based on category scores
    categories.forEach(category => {
      if (category.score < 80) {
        recommendations.push({
          category: category.name,
          priority: category.score < 60 ? 'high' : 'medium',
          title: `Improve ${category.name}`,
          description: category.suggestions.join('. '),
          effort: 'medium',
          impact: 'high'
        });
      }
    });

    // Add security recommendations
    if (security.score < 80) {
      recommendations.push({
        category: 'Security',
        priority: 'high',
        title: 'Address Security Concerns',
        description: 'Fix identified vulnerabilities and implement security best practices',
        effort: 'high',
        impact: 'high'
      });
    }

    // Add performance recommendations
    if (performance.score < 80) {
      recommendations.push({
        category: 'Performance',
        priority: 'medium',
        title: 'Optimize Performance',
        description: performance.optimizations.map(opt => opt.description).join('. '),
        effort: 'medium',
        impact: 'medium'
      });
    }

    return recommendations;
  }

  // Helper methods
  private checkNamingConsistency(files: any[]): number {
    let inconsistencies = 0;
    
    files.forEach(file => {
      const fileName = file.path.split('/').pop();
      
      // Check component naming (should be PascalCase)
      if (file.type === 'component' && fileName) {
        const nameWithoutExt = fileName.split('.')[0];
        if (nameWithoutExt !== this.pascalCase(nameWithoutExt)) {
          inconsistencies++;
        }
      }
      
      // Check service naming (should be camelCase.service)
      if (file.type === 'service' && fileName) {
        if (!fileName.includes('.service.')) {
          inconsistencies++;
        }
      }
    });
    
    return inconsistencies;
  }

  private checkImportOrganization(files: any[]): number {
    let poorlyOrganized = 0;
    
    files.forEach(file => {
      const lines = file.content.split('\n');
      const importLines = lines.filter((line: string) => line.trim().startsWith('import'));
      
      if (importLines.length > 5) {
        // Check if imports are organized (external first, then internal)
        let hasExternalAfterInternal = false;
        let foundInternal = false;
        
        importLines.forEach((line: string) => {
          const isInternal = line.includes('./') || line.includes('../');
          if (isInternal) {
            foundInternal = true;
          } else if (foundInternal) {
            hasExternalAfterInternal = true;
          }
        });
        
        if (hasExternalAfterInternal) {
          poorlyOrganized++;
        }
      }
    });
    
    return poorlyOrganized;
  }

  private pascalCase(str: string): string {
    return str.split(/[\s-_]+/)
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join('');
  }
}