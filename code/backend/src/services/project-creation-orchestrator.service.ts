/**
 * Project Creation Orchestrator Service
 * Main coordinator for the complete 5-phase project creation flow
 */

import { injectable } from 'tsyringe';
import { StateManagementService } from './project-creation/state-management.service';
import { RequirementsAssemblyService } from './project-creation/requirements-assembly.service';
import { SpecificationGenerationService } from './project-creation/specification-generation.service';
import { TestGenerationService } from './project-creation/test-generation.service';
import { CodeGenerationService } from './project-creation/code-generation.service';
import { QualityAnalysisService } from './quality/quality-analysis.service';
import { DeploymentService } from './deployment/deployment.service';
import { 
  ProjectContext, 
  ProjectPhase, 
  ResumePoint,
  PersonalProfile,
  ProjectDiscoveryResult
} from './project-creation/types/project-context.types';
import {
  SpecificationResult,
  TestSuiteResult,
  CodeGenerationResult
} from './project-creation/types/generation-results.types';

export interface ProjectResult {
  projectId: string;
  status: 'completed' | 'failed' | 'partial';
  results: {
    specifications?: SpecificationResult;
    tests?: TestSuiteResult;
    code?: CodeGenerationResult;
    qualityScore?: number;
    deploymentUrl?: string;
  };
  metadata: {
    totalDuration: number;
    phaseDurations: Record<ProjectPhase, number>;
    qualityScore: number;
    completedAt: Date;
  };
}

export interface ResumeResult {
  resumed: boolean;
  resumePoint: ResumePoint;
  context: ProjectContext;
  nextPhase: ProjectPhase;
}

@injectable()
export class ProjectCreationOrchestrator {
  constructor(
    private stateManagement: StateManagementService,
    private requirementsAssembly: RequirementsAssemblyService,
    private specificationGeneration: SpecificationGenerationService,
    private testGeneration: TestGenerationService,
    private codeGeneration: CodeGenerationService,
    private qualityAnalysis: QualityAnalysisService,
    private deploymentService: DeploymentService
  ) {}

  /**
   * Create a complete project using the 5-phase backwards build methodology
   */
  async createProject(userId: string, initialInput: string): Promise<ProjectResult> {
    const startTime = Date.now();
    const phaseDurations: Record<ProjectPhase, number> = {} as any;
    
    try {
      console.log(`🚀 [ORCHESTRATOR] Starting project creation for user ${userId}`);
      
      // Phase 1: Parse initial input and create context
      const personalProfile = await this.extractPersonalProfile(initialInput);
      const projectDiscovery = await this.extractProjectDiscovery(initialInput);
      
      const context: ProjectContext = {
        personalProfile,
        projectDiscovery,
        folder: this.extractFolderPath(initialInput),
        metadata: {
          createdAt: new Date(),
          userId,
          version: '1.0.0'
        }
      };

      // Create initial state
      const state = await this.stateManagement.createProjectState(userId, context);
      console.log(`📋 [ORCHESTRATOR] Created project state: ${state.projectId}`);

      // Phase 2: Requirements Assembly (1-2 minutes)
      const phaseStart = Date.now();
      await this.stateManagement.updatePhase(state.projectId, 'requirements_assembly', 'in_progress');
      
      const assembledRequirements = await this.requirementsAssembly.assembleRequirements(
        personalProfile,
        projectDiscovery
      );
      
      context.projectDiscovery = { ...projectDiscovery, ...assembledRequirements };
      phaseDurations.requirements_assembly = Date.now() - phaseStart;
      
      await this.stateManagement.updatePhase(
        state.projectId, 
        'requirements_assembly', 
        'completed',
        assembledRequirements
      );
      console.log(`✅ [ORCHESTRATOR] Requirements assembly completed in ${phaseDurations.requirements_assembly}ms`);

      // Phase 3: Specification Generation (Documentation + Slides) (1-2 minutes)
      const specStart = Date.now();
      await this.stateManagement.updatePhase(state.projectId, 'specification_generation', 'in_progress');
      
      const specifications = await this.specificationGeneration.generateSpecifications(context);
      phaseDurations.specification_generation = Date.now() - specStart;
      
      await this.stateManagement.updatePhase(
        state.projectId,
        'specification_generation',
        'completed',
        specifications
      );
      console.log(`📚 [ORCHESTRATOR] Specifications generated in ${phaseDurations.specification_generation}ms`);

      // Phase 4: Test Generation (1 minute)
      const testStart = Date.now();
      await this.stateManagement.updatePhase(state.projectId, 'test_generation', 'in_progress');
      
      const testSuite = await this.testGeneration.generateTestSuite(
        specifications,
        context
      );
      phaseDurations.test_generation = Date.now() - testStart;
      
      await this.stateManagement.updatePhase(
        state.projectId,
        'test_generation',
        'completed',
        testSuite
      );
      console.log(`🧪 [ORCHESTRATOR] Test suite generated in ${phaseDurations.test_generation}ms`);

      // Phase 5: Code Generation (1-2 minutes)
      const codeStart = Date.now();
      await this.stateManagement.updatePhase(state.projectId, 'code_generation', 'in_progress');
      
      const codeGeneration = await this.codeGeneration.generateCodeImplementation(
        specifications,
        testSuite,
        context
      );
      phaseDurations.code_generation = Date.now() - codeStart;
      
      await this.stateManagement.updatePhase(
        state.projectId,
        'code_generation',
        'completed',
        codeGeneration
      );
      console.log(`💻 [ORCHESTRATOR] Code generated in ${phaseDurations.code_generation}ms`);

      // Phase 6: Quality Analysis (30 seconds)
      const qualityStart = Date.now();
      await this.stateManagement.updatePhase(state.projectId, 'quality_analysis', 'in_progress');
      
      const qualityScore = await this.qualityAnalysis.analyzeCodeQuality(codeGeneration);
      phaseDurations.quality_analysis = Date.now() - qualityStart;
      
      await this.stateManagement.updatePhase(
        state.projectId,
        'quality_analysis',
        'completed',
        { qualityScore }
      );
      console.log(`🔍 [ORCHESTRATOR] Quality analysis completed in ${phaseDurations.quality_analysis}ms`);

      // Phase 7: Deployment (optional, 30 seconds)
      let deploymentUrl: string | undefined;
      if (context.projectDiscovery.requirements?.includes('deploy')) {
        const deployStart = Date.now();
        await this.stateManagement.updatePhase(state.projectId, 'deployment', 'in_progress');
        
        const deploymentResult = await this.deploymentService.deployToRailway(
          codeGeneration,
          context
        );
        deploymentUrl = deploymentResult.url;
        phaseDurations.deployment = Date.now() - deployStart;
        
        await this.stateManagement.updatePhase(
          state.projectId,
          'deployment',
          'completed',
          deploymentResult
        );
        console.log(`🚀 [ORCHESTRATOR] Deployment completed in ${phaseDurations.deployment}ms`);
      }

      // Mark project as completed
      await this.stateManagement.updatePhase(state.projectId, 'completed' as ProjectPhase, 'completed');

      const totalDuration = Date.now() - startTime;
      
      const result: ProjectResult = {
        projectId: state.projectId,
        status: 'completed',
        results: {
          specifications,
          tests: testSuite,
          code: codeGeneration,
          qualityScore: qualityScore.overallScore,
          deploymentUrl
        },
        metadata: {
          totalDuration,
          phaseDurations,
          qualityScore: qualityScore.overallScore,
          completedAt: new Date()
        }
      };

      console.log(`🎉 [ORCHESTRATOR] Project creation completed in ${totalDuration}ms with quality score ${qualityScore.overallScore}`);
      return result;

    } catch (error) {
      console.error(`❌ [ORCHESTRATOR] Project creation failed:`, error);
      
      return {
        projectId: 'error',
        status: 'failed',
        results: {},
        metadata: {
          totalDuration: Date.now() - startTime,
          phaseDurations,
          qualityScore: 0,
          completedAt: new Date()
        }
      };
    }
  }

  /**
   * Resume a project from where it left off
   */
  async resumeProject(userId: string): Promise<ResumeResult> {
    try {
      console.log(`🔄 [ORCHESTRATOR] Attempting to resume project for user ${userId}`);
      
      const resumePoint = await this.stateManagement.getResumePoint(userId);
      if (!resumePoint) {
        console.log(`ℹ️ [ORCHESTRATOR] No resumable project found for user ${userId}`);
        return {
          resumed: false,
          resumePoint: null as any,
          context: null as any,
          nextPhase: 'personal_onboarding'
        };
      }

      // Mark as resumed
      const state = await this.stateManagement.loadProjectState(userId);
      if (state) {
        await this.stateManagement.markAsResumed(state.projectId);
      }

      const nextPhase = this.getNextPhase(resumePoint.phase);
      
      console.log(`✅ [ORCHESTRATOR] Resume point found at phase: ${resumePoint.phase}, next: ${nextPhase}`);
      
      return {
        resumed: true,
        resumePoint,
        context: resumePoint.context,
        nextPhase
      };
    } catch (error) {
      console.error(`❌ [ORCHESTRATOR] Error resuming project:`, error);
      throw error;
    }
  }

  /**
   * Get project creation progress
   */
  async getProjectProgress(projectId: string): Promise<any> {
    try {
      const summary = await this.stateManagement.getProjectSummary(projectId);
      return summary;
    } catch (error) {
      console.error(`❌ [ORCHESTRATOR] Error getting progress:`, error);
      return null;
    }
  }

  /**
   * Cancel ongoing project creation
   */
  async cancelProject(projectId: string): Promise<boolean> {
    try {
      // TODO: Implement cancellation logic
      console.log(`🛑 [ORCHESTRATOR] Cancelling project ${projectId}`);
      return true;
    } catch (error) {
      console.error(`❌ [ORCHESTRATOR] Error cancelling project:`, error);
      return false;
    }
  }

  // Private helper methods

  private async extractPersonalProfile(input: string): Promise<PersonalProfile> {
    // Parse personal profile from initial input
    // This would typically use the conversation data
    return {
      name: 'User', // Extract from conversation
      experienceLevel: 'intermediate', // Extract from input
      motivation: 'building', // Extract from input
      preferredStyle: 'direct' // Default
    };
  }

  private async extractProjectDiscovery(input: string): Promise<ProjectDiscoveryResult> {
    // Parse project discovery from initial input
    return {
      projectType: this.extractProjectType(input),
      objective: this.extractObjective(input),
      requirements: this.extractRequirements(input),
      techStack: this.extractTechStack(input),
      features: this.extractFeatures(input),
      complexity: 'moderate'
    };
  }

  private extractProjectType(input: string): string {
    // Simple extraction logic - would be enhanced with NLP
    if (input.toLowerCase().includes('web')) return 'web-app';
    if (input.toLowerCase().includes('mobile')) return 'mobile-app';
    if (input.toLowerCase().includes('api')) return 'api-backend';
    return 'web-app'; // default
  }

  private extractObjective(input: string): string {
    if (input.toLowerCase().includes('new')) return 'build-new';
    if (input.toLowerCase().includes('improve')) return 'improve-existing';
    return 'build-new'; // default
  }

  private extractRequirements(input: string): string[] {
    const requirements: string[] = [];
    if (input.toLowerCase().includes('authentication')) requirements.push('user authentication');
    if (input.toLowerCase().includes('database')) requirements.push('data persistence');
    if (input.toLowerCase().includes('deploy')) requirements.push('deploy');
    return requirements.length > 0 ? requirements : ['basic functionality'];
  }

  private extractTechStack(input: string): string[] {
    const techStack: string[] = [];
    if (input.toLowerCase().includes('react')) techStack.push('React');
    if (input.toLowerCase().includes('node')) techStack.push('Node.js');
    if (input.toLowerCase().includes('typescript')) techStack.push('TypeScript');
    return techStack.length > 0 ? techStack : ['React', 'TypeScript'];
  }

  private extractFeatures(input: string): string[] {
    // Extract features from input
    return ['core features']; // placeholder
  }

  private extractFolderPath(input: string): string {
    // Extract folder path from input
    const pathMatch = input.match(/\/[\w\-\/]+/);
    return pathMatch ? pathMatch[0] : './project';
  }

  private getNextPhase(currentPhase: ProjectPhase): ProjectPhase {
    const phaseOrder: ProjectPhase[] = [
      'personal_onboarding',
      'project_discovery',
      'requirements_assembly',
      'specification_generation',
      'test_generation',
      'code_generation',
      'quality_analysis',
      'deployment'
    ];

    const currentIndex = phaseOrder.indexOf(currentPhase);
    return currentIndex < phaseOrder.length - 1 
      ? phaseOrder[currentIndex + 1] 
      : 'completed' as ProjectPhase;
  }
}