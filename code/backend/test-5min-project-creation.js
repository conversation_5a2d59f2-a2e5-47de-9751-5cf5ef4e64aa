// Simple test script for 5-minute project creation
const { ProjectCreationService } = require('./dist/src/services/project-creation.service');
const fs = require('fs').promises;
const path = require('path');

async function test5MinuteProjectCreation() {
  console.log('🚀 Testing 5-Minute Project Creation System...');
  
  const service = new ProjectCreationService();
  const testProjectPath = '/Users/<USER>/Code/KAPI/demo-5min-test-project';
  
  // Mock interview context
  const mockContext = {
    name: 'Task Management App',
    description: 'Modern collaborative task management application',
    type: 'web-app',
    features: ['real-time collaboration', 'drag-and-drop', 'notifications'],
    frontend: 'React',
    backend: 'Node.js',
    database: 'PostgreSQL',
    userId: 'test-user-123',
    projectPath: testProjectPath
  };
  
  try {
    // Clean up existing project
    await fs.rm(testProjectPath, { recursive: true, force: true });
    
    console.log('📝 Phase 1: Documentation Generation...');
    const startTime = Date.now();
    
    // Generate complete project
    const result = await service.createProject(mockContext, testProjectPath);
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    console.log(`✅ Project created in ${duration.toFixed(2)} seconds`);
    console.log('');
    
    // Verify backwards-build structure
    const expectedFiles = [
      'docs/README.md',
      'docs/vision.md',
      'docs/architecture/system-overview.md',
      'slides/package.json',
      'slides/index.html',
      'slides/css/custom.css',
      'tests/backend/conftest.py',
      'tests/backend/unit/test_example.py',
      'tests/frontend/playwright.config.ts',
      'tests/frontend/e2e/login.spec.ts',
      'package.json',
      'backend/package.json',
      'frontend/package.json',
      'README.md'
    ];
    
    console.log('📋 Verifying backwards-build structure...');
    let verified = 0;
    
    for (const file of expectedFiles) {
      try {
        await fs.access(path.join(testProjectPath, file));
        console.log(`✅ ${file}`);
        verified++;
      } catch (error) {
        console.log(`❌ ${file} - Missing`);
      }
    }
    
    console.log('');
    console.log(`🎯 Structure Verification: ${verified}/${expectedFiles.length} files found`);
    
    // Test Node.js readiness
    console.log('🧪 Testing Node.js readiness...');
    
    const rootPackage = JSON.parse(await fs.readFile(path.join(testProjectPath, 'package.json'), 'utf8'));
    const hasNodeScripts = rootPackage.scripts && rootPackage.scripts.start;
    const hasWorkspaces = rootPackage.workspaces && rootPackage.workspaces.length > 0;
    
    console.log(`✅ Node.js scripts: ${hasNodeScripts ? 'Yes' : 'No'}`);
    console.log(`✅ Workspaces: ${hasWorkspaces ? 'Yes' : 'No'}`);
    
    // Test slides setup
    const slidesPackage = JSON.parse(await fs.readFile(path.join(testProjectPath, 'slides/package.json'), 'utf8'));
    const hasRevealJS = slidesPackage.dependencies && slidesPackage.dependencies['reveal.js'];
    
    console.log(`✅ RevealJS setup: ${hasRevealJS ? 'Yes' : 'No'}`);
    
    console.log('');
    console.log('🎉 5-Minute Project Creation Test Results:');
    console.log(`⏱️  Duration: ${duration.toFixed(2)} seconds`);
    console.log(`📁 Files created: ${result.files ? result.files.length : 'Unknown'}`);
    console.log(`🏗️  Structure: ${verified}/${expectedFiles.length} components`);
    console.log(`🚀 Node.js ready: ${hasNodeScripts ? 'Yes' : 'No'}`);
    console.log(`📊 Slides ready: ${hasRevealJS ? 'Yes' : 'No'}`);
    
    console.log('');
    console.log('🚀 Quick Start Commands:');
    console.log(`cd ${testProjectPath}`);
    console.log('npm run install:all');
    console.log('npm start');
    console.log('npm run slides:start');
    
    return true;
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    return false;
  }
}

// Run if called directly
if (require.main === module) {
  test5MinuteProjectCreation()
    .then(success => {
      if (success) {
        console.log('✅ 5-Minute Project Creation test passed!');
        process.exit(0);
      } else {
        console.log('❌ 5-Minute Project Creation test failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Test error:', error);
      process.exit(1);
    });
}

module.exports = { test5MinuteProjectCreation };