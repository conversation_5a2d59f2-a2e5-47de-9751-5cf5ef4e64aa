/**
 * Test the actual onboarding flow with real project creation
 * This simulates the conversation flow that should trigger project creation
 */

// Register ts-node for TypeScript execution
require('ts-node').register({
  project: './tsconfig.json',
  transpileOnly: true,
  compilerOptions: {
    skipLibCheck: true,
    experimentalDecorators: true,
    emitDecoratorMetadata: true
  }
});

const { ProjectOnboardingTaskStrategy } = require('./src/services/conversation/strategies/project-onboarding-task-strategy.ts');

async function testOnboardingFlow() {
  console.log('🧪 [TEST] Testing real onboarding flow with project creation...');
  
  try {
    // Create strategy instance (this should use real services now)
    console.log('🔧 [TEST] Creating ProjectOnboardingTaskStrategy...');
    const strategy = new ProjectOnboardingTaskStrategy();
    console.log('✅ [TEST] Strategy created successfully with real services');
    
    // Mock conversation messages that simulate actual onboarding flow
    const mockMessages = [
      {
        id: 1,
        conversation_id: 888,
        role: 'assistant',
        content: 'What kind of project would you like to build today?',
        model: 'claude-3.5-haiku',
        prompt_tokens: 50,
        completion_tokens: 20,
        cost: 0.001,
        duration_ms: 1000,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      },
      {
        id: 2,
        conversation_id: 888,
        role: 'user',
        content: 'I want to build a productivity application with AI-powered task insights using React and Node.js',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      },
      {
        id: 3,
        conversation_id: 888,
        role: 'assistant',
        content: 'That sounds like a great project! Do you want me to include features like user authentication, a dashboard, and database integration?',
        model: 'claude-3.5-haiku',
        prompt_tokens: 60,
        completion_tokens: 25,
        cost: 0.002,
        duration_ms: 1200,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      }
    ];

    // Create mock context that matches the onboarding flow
    const mockContext = {
      conversationId: 888,
      messages: mockMessages.slice(0, -1), // All but the last message
      options: {
        strategy: 'project_onboarding'
      }
    };

    // Create the user response that should trigger project creation
    const triggerResponse = {
      message: {
        id: 4,
        conversation_id: 888,
        role: 'user',
        content: 'Yes, that sounds perfect! Let\'s build this project with all those features.',
        model: '',
        prompt_tokens: 0,
        completion_tokens: 0,
        cost: 0,
        duration_ms: 0,
        user_feedback: '',
        error_message: '',
        metadata: {},
        createdAt: new Date(),
        updatedAt: new Date(),
        line_start: 0,
        line_end: 0,
        project_id: null,
        created_at: new Date(),
        meta_data: {},
        code_language: null,
        file_path: null
      },
      status: 'success',
      conversationId: 888,
      messageId: '4'
    };

    console.log('💬 [TEST] Simulating onboarding conversation flow...');
    console.log(`💬 [TEST] User message: "${triggerResponse.message.content}"`);
    console.log('🔍 [TEST] This should trigger project creation...');

    // Process the response through the strategy (this should detect trigger and start project creation)
    const result = strategy.processResponse(triggerResponse, mockContext);
    
    console.log('✅ [TEST] Response processed by strategy');
    console.log(`📋 [TEST] Result status: ${result ? 'Success' : 'Failed'}`);

    console.log('⏳ [TEST] Waiting for async project creation to complete...');
    
    // Wait longer for the real project creation to complete
    await new Promise(resolve => setTimeout(resolve, 8000));

    console.log('🎉 [TEST] Onboarding flow test completed!');
    console.log('📝 [TEST] Check logs above to see if project creation was triggered.');
    console.log('📁 [TEST] Check /Users/<USER>/Code/experimental/ for new project folder.');
    
  } catch (error) {
    console.error('❌ [TEST] Onboarding flow test failed:', error.message);
    console.error('📊 [TEST] Stack trace:', error.stack);
  }
}

// Run the test
testOnboardingFlow().then(() => {
  console.log('🏁 [TEST] Onboarding flow test execution finished');
  process.exit(0);
}).catch(error => {
  console.error('💥 [TEST] Unhandled error in onboarding flow test:', error);
  process.exit(1);
});