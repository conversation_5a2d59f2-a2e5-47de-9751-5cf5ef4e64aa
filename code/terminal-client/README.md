# KAPI Terminal Client

AI-powered development assistant for the command line. This is a Node.js implementation of KAPI that provides terminal access to all KAPI capabilities while adding powerful local intelligence features.

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Make CLI executable
chmod +x bin/kapi

# Start interactive AI session (recommended)
./bin/kapi

# Or view help for legacy commands
./bin/kapi --help
```

## 🎨 Usage Modes

### 🌟 Interactive Mode (Recommended)
```bash
$ ./bin/kapi
# Shows ASCII art welcome and starts natural language session
kapi> analyze this project
kapi> find all TODO comments  
kapi> help
kapi> exit
```

### ⚡ Legacy Command Mode
```bash
$ ./bin/kapi search "pattern"    # Direct command execution
$ ./bin/kapi ask "question"      # For scripting/automation
```

## 📋 Implementation Status

This project follows the step-by-step specification in `/docs/02-products/01-core-features/09-terminal-client/major-features.md`:

- ✅ **Feature #0**: Project Setup & Architecture Foundation
- ✅ **Feature #1**: Local Code Intelligence & Search Engine
- ✅ **Feature #2**: Backend Agent & Conversation Bridge + **Unified Clerk Authentication**
- ⏳ **Feature #3**: Project Context & Memory System (next)
- ⏳ **Feature #4-11**: Advanced AI features (pending)

### 🔐 Authentication System

The terminal client now uses **unified Clerk authentication** consistent across all KAPI platforms:

- **✅ Development Mode**: Instant authentication bypass with mock admin user
- **✅ Production Mode**: Browser-based OAuth flow with secure local token storage
- **✅ Token Persistence**: Encrypted storage in `.kapi/auth/` directory
- **✅ Backend Integration**: JWT validation with existing KAPI backend
- **✅ Role-Based Access**: ADMIN and USER role support

See [AUTHENTICATION.md](AUTHENTICATION.md) for setup instructions.

## 🏗️ Architecture

**Node.js Version**: v22 LTS  
**Pattern**: Hybrid terminal client with local intelligence + backend integration

### Key Design Principles from Python Version Analysis:
- **.kapi folder**: Local project cache for context, permissions, chat history
- **Permission system**: User-controlled file access with trusted paths
- **Session memory**: Persistent conversation context
- **Backend integration**: Leverage existing KAPI agent infrastructure

## 📁 Project Structure

```
terminal-client/
├── package.json         # Project dependencies and scripts
├── bin/kapi            # Executable CLI entry point
├── src/
│   ├── cli.js          # Main CLI application
│   ├── commands/       # Command implementations
│   ├── services/       # Backend integration services
│   ├── utils/          # Utility functions
│   └── config/         # Configuration management
└── .kapi/              # Local project cache (created at runtime)
```

## 🛠️ Core Dependencies

- **commander**: Command-line framework
- **chalk**: Terminal styling and colors
- **inquirer**: Interactive prompts
- **ora**: Loading spinners
- **node-fetch**: HTTP client for backend APIs
- **ws**: WebSocket client for streaming
- **fs-extra**: Enhanced file operations
- **fast-glob**: Fast file pattern matching
- **configstore**: Configuration storage

## 🔧 Development

```bash
# Install dependencies
npm install

# Run with auto-reload
npm run dev

# Test the CLI
./bin/kapi status
./bin/kapi search "pattern"
./bin/kapi --help
```

## 📚 Available Commands

### Local Intelligence (Feature #1)
- `kapi search <pattern>` - Search through codebase
- `kapi grep <pattern>` - Advanced grep with semantic understanding  
- `kapi index` - Build/rebuild local file index

### AI-Powered Commands (Feature #2)
- `kapi login` - Authenticate with KAPI backend
- `kapi logout` - Logout from KAPI backend
- `kapi chat` - Start an interactive AI chat session
- `kapi ask <question>` - Ask AI a quick question
- `kapi explain <input>` - Explain command or code with AI
- `kapi translate <description>` - Translate natural language to terminal commands
- `kapi analyze-project` - Analyze current project with AI

### Project Management
- `kapi init` - Initialize KAPI in current project
- `kapi status` - Show client status and configuration

## 🎯 Implementation Plan

This terminal client implements the 11 features specified in the major-features.md document:

1. **Local Code Intelligence & Search Engine** (Current)
2. Backend Agent & Conversation Bridge
3. Project Context & Memory System
4. Brutal Honesty Analysis Terminal
5. AI Agent Orchestration
6. Interactive CLI Conversations
7. Intelligent Project Discovery
8. Smart Code Fixes & Suggestions
9. Real-time Project Health Monitoring
10. Documentation Intelligence
11. Security & Vulnerability Analysis

Each feature builds upon the previous ones, creating a comprehensive terminal-based alternative to KAPI's IDE integration.

## 🔗 Integration

This terminal client is designed to integrate seamlessly with the existing KAPI backend at `/Users/<USER>/Code/KAPI/code/backend`, leveraging:

- Unified Conversation Service APIs
- Agent Architecture & Orchestration
- Memory System & Context Management
- Streaming Response Handling

## 📝 Development Notes

- Uses ES6 modules (`"type": "module"` in package.json)
- Node.js v22 LTS for modern JavaScript features
- Follows existing KAPI architectural patterns
- Maintains compatibility with backend services
- Implements lessons learned from abandoned Python implementation