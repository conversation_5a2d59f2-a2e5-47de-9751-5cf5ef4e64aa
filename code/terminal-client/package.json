{"name": "kapi-terminal-client", "version": "1.0.0", "description": "KAPI Terminal Client - AI-powered development assistant for the command line", "main": "src/cli.js", "type": "module", "bin": {"kapi": "./bin/kapi"}, "scripts": {"start": "node src/cli.js", "dev": "node --watch src/cli.js", "test": "echo \"Tests coming soon...\"", "lint": "echo \"Linting coming soon...\""}, "keywords": ["cli", "terminal", "kapi", "ai", "development", "assistant", "code-analysis"], "author": "KAPI Team", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"chalk": "^5.3.0", "commander": "^12.0.0", "configstore": "^7.0.0", "eventsource": "^4.0.0", "fast-glob": "^3.3.2", "fs-extra": "^11.2.0", "inquirer": "^10.2.2", "node-fetch": "^3.3.2", "ora": "^8.1.0", "ws": "^8.18.0"}, "devDependencies": {"nodemon": "^3.1.7"}}