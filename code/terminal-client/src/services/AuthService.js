/**
 * KAPI Terminal Client - Authentication Service
 * 
 * Unified authentication service that delegates to ClerkAuthService for 
 * production authentication while maintaining backwards compatibility.
 * 
 * This service provides a consistent interface for all authentication operations.
 */

import { clerkAuthService } from './ClerkAuthService.js';
import EventEmitter from './utils/EventEmitter.js';

class AuthService extends EventEmitter {
  constructor() {
    super();
    this.clerkAuth = clerkAuthService;
    
    // Forward all events from ClerkAuthService
    this.clerkAuth.on('authenticated', (user) => this.emit('authenticated', user));
    this.clerkAuth.on('unauthenticated', () => this.emit('unauthenticated'));
    this.clerkAuth.on('authentication_error', (error) => this.emit('authentication_error', error));
    this.clerkAuth.on('initialized', () => this.emit('initialized'));
  }

  /**
   * Initialize authentication service
   */
  async initialize() {
    return await this.clerkAuth.initialize();
  }

  /**
   * Authenticates user using Clerk device flow (browser-based)
   * For backwards compatibility, we ignore credentials parameter
   */
  async login(credentials = null) {
    return await this.clerkAuth.login();
  }
  
  /**
   * Logs out the current user
   */
  async logout() {
    return await this.clerkAuth.logout();
  }
  
  /**
   * Fetches the current user data using the stored token
   */
  async getCurrentUser() {
    return this.clerkAuth.getUser();
  }
  
  /**
   * Utility method to check if the user is currently authenticated
   */
  isAuthenticated() {
    return this.clerkAuth.isAuthenticated();
  }
  
  /**
   * Get the current user (cached)
   */
  getUser() {
    return this.clerkAuth.getUser();
  }
  
  /**
   * Validate current authentication status
   */
  async validateAuth() {
    return await this.clerkAuth.validateAuth();
  }

  /**
   * Get authentication status for display
   */
  getAuthStatus() {
    return this.clerkAuth.getAuthStatus();
  }

  /**
   * Clear authentication state (legacy method)
   */
  clearAuthState() {
    return this.clerkAuth.logout();
  }
}

// Export a singleton instance
export const authService = new AuthService();
export default authService;