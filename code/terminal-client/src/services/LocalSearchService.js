/**
 * KAPI Terminal Client - Local Search Service
 * 
 * Implements Feature #1: Local Code Intelligence & Search Engine
 * 
 * This service provides local file system searching, indexing, and 
 * pattern matching capabilities that power the terminal client's
 * code intelligence features.
 */

import fs from 'fs-extra';
import path from 'path';
import glob from 'fast-glob';
import chalk from 'chalk';

export class LocalSearchService {
  constructor(options = {}) {
    this.workingDir = options.workingDir || process.cwd();
    this.maxDepth = options.maxDepth || 5;
    this.excludePatterns = options.excludePatterns || [
      'node_modules/**',
      '.git/**',
      'dist/**',
      'build/**',
      '.next/**',
      'coverage/**',
      '.kapi/**'
    ];
  }

  /**
   * Search for files matching a pattern
   * @param {string} pattern - Search pattern
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of matching files
   */
  async searchFiles(pattern, options = {}) {
    const { type, maxDepth = this.maxDepth } = options;
    
    console.log(chalk.blue(`🔍 Searching for pattern: "${pattern}"`));
    console.log(chalk.gray(`📁 Working directory: ${this.workingDir}`));
    
    try {
      // Build glob patterns based on file type
      let globPatterns = ['**/*'];
      
      if (type) {
        const extensions = this.getExtensionsForType(type);
        globPatterns = extensions.map(ext => `**/*.${ext}`);
      }
      
      // Find all files
      const files = await glob(globPatterns, {
        cwd: this.workingDir,
        ignore: this.excludePatterns,
        dot: false,
        deep: maxDepth,
        onlyFiles: true
      });
      
      console.log(chalk.green(`📄 Found ${files.length} files to search`));
      
      // Filter files by pattern (filename matching for now)
      const matchingFiles = files.filter(file => {
        const filename = path.basename(file);
        const dirname = path.dirname(file);
        
        return filename.toLowerCase().includes(pattern.toLowerCase()) ||
               dirname.toLowerCase().includes(pattern.toLowerCase());
      });
      
      console.log(chalk.green(`✅ ${matchingFiles.length} files match pattern`));
      
      return matchingFiles.map(file => ({
        path: file,
        fullPath: path.join(this.workingDir, file),
        relativePath: file,
        type: path.extname(file).slice(1)
      }));
      
    } catch (error) {
      console.error(chalk.red('❌ Search error:'), error.message);
      return [];
    }
  }

  /**
   * Search content within files (basic grep functionality)
   * @param {string} pattern - Pattern to search for
   * @param {Object} options - Search options
   * @returns {Promise<Array>} - Array of matches
   */
  async searchContent(pattern, options = {}) {
    const { type, before = 0, after = 0 } = options;
    
    console.log(chalk.blue(`🔎 Searching content for: "${pattern}"`));
    
    try {
      // Get list of files to search - get all files, not just ones matching '*'
      const globPatterns = type ? this.getExtensionsForType(type).map(ext => `**/*.${ext}`) : ['**/*'];
      const allFiles = await glob(globPatterns, {
        cwd: this.workingDir,
        ignore: this.excludePatterns,
        dot: false,
        deep: this.maxDepth,
        onlyFiles: true
      });
      
      const files = allFiles.map(file => ({
        relativePath: file,
        fullPath: path.join(this.workingDir, file)
      }));
      const matches = [];
      
      for (const file of files) {
        try {
          const content = await fs.readFile(file.fullPath, 'utf8');
          const lines = content.split('\n');
          
          lines.forEach((line, lineNumber) => {
            if (line.toLowerCase().includes(pattern.toLowerCase())) {
              matches.push({
                file: file.relativePath,
                fullPath: file.fullPath,
                lineNumber: lineNumber + 1,
                line: line.trim(),
                before: before > 0 ? lines.slice(Math.max(0, lineNumber - before), lineNumber) : [],
                after: after > 0 ? lines.slice(lineNumber + 1, lineNumber + 1 + after) : []
              });
            }
          });
          
        } catch (fileError) {
          // Skip files that can't be read (binary files, permission issues, etc.)
          console.log(chalk.yellow(`⚠️  Skipped: ${file.relativePath}`));
        }
      }
      
      console.log(chalk.green(`✅ Found ${matches.length} content matches`));
      return matches;
      
    } catch (error) {
      console.error(chalk.red('❌ Content search error:'), error.message);
      return [];
    }
  }

  /**
   * Build a file index for faster searching
   * @param {Object} options - Index options
   * @returns {Promise<Object>} - File index
   */
  async buildIndex(options = {}) {
    const { rebuild = false } = options;
    
    console.log(chalk.blue('📚 Building file index...'));
    
    try {
      const files = await glob(['**/*'], {
        cwd: this.workingDir,
        ignore: this.excludePatterns,
        dot: false,
        deep: this.maxDepth
      });
      
      const index = {
        created: new Date().toISOString(),
        workingDir: this.workingDir,
        totalFiles: files.length,
        files: {}
      };
      
      for (const file of files) {
        const fullPath = path.join(this.workingDir, file);
        const stats = await fs.stat(fullPath);
        
        index.files[file] = {
          path: file,
          fullPath,
          size: stats.size,
          modified: stats.mtime,
          type: path.extname(file).slice(1),
          directory: path.dirname(file)
        };
      }
      
      console.log(chalk.green(`✅ Indexed ${files.length} files`));
      return index;
      
    } catch (error) {
      console.error(chalk.red('❌ Index error:'), error.message);
      return null;
    }
  }

  /**
   * Get file extensions for a given type
   * @param {string} type - File type (js, ts, py, etc.)
   * @returns {Array} - Array of extensions
   */
  getExtensionsForType(type) {
    const typeMap = {
      js: ['js', 'jsx'],
      ts: ['ts', 'tsx'],
      py: ['py', 'pyx', 'pyi'],
      java: ['java'],
      cpp: ['cpp', 'cxx', 'cc', 'c'],
      cs: ['cs'],
      go: ['go'],
      rust: ['rs'],
      php: ['php'],
      rb: ['rb'],
      swift: ['swift'],
      kt: ['kt'],
      scala: ['scala'],
      text: ['txt', 'md', 'rst'],
      config: ['json', 'yaml', 'yml', 'toml', 'ini', 'cfg']
    };
    
    return typeMap[type] || [type];
  }
}

export default LocalSearchService;