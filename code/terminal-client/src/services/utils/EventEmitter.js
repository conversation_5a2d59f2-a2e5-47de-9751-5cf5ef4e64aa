/**
 * KAPI Terminal Client - Event Emitter
 * 
 * A simple EventEmitter implementation for the terminal client.
 * Used by service classes to provide a consistent event system
 * for decoupled communication between components.
 */

export class EventEmitter {
  constructor() {
    this.events = {};
  }

  /**
   * Register an event listener
   * @param {string} event - Event name
   * @param {Function} listener - Function to call when event is emitted
   * @returns {EventEmitter} - Returns this for chaining
   */
  on(event, listener) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
    return this;
  }

  /**
   * Register a one-time event listener
   * @param {string} event - Event name
   * @param {Function} listener - Function to call when event is emitted
   * @returns {EventEmitter} - Returns this for chaining
   */
  once(event, listener) {
    const onceWrapper = (...args) => {
      listener(...args);
      this.removeListener(event, onceWrapper);
    };
    return this.on(event, onceWrapper);
  }

  /**
   * Emit an event with optional arguments
   * @param {string} event - Event name
   * @param {...any} args - Arguments to pass to listeners
   * @returns {boolean} - Returns true if event had listeners
   */
  emit(event, ...args) {
    if (!this.events[event]) {
      return false;
    }
    this.events[event].forEach(listener => {
      try {
        listener(...args);
      } catch (error) {
        console.error(`[EventEmitter] Error in listener for event '${event}':`, error);
      }
    });
    return true;
  }

  /**
   * Remove a specific listener for an event
   * @param {string} event - Event name
   * @param {Function} listener - Function to remove
   * @returns {EventEmitter} - Returns this for chaining
   */
  removeListener(event, listener) {
    if (!this.events[event]) {
      return this;
    }
    this.events[event] = this.events[event].filter(l => l !== listener);
    return this;
  }

  /**
   * Remove all listeners for a specific event
   * @param {string} event - Event name
   * @returns {EventEmitter} - Returns this for chaining
   */
  removeAllListeners(event) {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
    return this;
  }

  /**
   * Get the number of listeners for an event
   * @param {string} event - Event name
   * @returns {number} - Number of listeners
   */
  listenerCount(event) {
    return this.events[event] ? this.events[event].length : 0;
  }

  /**
   * Get all events that have listeners
   * @returns {string[]} - Array of event names
   */
  eventNames() {
    return Object.keys(this.events);
  }
}

export default EventEmitter;