/**
 * KAPI Terminal Client - Terminal AI Client
 * 
 * Specialized AI service for terminal interactions.
 * Handles terminal-specific AI features like command analysis,
 * natural language to command translation, and code suggestions.
 */

import { apiClient } from './ApiClient.js';
import { conversationService } from './ConversationService.js';
import EventEmitter from './utils/EventEmitter.js';

export class TerminalAIClient extends EventEmitter {
  constructor() {
    super();
    this.conversationService = conversationService;
    this.activeConversation = null;
  }

  /**
   * Initialize the terminal AI client with a conversation
   */
  async initialize() {
    try {
      console.log('[TerminalAIClient] Initializing...');
      
      // Create a dedicated conversation for terminal assistance
      this.activeConversation = await conversationService.createConversation({
        title: 'Terminal AI Assistant',
        strategy: 'terminal_help'
      });

      console.log(`[TerminalAIClient] Initialized with conversation ID: ${this.activeConversation.id}`);
      this.emit('initialized', this.activeConversation);
      return this.activeConversation;
    } catch (error) {
      console.error('[TerminalAIClient] Initialization failed:', error);
      this.emit('error', { type: 'initialization_failed', error });
      throw error;
    }
  }

  /**
   * Analyze terminal command output with AI
   */
  async analyzeOutput(output, context = {}) {
    try {
      if (!this.activeConversation) {
        await this.initialize();
      }

      const prompt = `Analyze this terminal output and provide insights:

Output:
\`\`\`
${output}
\`\`\`

Context:
- Current directory: ${context.currentDirectory || 'Unknown'}
- Recent commands: ${context.recentCommands ? context.recentCommands.join(', ') : 'None'}

Please provide:
1. Analysis of what happened
2. Any errors or issues detected
3. Suggested next steps or fixes
4. Relevant commands to try`;

      console.log('[TerminalAIClient] Analyzing output...');
      const response = await conversationService.sendSimpleMessage(
        this.activeConversation.id,
        prompt,
        {
          strategy: 'debug_analysis',
          temperature: 0.3,
          maxTokens: 1000
        }
      );

      const analysis = {
        explanation: response.message?.content || response.content || 'No analysis available',
        suggestions: this.extractSuggestions(response.message?.content || response.content || ''),
        commands: this.extractCommands(response.message?.content || response.content || '')
      };

      this.emit('analysis_complete', { output, context, analysis });
      return analysis;
    } catch (error) {
      console.error('[TerminalAIClient] Error analyzing output:', error);
      this.emit('error', { type: 'analyze_output_failed', error });
      return {
        explanation: 'Failed to analyze output',
        suggestions: ['Check your internet connection', 'Try the command again'],
        commands: []
      };
    }
  }

  /**
   * Get AI assistance for a natural language command
   */
  async getAssistance(query, context = {}) {
    try {
      if (!this.activeConversation) {
        await this.initialize();
      }

      const prompt = `Help with this terminal request: "${query}"

Context:
- Current directory: ${context.currentDirectory || process.cwd()}
- Recent commands: ${context.recentCommands ? context.recentCommands.join(', ') : 'None'}
- Operating system: ${process.platform}

Please provide:
1. Clear explanation of how to accomplish this
2. Specific terminal commands to run
3. Any important warnings or considerations
4. Alternative approaches if applicable`;

      console.log(`[TerminalAIClient] Getting assistance for: "${query}"`);
      const response = await conversationService.sendSimpleMessage(
        this.activeConversation.id,
        prompt,
        {
          strategy: 'terminal_help',
          temperature: 0.2,
          maxTokens: 1500
        }
      );

      const assistance = {
        explanation: response.message?.content || response.content || 'No assistance available',
        commands: this.extractCommands(response.message?.content || response.content || ''),
        suggestions: this.extractSuggestions(response.message?.content || response.content || ''),
        codeSnippets: this.extractCodeSnippets(response.message?.content || response.content || '')
      };

      this.emit('assistance_provided', { query, context, assistance });
      return assistance;
    } catch (error) {
      console.error('[TerminalAIClient] Error getting assistance:', error);
      this.emit('error', { type: 'assistance_failed', error });
      return {
        explanation: 'Failed to get AI assistance',
        commands: [],
        suggestions: ['Check your connection and try again']
      };
    }
  }

  /**
   * Translate natural language to terminal commands
   */
  async translateToCommand(naturalLanguage, context = {}) {
    try {
      if (!this.activeConversation) {
        await this.initialize();
      }

      const prompt = `Convert this request to terminal commands: "${naturalLanguage}"

Context:
- Current directory: ${context.currentDirectory || process.cwd()}
- Operating system: ${process.platform}
- Shell: ${context.shell || process.env.SHELL || 'bash'}

Provide only the exact commands that should be run, one per line.
Include comments (starting with #) for explanation when needed.
Make sure commands are safe and appropriate for ${process.platform}.`;

      console.log(`[TerminalAIClient] Translating: "${naturalLanguage}"`);
      const response = await conversationService.sendSimpleMessage(
        this.activeConversation.id,
        prompt,
        {
          strategy: 'code_generation',
          temperature: 0.1,
          maxTokens: 500
        }
      );

      const content = response.message?.content || response.content || '';
      const commands = this.extractCommands(content);

      this.emit('translation_complete', { naturalLanguage, commands });
      return commands;
    } catch (error) {
      console.error('[TerminalAIClient] Error translating to command:', error);
      this.emit('error', { type: 'translation_failed', error });
      return [`# Failed to translate: ${naturalLanguage}`];
    }
  }

  /**
   * Get contextual code suggestions based on terminal state
   */
  async getCodeSuggestions(context = {}) {
    try {
      if (!this.activeConversation) {
        await this.initialize();
      }

      const prompt = `Based on the current terminal context, suggest useful code snippets or commands:

Context:
- Current directory: ${context.currentDirectory || process.cwd()}
- Recent commands: ${context.recentCommands ? context.recentCommands.join(', ') : 'None'}
- Selected text: ${context.selectedText || 'None'}
- Project type: ${context.projectType || 'Unknown'}

Provide practical code snippets or commands that would be helpful in this context.
Include brief explanations for each suggestion.`;

      console.log('[TerminalAIClient] Getting code suggestions...');
      const response = await conversationService.sendSimpleMessage(
        this.activeConversation.id,
        prompt,
        {
          strategy: 'code_generation',
          temperature: 0.4,
          maxTokens: 1200
        }
      );

      const suggestions = {
        explanation: 'Here are some contextual suggestions:',
        codeSnippets: this.extractCodeSnippets(response.message?.content || response.content || ''),
        commands: this.extractCommands(response.message?.content || response.content || ''),
        suggestions: this.extractSuggestions(response.message?.content || response.content || '')
      };

      this.emit('suggestions_provided', { context, suggestions });
      return suggestions;
    } catch (error) {
      console.error('[TerminalAIClient] Error getting code suggestions:', error);
      this.emit('error', { type: 'suggestions_failed', error });
      return {
        explanation: 'Failed to get code suggestions',
        codeSnippets: [],
        commands: []
      };
    }
  }

  /**
   * Generate documentation based on code or commands
   */
  async generateDocumentation(input, context = {}) {
    try {
      if (!this.activeConversation) {
        await this.initialize();
      }

      const prompt = `Generate clear documentation for this code or command:

Input:
\`\`\`
${input}
\`\`\`

Context:
- File type: ${context.fileType || 'Unknown'}
- Project context: ${context.projectContext || 'General'}

Please provide:
1. Clear description of what this does
2. Parameters or options explained
3. Usage examples
4. Common pitfalls or considerations`;

      console.log('[TerminalAIClient] Generating documentation...');
      const response = await conversationService.sendSimpleMessage(
        this.activeConversation.id,
        prompt,
        {
          strategy: 'documentation',
          temperature: 0.3,
          maxTokens: 2000
        }
      );

      const documentation = response.message?.content || response.content || `# Documentation for ${input}\n\nDocumentation generation failed.`;

      this.emit('documentation_generated', { input, documentation });
      return documentation;
    } catch (error) {
      console.error('[TerminalAIClient] Error generating documentation:', error);
      this.emit('error', { type: 'documentation_failed', error });
      return `# Documentation for ${input}\n\nFailed to generate documentation.`;
    }
  }

  /**
   * Perform project analysis
   */
  async analyzeProject(projectPath = process.cwd()) {
    try {
      if (!this.activeConversation) {
        await this.initialize();
      }

      // Get basic project info
      const projectInfo = await this.getProjectInfo(projectPath);
      
      const prompt = `Analyze this project and provide insights:

Project Information:
${JSON.stringify(projectInfo, null, 2)}

Please provide:
1. Project type and technology stack analysis
2. Potential issues or improvements
3. Recommended development workflows
4. Useful commands for this project type`;

      console.log('[TerminalAIClient] Analyzing project...');
      const response = await conversationService.sendSimpleMessage(
        this.activeConversation.id,
        prompt,
        {
          strategy: 'project_analysis',
          temperature: 0.4,
          maxTokens: 2000
        }
      );

      const analysis = {
        explanation: response.message?.content || response.content || 'No analysis available',
        projectInfo,
        suggestions: this.extractSuggestions(response.message?.content || response.content || ''),
        commands: this.extractCommands(response.message?.content || response.content || '')
      };

      this.emit('project_analyzed', { projectPath, analysis });
      return analysis;
    } catch (error) {
      console.error('[TerminalAIClient] Error analyzing project:', error);
      this.emit('error', { type: 'project_analysis_failed', error });
      return {
        explanation: 'Failed to analyze project',
        suggestions: [],
        commands: []
      };
    }
  }

  /**
   * Get basic project information
   */
  async getProjectInfo(projectPath) {
    try {
      const fs = await import('fs-extra');
      const path = await import('path');

      const info = {
        path: projectPath,
        files: []
      };

      // Check for common project files
      const commonFiles = [
        'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod',
        'pom.xml', 'build.gradle', 'composer.json', 'Pipfile',
        'README.md', '.gitignore', 'docker-compose.yml', 'Dockerfile'
      ];

      for (const file of commonFiles) {
        const filePath = path.join(projectPath, file);
        if (await fs.pathExists(filePath)) {
          info.files.push(file);
        }
      }

      return info;
    } catch (error) {
      console.error('[TerminalAIClient] Error getting project info:', error);
      return { path: projectPath, files: [], error: error.message };
    }
  }

  /**
   * Check if the backend AI services are available
   */
  async checkConnection() {
    try {
      await apiClient.get('/health');
      return true;
    } catch (error) {
      console.error('[TerminalAIClient] Connection check failed:', error);
      return false;
    }
  }

  /**
   * Extract command suggestions from AI response
   */
  extractCommands(content) {
    const commands = [];
    const codeBlockRegex = /```(?:bash|sh|shell|terminal)?\n([\s\S]*?)```/g;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      const blockContent = match[1].trim();
      blockContent.split('\n').forEach(line => {
        line = line.trim();
        if (line && !line.startsWith('#')) {
          commands.push(line);
        }
      });
    }

    // Also look for inline commands (starting with $)
    const inlineCommandRegex = /\$\s+(.+)/g;
    while ((match = inlineCommandRegex.exec(content)) !== null) {
      commands.push(match[1].trim());
    }

    return commands;
  }

  /**
   * Extract suggestions from AI response
   */
  extractSuggestions(content) {
    const suggestions = [];
    const lines = content.split('\n');

    lines.forEach(line => {
      line = line.trim();
      if (line.match(/^[\d\-\*]\s+/) || line.startsWith('• ')) {
        suggestions.push(line.replace(/^[\d\-\*•]\s+/, ''));
      }
    });

    return suggestions;
  }

  /**
   * Extract code snippets from AI response
   */
  extractCodeSnippets(content) {
    const snippets = [];
    const codeBlockRegex = /```(?:javascript|js|python|py|java|c|cpp|go|rust|php|rb|swift)?\n([\s\S]*?)```/g;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      snippets.push(match[1].trim());
    }

    return snippets;
  }

  /**
   * Get the active conversation ID
   */
  getConversationId() {
    return this.activeConversation?.id;
  }

  /**
   * Reset the conversation
   */
  async resetConversation() {
    try {
      if (this.activeConversation) {
        await conversationService.archiveConversation(this.activeConversation.id);
      }
      await this.initialize();
      this.emit('conversation_reset');
    } catch (error) {
      console.error('[TerminalAIClient] Error resetting conversation:', error);
      this.emit('error', { type: 'reset_failed', error });
    }
  }
}

// Export singleton instance
export const terminalAIClient = new TerminalAIClient();
export default terminalAIClient;