/**
 * KAPI Terminal Client - Backend Authentication Service
 * 
 * Handles authentication via KAPI backend device flow endpoints.
 * The backend manages Clerk integration - this client only talks to backend.
 */

import fs from 'fs-extra';
import path from 'path';
import crypto from 'crypto';
import { exec } from 'child_process';
import { apiClient } from './ApiClient.js';
import { VERSIONED_API_URL, isDev } from '../config/config.js';
import EventEmitter from './utils/EventEmitter.js';
import chalk from 'chalk';
import ora from 'ora';

// Development bypass configuration
const DEV_CONFIG = {
  bypassToken: 'kapi_dev_unified_2024',
  mockUser: {
    id: 'dev_user_123',
    email: '<EMAIL>',
    username: 'admin',
    first_name: 'Admin',
    last_name: 'User',
    role: 'ADMIN',
    clerk_id: 'dev_clerk_123'
  }
};

class ClerkAuthService extends EventEmitter {
  constructor() {
    super();
    this.user = null;
    this.isInitialized = false;
    this.authDir = path.join(process.cwd(), '.kapi', 'auth');
    this.tokenFile = path.join(this.authDir, 'token.json');
  }

  /**
   * Initialize authentication service
   */
  async initialize() {
    console.log('[ClerkAuth] Initializing Clerk authentication...');
    
    // Ensure auth directory exists
    await fs.ensureDir(this.authDir);
    
    if (isDev && process.env.USE_DEVELOPMENT_AUTH === 'true') {
      console.log(chalk.yellow('[ClerkAuth] Using development bypass'));
      apiClient.setToken(DEV_CONFIG.bypassToken);
      this.user = DEV_CONFIG.mockUser;
      await this.saveTokenData({
        token: DEV_CONFIG.bypassToken,
        user: DEV_CONFIG.mockUser,
        expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000) // 7 days
      });
      this.emit('authenticated', this.user);
    } else {
      // Check for existing valid token
      const tokenData = await this.loadTokenData();
      if (tokenData && tokenData.token && tokenData.expiresAt > Date.now()) {
        try {
          apiClient.setToken(tokenData.token);
          this.user = await this.validateTokenWithBackend(tokenData.token);
          this.emit('authenticated', this.user);
          console.log(chalk.green('[ClerkAuth] Existing session restored'));
        } catch (error) {
          console.log(chalk.yellow('[ClerkAuth] Invalid stored token, clearing...'));
          await this.clearTokenData();
          this.emit('unauthenticated');
        }
      } else {
        console.log('[ClerkAuth] No valid token found');
        this.emit('unauthenticated');
      }
    }
    
    this.isInitialized = true;
    this.emit('initialized');
  }

  /**
   * Start device-based authentication flow
   */
  async login() {
    console.log(chalk.blue('[ClerkAuth] Starting authentication...'));
    
    // Development bypass
    if (isDev && process.env.USE_DEVELOPMENT_AUTH === 'true') {
      console.log(chalk.yellow('[ClerkAuth] Using development bypass'));
      apiClient.setToken(DEV_CONFIG.bypassToken);
      this.user = DEV_CONFIG.mockUser;
      await this.saveTokenData({
        token: DEV_CONFIG.bypassToken,
        user: DEV_CONFIG.mockUser,
        expiresAt: Date.now() + (7 * 24 * 60 * 60 * 1000)
      });
      this.emit('authenticated', this.user);
      return { success: true, user: this.user };
    }

    try {
      // Step 1: Request device authorization
      const deviceAuth = await this.requestDeviceAuthorization();
      
      // Step 2: Open browser for user authentication
      console.log(chalk.cyan(`\n🔐 Please authenticate in your browser:`));
      console.log(chalk.white(`   ${deviceAuth.verification_uri}`));
      console.log(chalk.gray(`   Device code: ${deviceAuth.user_code}`));
      
      await this.openBrowser(deviceAuth.verification_uri);
      
      // Step 3: Poll for completion
      const spinner = ora('Waiting for authentication...').start();
      const tokenData = await this.pollForToken(deviceAuth, spinner);
      spinner.succeed('Authentication successful!');
      
      // Step 4: Validate and store token
      this.user = await this.validateTokenWithBackend(tokenData.token);
      apiClient.setToken(tokenData.token);
      
      await this.saveTokenData(tokenData);
      this.emit('authenticated', this.user);
      
      console.log(chalk.green(`✅ Welcome, ${this.user.first_name || this.user.username}!`));
      return { success: true, user: this.user };
      
    } catch (error) {
      console.error(chalk.red('❌ Authentication failed:'), error.message);
      this.emit('authentication_error', error);
      throw error;
    }
  }

  /**
   * Request device authorization from backend
   */
  async requestDeviceAuthorization() {
    try {
      const response = await fetch(`${VERSIONED_API_URL}/auth/device/authorize`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_type: 'terminal',
          device_name: `KAPI Terminal (${process.platform})`
        })
      });

      if (!response.ok) {
        throw new Error(`Device authorization failed: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Device authorization failed');
      }

      const data = result.data;
      return {
        device_code: data.device_code,
        user_code: data.user_code,
        verification_uri: data.verification_uri,
        verification_uri_complete: data.verification_uri_complete,
        expires_in: data.expires_in,
        interval: data.interval || 5
      };
    } catch (error) {
      throw new Error(`Failed to request device authorization: ${error.message}`);
    }
  }

  /**
   * Poll for token completion
   */
  async pollForToken(deviceAuth, spinner) {
    const maxAttempts = Math.floor(deviceAuth.expires_in / deviceAuth.interval);
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        if (attempts >= maxAttempts) {
          reject(new Error('Authentication timeout - device code expired'));
          return;
        }

        try {
          const response = await fetch(`${VERSIONED_API_URL}/auth/device/token`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              device_code: deviceAuth.device_code
            })
          });

          const result = await response.json();

          if (response.ok && result.success && result.access_token) {
            // Success - we have a token
            resolve({
              token: result.access_token,
              expiresAt: Date.now() + (result.expires_in * 1000),
              refreshToken: result.refresh_token
            });
          } else if (result.error === 'authorization_pending') {
            // Still waiting for user to complete auth
            spinner.text = `Waiting for authentication... (${attempts + 1}/${maxAttempts})`;
            attempts++;
            setTimeout(poll, deviceAuth.interval * 1000);
          } else if (result.error === 'slow_down') {
            // Slow down polling
            attempts++;
            setTimeout(poll, (deviceAuth.interval + 5) * 1000);
          } else {
            // Error occurred
            reject(new Error(result.error_description || result.error || result.message || 'Authentication failed'));
          }
        } catch (error) {
          reject(new Error(`Token polling failed: ${error.message}`));
        }
      };

      // Start polling
      poll();
    });
  }

  /**
   * Validate token with backend and get user data
   */
  async validateTokenWithBackend(token) {
    try {
      // Set the token for the API client to use
      apiClient.setToken(token);
      
      const response = await apiClient.get('/auth/me');

      if (response.success && response.data) {
        return response.data;
      } else {
        throw new Error('Invalid response format from backend');
      }
    } catch (error) {
      throw new Error(`Backend token validation failed: ${error.message}`);
    }
  }

  /**
   * Open browser for authentication
   */
  async openBrowser(url) {
    return new Promise((resolve) => {
      let command;
      
      switch (process.platform) {
        case 'darwin':
          command = `open "${url}"`;
          break;
        case 'win32':
          command = `start "${url}"`;
          break;
        default:
          command = `xdg-open "${url}"`;
      }

      exec(command, (error) => {
        if (error) {
          console.log(chalk.yellow('Could not automatically open browser. Please visit the URL above.'));
        }
        resolve();
      });
    });
  }

  /**
   * Save token data securely to local file
   */
  async saveTokenData(tokenData) {
    try {
      const encryptedData = this.encryptTokenData(tokenData);
      await fs.writeFile(this.tokenFile, JSON.stringify(encryptedData, null, 2));
      
      // Set restrictive permissions (owner only)
      await fs.chmod(this.tokenFile, 0o600);
    } catch (error) {
      console.error('[ClerkAuth] Failed to save token data:', error);
    }
  }

  /**
   * Load token data from local file
   */
  async loadTokenData() {
    try {
      if (!(await fs.pathExists(this.tokenFile))) {
        return null;
      }

      const encryptedData = JSON.parse(await fs.readFile(this.tokenFile, 'utf8'));
      return this.decryptTokenData(encryptedData);
    } catch (error) {
      console.error('[ClerkAuth] Failed to load token data:', error);
      return null;
    }
  }

  /**
   * Clear stored token data
   */
  async clearTokenData() {
    try {
      if (await fs.pathExists(this.tokenFile)) {
        await fs.remove(this.tokenFile);
      }
    } catch (error) {
      console.error('[ClerkAuth] Failed to clear token data:', error);
    }
  }

  /**
   * Encrypt token data for secure storage
   */
  encryptTokenData(data) {
    // Simple base64 encoding with system key obfuscation for development
    // In production, this would use proper encryption
    const key = this.getSystemKey();
    const plaintext = JSON.stringify(data);
    const combined = key + '::' + plaintext;
    const encoded = Buffer.from(combined).toString('base64');
    
    return { 
      encoded, 
      timestamp: Date.now() 
    };
  }

  /**
   * Decrypt token data
   */
  decryptTokenData(encryptedData) {
    try {
      const key = this.getSystemKey();
      
      // Handle both old and new formats
      let encoded;
      if (encryptedData.encoded) {
        encoded = encryptedData.encoded;
      } else if (encryptedData.encrypted) {
        // Legacy format - return error to trigger re-auth
        throw new Error('Legacy format');
      } else {
        throw new Error('Invalid format');
      }
      
      const combined = Buffer.from(encoded, 'base64').toString('utf8');
      const [storedKey, ...dataParts] = combined.split('::');
      
      if (storedKey !== key) {
        throw new Error('Key mismatch');
      }
      
      const plaintext = dataParts.join('::');
      return JSON.parse(plaintext);
    } catch (error) {
      // If decryption fails, it might be corrupted - clear and re-authenticate
      console.log(chalk.yellow('[ClerkAuth] Token data invalid, clearing...'));
      this.clearTokenData().catch(() => {}); // Ignore errors during cleanup
      throw new Error('Failed to decrypt token data - please re-authenticate');
    }
  }

  /**
   * Generate system-specific encryption key
   */
  getSystemKey() {
    // Use system-specific information for encryption key
    const systemInfo = `${process.platform}-${process.arch}-${process.env.HOME || process.env.USERPROFILE}`;
    return crypto.createHash('sha256').update(systemInfo).digest('hex');
  }

  /**
   * Logout and clear authentication data
   */
  async logout() {
    try {
      console.log('[ClerkAuth] Logging out...');
      
      // Clear stored token
      await this.clearTokenData();
      
      // Clear in-memory data
      apiClient.removeToken();
      this.user = null;
      
      this.emit('unauthenticated');
      console.log(chalk.green('✅ Logged out successfully'));
    } catch (error) {
      console.error(chalk.red('❌ Logout error:'), error.message);
      throw error;
    }
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated() {
    return !!this.user && !!apiClient.getToken();
  }

  /**
   * Get current user
   */
  getUser() {
    return this.user;
  }

  /**
   * Validate current authentication status
   */
  async validateAuth() {
    if (!this.isAuthenticated()) {
      return false;
    }

    try {
      this.user = await this.validateTokenWithBackend(apiClient.getToken());
      return true;
    } catch (error) {
      console.error('[ClerkAuth] Authentication validation failed:', error);
      await this.logout();
      return false;
    }
  }

  /**
   * Get authentication status for display
   */
  getAuthStatus() {
    if (!this.isAuthenticated()) {
      return {
        authenticated: false,
        message: 'Not authenticated'
      };
    }

    return {
      authenticated: true,
      user: this.user,
      role: this.user?.role || 'USER',
      message: `Authenticated as ${this.user?.email || 'Unknown'}`
    };
  }
}

// Export singleton instance
export const clerkAuthService = new ClerkAuthService();
export default clerkAuthService;