/**
 * KAPI Terminal Client - Conversation Service
 * 
 * Unified conversation management for KAPI Terminal Client.
 * Provides conversation CRUD operations, real-time streaming responses,
 * task strategy routing, and context-aware interactions.
 * 
 * This service is the core of AI-powered terminal assistance.
 */

import { apiClient } from './ApiClient.js';
import EventEmitter from './utils/EventEmitter.js';

// Available AI models for terminal client
const AVAILABLE_MODELS = [
  { id: 'gpt-4o', name: 'GPT-4 Omni', cost: 0.005 },
  { id: 'claude-3-5-sonnet-********', name: 'Claude 3.5 Sonnet', cost: 0.003 },
  { id: 'claude-3-5-haiku-********', name: '<PERSON> 3.5 Haiku', cost: 0.001 },
  { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash', cost: 0.002 },
];

const DEFAULT_MODEL = 'claude-3-5-sonnet-********';

// Task strategies available for terminal client
const TASK_STRATEGIES = [
  'chat',              // General conversational AI
  'code_generation',   // Creating code from specifications  
  'code_planning',     // Architectural planning and design
  'terminal_help',     // Terminal-specific assistance
  'debug_analysis',    // Debugging and error analysis
  'project_analysis',  // Project structure and insights
  'documentation',     // Documentation generation
];

export class ConversationService extends EventEmitter {
  constructor() {
    super();
    this.activeStreams = new Map();
  }

  /**
   * Get all conversations for the current user
   */
  async getConversations(filter = {}) {
    try {
      const params = {};
      
      if (filter.status) params.status = filter.status;
      if (filter.skip) params.skip = filter.skip;
      if (filter.limit) params.limit = filter.limit;
      if (filter.strategy) params.strategy = filter.strategy;

      console.log('[ConversationService] Fetching conversations with filter:', filter);
      const data = await apiClient.get('/conversations', params);

      return {
        conversations: data.conversations || data,
        total: data.total || data.length || 0,
      };
    } catch (error) {
      console.error('[ConversationService] Error fetching conversations:', error);
      this.emit('error', { type: 'fetch_conversations', error });
      throw error;
    }
  }

  /**
   * Get a specific conversation by ID with messages
   */
  async getConversation(conversationId) {
    try {
      console.log(`[ConversationService] Fetching conversation ${conversationId}`);
      const data = await apiClient.get(`/conversations/${conversationId}`);

      return {
        conversation: data.conversation || data,
        messages: data.messages || [],
      };
    } catch (error) {
      if (error.statusCode === 404) {
        return null;
      }
      console.error(`[ConversationService] Error fetching conversation ${conversationId}:`, error);
      this.emit('error', { type: 'fetch_conversation', conversationId, error });
      throw error;
    }
  }

  /**
   * Create a new conversation
   */
  async createConversation(options = {}) {
    try {
      const requestBody = {
        title: options.title || `New ${options.strategy || 'chat'} conversation`,
        taskType: options.strategy || 'chat',
        projectContext: options.projectContext,
      };

      console.log('[ConversationService] Creating conversation:', requestBody);
      const data = await apiClient.post('/conversations', requestBody);

      const conversation = data.conversation || data;
      this.emit('conversation_created', conversation);
      return conversation;
    } catch (error) {
      console.error('[ConversationService] Error creating conversation:', error);
      this.emit('error', { type: 'create_conversation', error });
      throw error;
    }
  }

  /**
   * Send a message and get a streaming response
   */
  async sendMessage(conversationId, message, options = {}) {
    const streamId = `${conversationId}-${Date.now()}`;
    
    try {
      if (!conversationId) {
        throw new Error('Conversation ID is required for streaming');
      }

      const requestBody = {
        prompt: message,
        ...(options.modelId && { model: options.modelId }),
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || 4000,
        taskType: options.strategy || 'chat',
        memoryCount: options.memoryCount || 10,
        projectContext: options.projectContext,
      };
      
      console.log('[ConversationService] Sending message:', {
        conversationId,
        strategy: options.strategy,
        model: options.modelId,
        messageLength: message.length
      });

      // Setup EventSource for streaming
      const eventSource = apiClient.setupEventSource(
        `/conversations/${conversationId}/stream`,
        apiClient.getToken()
      );

      // Store active stream for potential cleanup
      this.activeStreams.set(streamId, eventSource);

      // Send the message via POST first
      await apiClient.post(`/conversations/${conversationId}/stream`, requestBody);

      // Return async generator for streaming
      const self = this;
      return {
        async* [Symbol.asyncIterator]() {
          return new Promise((resolve, reject) => {
            let chunks = [];
            
            eventSource.onmessage = (event) => {
              try {
                const data = JSON.parse(event.data);
                chunks.push(data);
                self.emit('stream_chunk', { streamId, chunk: data });
                
                if (data.type === 'done') {
                  eventSource.close();
                  self.activeStreams.delete(streamId);
                  resolve(chunks);
                }
              } catch (error) {
                console.error('[ConversationService] Error parsing stream data:', error);
              }
            };

            eventSource.onerror = (error) => {
              console.error('[ConversationService] EventSource error:', error);
              eventSource.close();
              self.activeStreams.delete(streamId);
              self.emit('error', { type: 'stream_error', streamId, error });
              reject(error);
            };

            // Timeout after 30 seconds
            setTimeout(() => {
              if (self.activeStreams.has(streamId)) {
                eventSource.close();
                self.activeStreams.delete(streamId);
                reject(new Error('Stream timeout'));
              }
            }, 30000);
          });
        }
      };
    } catch (error) {
      console.error('[ConversationService] Error sending message:', error);
      this.activeStreams.delete(streamId);
      this.emit('error', { type: 'send_message', conversationId, error });
      throw error;
    }
  }

  /**
   * Send a non-streaming message (simple request-response)
   */
  async sendSimpleMessage(conversationId, message, options = {}) {
    try {
      if (!conversationId) {
        throw new Error('Conversation ID is required');
      }

      const requestBody = {
        prompt: message,
        ...(options.modelId && { model: options.modelId }),
        temperature: options.temperature || 0.7,
        maxTokens: options.maxTokens || 4000,
        taskType: options.strategy || 'chat',
        memoryCount: options.memoryCount || 10,
        projectContext: options.projectContext,
        stream: false, // Disable streaming
      };

      console.log('[ConversationService] Sending simple message:', {
        conversationId,
        strategy: options.strategy,
        model: options.modelId
      });

      const response = await apiClient.post(`/conversations/${conversationId}/messages`, requestBody);
      
      this.emit('message_sent', { conversationId, response });
      return response;
    } catch (error) {
      console.error('[ConversationService] Error sending simple message:', error);
      this.emit('error', { type: 'send_simple_message', conversationId, error });
      throw error;
    }
  }

  /**
   * Close all active streams
   */
  closeAllStreams() {
    console.log(`[ConversationService] Closing ${this.activeStreams.size} active streams`);
    
    for (const [streamId, eventSource] of this.activeStreams) {
      eventSource.close();
      console.log(`[ConversationService] Closed stream ${streamId}`);
    }
    
    this.activeStreams.clear();
  }

  /**
   * Close a specific stream
   */
  closeStream(streamId) {
    const eventSource = this.activeStreams.get(streamId);
    if (eventSource) {
      eventSource.close();
      this.activeStreams.delete(streamId);
      console.log(`[ConversationService] Closed stream ${streamId}`);
    }
  }

  /**
   * Update conversation title
   */
  async updateConversation(conversationId, updates) {
    try {
      console.log(`[ConversationService] Updating conversation ${conversationId}:`, updates);
      const response = await apiClient.patch(`/conversations/${conversationId}`, updates);
      
      this.emit('conversation_updated', { conversationId, updates, response });
      return response;
    } catch (error) {
      console.error('[ConversationService] Error updating conversation:', error);
      this.emit('error', { type: 'update_conversation', conversationId, error });
      throw error;
    }
  }

  /**
   * Archive a conversation
   */
  async archiveConversation(conversationId) {
    return this.updateConversation(conversationId, { status: 'archived' });
  }

  /**
   * Delete a conversation
   */
  async deleteConversation(conversationId) {
    try {
      console.log(`[ConversationService] Deleting conversation ${conversationId}`);
      await apiClient.delete(`/conversations/${conversationId}`);
      
      this.emit('conversation_deleted', { conversationId });
    } catch (error) {
      console.error('[ConversationService] Error deleting conversation:', error);
      this.emit('error', { type: 'delete_conversation', conversationId, error });
      throw error;
    }
  }

  /**
   * Get available AI models
   */
  getAvailableModels() {
    return AVAILABLE_MODELS;
  }

  /**
   * Get available task strategies
   */
  getAvailableStrategies() {
    return TASK_STRATEGIES;
  }

  /**
   * Get default model
   */
  getDefaultModel() {
    return DEFAULT_MODEL;
  }

  /**
   * Estimate cost for a message based on length and model
   */
  estimateCost(message, modelId = DEFAULT_MODEL) {
    // Rough token estimation (1 token ≈ 4 characters)
    const estimatedTokens = Math.ceil(message.length / 4);
    
    const model = AVAILABLE_MODELS.find(m => m.id === modelId);
    const modelCost = model ? model.cost : 0.003; // Default cost
    
    const estimatedCost = (estimatedTokens / 1000) * modelCost;

    return {
      estimatedTokens,
      estimatedCost: parseFloat(estimatedCost.toFixed(6)),
      model: model || { id: modelId, name: 'Unknown', cost: modelCost }
    };
  }

  /**
   * Get conversation statistics
   */
  async getConversationStats() {
    try {
      const response = await apiClient.get('/conversations/stats');
      return response;
    } catch (error) {
      console.error('[ConversationService] Error fetching conversation stats:', error);
      return {
        totalConversations: 0,
        totalMessages: 0,
        totalCost: 0,
        activeConversations: 0
      };
    }
  }
}

// Export singleton instance
export const conversationService = new ConversationService();
export default conversationService;