/**
 * KAPI Terminal Client - API Client Service
 * 
 * Base API client that provides a standardized interface for making requests to the backend.
 * Handles authentication, error handling, and provides methods for common HTTP operations.
 * 
 * This service is the foundation for all backend communication in the terminal client.
 */

import fetch from 'node-fetch';
import { EventSource } from 'eventsource';
import { VERSIONED_API_URL } from '../config/config.js';

/**
 * ApiError is a custom error class for API-related errors
 */
export class ApiError extends Error {
  constructor(message, statusCode) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
  }
}

/**
 * Simple token storage for terminal client
 * Uses in-memory storage - actual token persistence handled by ClerkAuthService
 */
class TokenStorage {
  constructor() {
    this.token = null;
  }

  setToken(token) {
    this.token = token;
  }

  getToken() {
    return this.token;
  }

  removeToken() {
    this.token = null;
  }
}

const tokenStorage = new TokenStorage();

export class ApiClient {
  constructor(baseUrl = VERSIONED_API_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Retrieves the authentication token
   */
  getToken() {
    return tokenStorage.getToken();
  }

  /**
   * Sets the authentication token
   */
  setToken(token) {
    tokenStorage.setToken(token);
  }

  /**
   * Removes the authentication token
   */
  removeToken() {
    tokenStorage.removeToken();
  }

  /**
   * Creates headers with proper authentication and content type
   */
  getHeaders(contentType = 'application/json') {
    const headers = {
      'Content-Type': contentType
    };

    const token = this.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Handles API responses and standardizes error handling
   */
  async handleResponse(response) {
    if (!response.ok) {
      let errorText;
      try {
        // Try to parse error response as JSON
        const errorData = await response.json();
        errorText = errorData.detail || errorData.message || `Error: ${response.status} ${response.statusText}`;
      } catch (e) {
        // Fallback to raw text or status
        try {
          errorText = await response.text();
        } catch (err) {
          errorText = `Error: ${response.status} ${response.statusText}`;
        }
      }

      // Throw a standardized API error
      throw new ApiError(errorText, response.status);
    }

    // For non-empty responses, parse JSON
    if (response.status !== 204) {
      return response.json();
    }

    return null;
  }

  /**
   * Performs a GET request to the specified endpoint
   */
  async get(endpoint, params = {}) {
    const url = new URL(`${this.baseUrl}${endpoint}`);

    // Add query parameters
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        url.searchParams.append(key, String(params[key]));
      }
    });

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getHeaders()
      });

      return this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        `Network error when fetching ${endpoint}: ${error.message}`,
        0
      );
    }
  }

  /**
   * Performs a POST request to the specified endpoint
   */
  async post(endpoint, data = {}) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      return this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        `Network error when posting to ${endpoint}: ${error.message}`,
        0
      );
    }
  }

  /**
   * Performs a PUT request to the specified endpoint
   */
  async put(endpoint, data = {}) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      return this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        `Network error when putting to ${endpoint}: ${error.message}`,
        0
      );
    }
  }

  /**
   * Performs a PATCH request to the specified endpoint
   */
  async patch(endpoint, data = {}) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PATCH',
        headers: this.getHeaders(),
        body: JSON.stringify(data)
      });

      return this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        `Network error when patching ${endpoint}: ${error.message}`,
        0
      );
    }
  }

  /**
   * Performs a DELETE request to the specified endpoint
   */
  async delete(endpoint) {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: this.getHeaders()
      });

      return this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        `Network error when deleting at ${endpoint}: ${error.message}`,
        0
      );
    }
  }

  /**
   * Sends form data via POST (useful for file uploads)
   */
  async postFormData(endpoint, formData) {
    try {
      const headers = {};
      const token = this.getToken();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      // No Content-Type header as fetch sets it with the boundary parameter

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers,
        body: formData
      });

      return this.handleResponse(response);
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      throw new ApiError(
        `Network error when posting form data to ${endpoint}: ${error.message}`,
        0
      );
    }
  }

  /**
   * Sets up an EventSource for server-sent events (streaming responses)
   */
  setupEventSource(endpoint, token) {
    const url = new URL(`${this.baseUrl}${endpoint}`);

    // For EventSource, we need to pass token as query param since headers aren't supported
    const authToken = token || this.getToken();
    if (authToken) {
      url.searchParams.append('token', authToken);
    }

    // Add a timestamp to prevent caching
    url.searchParams.append('_t', Date.now().toString());

    console.log(`[ApiClient] Setting up EventSource: ${url.toString()}`);

    try {
      return new EventSource(url.toString());
    } catch (error) {
      console.error('[ApiClient] Error creating EventSource:', error);
      throw new ApiError(
        `Failed to create EventSource for ${endpoint}: ${error.message}`,
        0
      );
    }
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();
export default apiClient;