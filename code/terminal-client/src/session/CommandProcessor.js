/**
 * KAPI Terminal Client - Command Processor
 * 
 * Processes natural language commands and routes them to appropriate handlers
 * within the interactive session context.
 */

import chalk from 'chalk';

export class CommandProcessor {
  constructor(session) {
    this.session = session;
    this.setupIntentPatterns();
  }

  /**
   * Setup natural language intent recognition patterns
   */
  setupIntentPatterns() {
    this.patterns = {
      // Session management
      help: /^(help|commands|\?|what can you do|show commands)$/i,
      status: /^(status|info|current|session|where am i)$/i,
      clear: /^(clear|cls)$/i,
      
      // Project analysis
      analysis: /^(analyze|check|review|examine|look at|assess)\s+(project|code|this|codebase)$/i,
      project_info: /^(what|show me|tell me about)\s+(type|kind|project|this project)$/i,
      security: /^(security|secure|vulnerabilities|check\s+security|security\s+issues?)$/i,
      
      // File operations  
      search_files: /^(find|search|show|locate|list)\s+(all\s+)?(.*?)(\s+files?|\s+comments?)?$/i,
      search_content: /^(search for|find|grep|look for)\s+"([^"]+)"$/i,
      
      // AI conversation
      question: /^(how|what|why|when|where|can\s+you|help me|explain|describe|tell me)/i,
      chat: /^(chat|talk|discuss|conversation|start chat)(\s+about\s+(.+))?$/i,
      
      // Authentication
      login: /^(login|auth|authenticate|connect)$/i,
      logout: /^(logout|disconnect)$/i,
    };
  }

  /**
   * Parse user input to determine intent
   */
  async parseIntent(input) {
    const cleanInput = input.trim();
    
    for (const [type, pattern] of Object.entries(this.patterns)) {
      const match = cleanInput.match(pattern);
      if (match) {
        return {
          type,
          input: cleanInput,
          params: match.slice(1).filter(Boolean),
          confidence: 'high'
        };
      }
    }

    // Default to general question/AI assistance
    return {
      type: 'question',
      input: cleanInput,
      params: [cleanInput],
      confidence: 'medium'
    };
  }

  /**
   * Execute command based on parsed intent
   */
  async executeCommand(intent, context) {
    const handler = this.getCommandHandler(intent.type);
    return await handler(intent, context);
  }

  /**
   * Get appropriate command handler for intent type
   */
  getCommandHandler(type) {
    const handlers = {
      help: this.handleHelp.bind(this),
      status: this.handleStatus.bind(this),
      clear: this.handleClear.bind(this),
      analysis: this.handleAnalysis.bind(this),
      project_info: this.handleProjectInfo.bind(this),
      security: this.handleSecurity.bind(this),
      search_files: this.handleSearchFiles.bind(this),
      search_content: this.handleSearchContent.bind(this),
      question: this.handleQuestion.bind(this),
      chat: this.handleChat.bind(this),
      login: this.handleLogin.bind(this),
      logout: this.handleLogout.bind(this)
    };

    return handlers[type] || this.handleUnknown.bind(this);
  }

  /**
   * Handle help command
   */
  async handleHelp(intent, context) {
    return {
      type: 'help',
      title: '📖 KAPI Commands & Examples',
      sections: [
        {
          title: '🔍 Project Analysis',
          commands: [
            'analyze project - Get comprehensive project insights',
            'what type of project is this? - Detect project type',
            'check for security issues - Security analysis',
            'status - Show current session info'
          ]
        },
        {
          title: '📁 File Operations',
          commands: [
            'find all TODO comments - Search for TODO items',
            'search for "authentication" - Find auth-related code',
            'locate config files - Find configuration files',
            'show me the largest files - File size analysis'
          ]
        },
        {
          title: '🤖 AI Assistance',
          commands: [
            'how do I implement JWT auth? - Get coding help',
            'explain this function [paste code] - Code explanation',
            'what\'s wrong with my code? - Code review',
            'help me optimize this component - Refactoring advice'
          ]
        },
        {
          title: '⚙️ Session Management',
          commands: [
            'help - Show this help screen',
            'clear - Clear terminal screen',
            'exit - End KAPI session'
          ]
        }
      ]
    };
  }

  /**
   * Handle status command
   */
  async handleStatus(intent, context) {
    const projectInfo = context.getProjectInfo();
    const commandCount = context.getCommandHistory().length;
    const conversationCount = context.getRecentConversation().length;

    return {
      type: 'status',
      data: {
        session: context.sessionNumber,
        project: {
          name: projectInfo.name || 'Unknown',
          type: projectInfo.type || 'Unknown',
          path: context.projectPath
        },
        activity: {
          commands: commandCount,
          conversations: conversationCount,
          startTime: context.sessionStartTime
        }
      }
    };
  }

  /**
   * Handle clear command
   */
  async handleClear(intent, context) {
    console.clear();
    return { type: 'clear', message: 'Screen cleared' };
  }

  /**
   * Handle project analysis
   */
  async handleAnalysis(intent, context) {
    const { terminalAIClient } = await import('../services/TerminalAIClient.js');
    
    if (!this.session.authService?.isAuthenticated()) {
      return {
        type: 'error',
        message: 'Authentication required for AI analysis. Run "login" first.'
      };
    }

    const analysis = await terminalAIClient.analyzeProject(context.projectPath);
    
    return {
      type: 'analysis',
      data: analysis,
      suggestions: this.extractActionableItems(analysis)
    };
  }

  /**
   * Handle project info request
   */
  async handleProjectInfo(intent, context) {
    const projectInfo = context.getProjectInfo();
    
    return {
      type: 'project_info',
      data: {
        name: projectInfo.name || 'Unknown Project',
        type: projectInfo.type || 'Unknown Type',
        path: context.projectPath,
        lastAnalyzed: projectInfo.lastAnalyzed
      }
    };
  }

  /**
   * Handle security analysis
   */
  async handleSecurity(intent, context) {
    const { terminalAIClient } = await import('../services/TerminalAIClient.js');
    
    if (!this.session.authService?.isAuthenticated()) {
      return {
        type: 'error',
        message: 'Authentication required for security analysis. Run "login" first.'
      };
    }

    const response = await terminalAIClient.getAssistance(
      'Perform a security analysis of this project, focusing on vulnerabilities, authentication issues, and best practices',
      context.getContextSnapshot()
    );
    
    return {
      type: 'security',
      data: response,
      suggestions: ['Run dependency audit', 'Review authentication code', 'Check for hardcoded secrets']
    };
  }

  /**
   * Handle file search
   */
  async handleSearchFiles(intent, context) {
    const { LocalSearchService } = await import('../services/LocalSearchService.js');
    const searchService = new LocalSearchService();
    
    const searchTerm = intent.params[2] || intent.params[1] || intent.params[0];
    
    const results = await searchService.searchFiles(searchTerm);
    
    return {
      type: 'search',
      query: searchTerm,
      results: results.slice(0, 20), // Limit results
      total: results.length
    };
  }

  /**
   * Handle content search
   */
  async handleSearchContent(intent, context) {
    const { LocalSearchService } = await import('../services/LocalSearchService.js');
    const searchService = new LocalSearchService();
    
    const searchTerm = intent.params[1] || intent.params[0];
    
    const results = await searchService.searchContent(searchTerm, {
      before: 2,
      after: 2
    });
    
    return {
      type: 'grep',
      query: searchTerm,
      results: results.slice(0, 15), // Limit results
      total: results.length
    };
  }

  /**
   * Handle general questions and AI assistance
   */
  async handleQuestion(intent, context) {
    const { terminalAIClient } = await import('../services/TerminalAIClient.js');
    const { authService } = await import('../services/AuthService.js');
    
    if (!authService.isAuthenticated()) {
      return {
        type: 'error',
        message: 'AI features require authentication. Run "login" to connect to KAPI AI.'
      };
    }

    const response = await terminalAIClient.getAssistance(
      intent.input,
      context.getContextSnapshot()
    );
    
    return {
      type: 'ai_response',
      question: intent.input,
      response: response.explanation,
      suggestions: response.suggestions || [],
      commands: response.commands || []
    };
  }

  /**
   * Handle chat command
   */
  async handleChat(intent, context) {
    return {
      type: 'info',
      message: 'Chat mode is the default! Just ask any question or give any command.',
      suggestions: [
        'Try: "how do I implement authentication?"',
        'Try: "analyze this project"',
        'Try: "find all TODO comments"'
      ]
    };
  }

  /**
   * Handle login command
   */
  async handleLogin(intent, context) {
    const { authService } = await import('../services/AuthService.js');
    
    if (authService.isAuthenticated()) {
      return {
        type: 'info',
        message: '✅ Already authenticated with KAPI AI',
        user: authService.getUser()
      };
    }
    
    // Actually perform the login
    try {
      const result = await authService.login();
      
      if (result.success) {
        return {
          type: 'success',
          message: '✅ Authentication successful!',
          user: authService.getUser()
        };
      } else {
        return {
          type: 'error',
          message: '❌ Authentication failed: ' + (result.message || 'Unknown error')
        };
      }
    } catch (error) {
      return {
        type: 'error',
        message: '❌ Authentication error: ' + error.message
      };
    }
  }

  /**
   * Handle logout command
   */
  async handleLogout(intent, context) {
    const { authService } = await import('../services/AuthService.js');
    
    await authService.logout();
    
    return {
      type: 'logout',
      message: '👋 Logged out from KAPI AI. You can still use local features.'
    };
  }

  /**
   * Handle unknown commands
   */
  async handleUnknown(intent, context) {
    const { terminalAIClient } = await import('../services/TerminalAIClient.js');
    const { authService } = await import('../services/AuthService.js');
    
    // If authenticated, try to handle with AI
    if (authService.isAuthenticated()) {
      try {
        const response = await terminalAIClient.getAssistance(
          intent.input,
          context.getContextSnapshot()
        );
        
        return {
          type: 'ai_response',
          question: intent.input,
          response: response.explanation,
          suggestions: response.suggestions || [],
          commands: response.commands || []
        };
      } catch (error) {
        // Fall through to error handling
      }
    }
    
    return {
      type: 'error',
      message: `Command not recognized: "${intent.input}"`,
      suggestions: [
        'Try "help" to see available commands',
        'Try "analyze project" to get project insights',
        'Try asking a question like "how do I..."'
      ]
    };
  }

  /**
   * Extract actionable items from analysis result
   */
  extractActionableItems(analysis) {
    const suggestions = [];
    
    if (analysis.explanation) {
      // Look for common patterns in AI responses that suggest actions
      const text = analysis.explanation.toLowerCase();
      
      if (text.includes('todo') || text.includes('fix')) {
        suggestions.push('Review and address TODO items');
      }
      if (text.includes('security') || text.includes('vulnerable')) {
        suggestions.push('Run security audit');
      }
      if (text.includes('test') || text.includes('testing')) {
        suggestions.push('Improve test coverage');
      }
      if (text.includes('documentation') || text.includes('readme')) {
        suggestions.push('Update project documentation');
      }
    }
    
    return suggestions.length > 0 ? suggestions : ['Continue exploring your project with KAPI'];
  }
}

export default CommandProcessor;