/**
 * KAPI Terminal Client - Interactive Session Manager
 * 
 * Main controller for interactive terminal sessions providing natural language
 * command processing with persistent context and AI agent integration.
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import ora from 'ora';
import { authService } from '../services/AuthService.js';
import { terminalAIClient } from '../services/TerminalAIClient.js';
import SessionContext from './SessionContext.js';
import CommandProcessor from './CommandProcessor.js';
import ResponseFormatter from './ResponseFormatter.js';

export class InteractiveSession {
  constructor() {
    this.running = false;
    this.context = new SessionContext();
    this.commandProcessor = new CommandProcessor(this);
    this.responseFormatter = new ResponseFormatter();
    this.sessionId = `session_${Date.now()}`;
  }

  /**
   * Start the interactive session
   */
  async start() {
    try {
      await this.initialize();
      await this.displayWelcome();
      await this.runInteractiveLoop();
      await this.cleanup();
    } catch (error) {
      console.error(chalk.red('\n❌ Session error:'), error.message);
      process.exit(1);
    }
  }

  /**
   * Initialize session components
   */
  async initialize() {
    const spinner = ora('Initializing KAPI session...').start();
    
    try {
      // Initialize authentication
      await authService.initialize();
      
      // Load project context
      await this.context.initialize();
      
      // Initialize AI client if authenticated
      if (authService.isAuthenticated()) {
        await terminalAIClient.initialize();
      }
      
      this.running = true;
      spinner.succeed('Session initialized');
    } catch (error) {
      spinner.fail('Initialization failed');
      throw error;
    }
  }

  /**
   * Display stylized welcome screen with ASCII art
   */
  async displayWelcome() {
    console.clear();
    
    // KAPI ASCII Art
    console.log(chalk.blue.bold(`
██╗  ██╗ █████╗ ██████╗ ██╗
██║ ██╔╝██╔══██╗██╔══██╗██║
█████╔╝ ███████║██████╔╝██║
██╔═██╗ ██╔══██║██╔═══╝ ██║
██║  ██╗██║  ██║██║     ██║
╚═╝  ╚═╝╚═╝  ╚═╝╚═╝     ╚═╝
    `));

    console.log(chalk.cyan.bold('    AI-Powered Development Assistant'));
    console.log(chalk.gray('    Interactive Terminal Client v1.0.0\n'));

    // Project context
    const projectInfo = this.context.getProjectInfo();
    if (projectInfo.name) {
      console.log(chalk.green('📂 Project:'), chalk.white(projectInfo.name));
      if (projectInfo.type) {
        console.log(chalk.green('🏗️  Type:'), chalk.white(projectInfo.type));
      }
    } else {
      console.log(chalk.yellow('📂 Project: Unknown (run "analyze project" to detect)'));
    }

    // Authentication status
    const isAuth = authService.isAuthenticated();
    const authStatus = isAuth ? 
      chalk.green('✅ Connected to KAPI AI') : 
      chalk.yellow('⚠️  Not authenticated (run "login" for AI features)');
    console.log(chalk.green('🤖 AI Status:'), authStatus);

    // Session info
    console.log(chalk.green('💬 Session:'), chalk.white(`#${this.context.sessionNumber}`));
    
    console.log(chalk.gray('\n' + '─'.repeat(60)));
    
    // How KAPI can help
    console.log(chalk.blue.bold('\n🚀 How KAPI Can Help You:'));
    console.log(chalk.white('  • ') + chalk.cyan('Analyze your project') + chalk.gray(' - "analyze this project"'));
    console.log(chalk.white('  • ') + chalk.cyan('Find files and code') + chalk.gray(' - "find all TODO comments"'));
    console.log(chalk.white('  • ') + chalk.cyan('Get AI assistance') + chalk.gray(' - "how do I implement JWT auth?"'));
    console.log(chalk.white('  • ') + chalk.cyan('Review code quality') + chalk.gray(' - "what\'s wrong with my code?"'));
    console.log(chalk.white('  • ') + chalk.cyan('Security analysis') + chalk.gray(' - "check for security issues"'));
    console.log(chalk.white('  • ') + chalk.cyan('Refactor and improve') + chalk.gray(' - "help me optimize this function"'));
    
    // Quick examples
    console.log(chalk.blue.bold('\n💡 Try These Commands:'));
    console.log(chalk.gray('  ├─ ') + chalk.yellow('analyze project') + chalk.gray(' - Get comprehensive project insights'));
    console.log(chalk.gray('  ├─ ') + chalk.yellow('search for "authentication"') + chalk.gray(' - Find auth-related code'));
    console.log(chalk.gray('  ├─ ') + chalk.yellow('what type of project is this?') + chalk.gray(' - AI project detection'));
    console.log(chalk.gray('  ├─ ') + chalk.yellow('help') + chalk.gray(' - Show available commands'));
    console.log(chalk.gray('  └─ ') + chalk.yellow('exit') + chalk.gray(' - End session'));

    if (!isAuth) {
      console.log(chalk.yellow('\n🔐 For full AI features, authenticate with:'), chalk.white('login'));
    }

    console.log(chalk.gray('\n' + '─'.repeat(60)));
    console.log(chalk.green.bold('\n✨ Ready! Type your command in natural language below:'));
  }

  /**
   * Main interactive command loop
   */
  async runInteractiveLoop() {
    while (this.running) {
      try {
        const { command } = await inquirer.prompt([{
          type: 'input',
          name: 'command',
          message: chalk.blue('kapi>'),
          prefix: ''
        }]);

        if (!command.trim()) continue;

        // Handle exit commands
        if (this.isExitCommand(command)) {
          await this.handleExit();
          break;
        }

        // Process command
        await this.processCommand(command.trim());
        
      } catch (error) {
        if (error.name === 'ExitPromptError') {
          // User pressed Ctrl+C
          await this.handleExit();
          break;
        }
        console.error(chalk.red('\n❌ Command error:'), error.message);
      }
    }
  }

  /**
   * Process a user command
   */
  async processCommand(input) {
    const spinner = ora().start();
    
    try {
      // Add to command history
      this.context.addToHistory(input);
      
      // Parse command intent
      const intent = await this.commandProcessor.parseIntent(input);
      
      // Route to appropriate handler
      const result = await this.commandProcessor.executeCommand(intent, this.context);
      
      // Display formatted response
      spinner.stop();
      this.responseFormatter.display(result);
      
      // Update session context
      this.context.updateWithResult(input, result);
      
    } catch (error) {
      spinner.fail('Command failed');
      console.error(chalk.red('❌ Error:'), error.message);
      
      // Suggest help for unrecognized commands
      if (error.message.includes('not recognized')) {
        console.log(chalk.yellow('\n💡 Try:'));
        console.log(chalk.gray('  • ') + chalk.cyan('help') + chalk.gray(' - Show available commands'));
        console.log(chalk.gray('  • ') + chalk.cyan('analyze project') + chalk.gray(' - Analyze your codebase'));
        console.log(chalk.gray('  • ') + chalk.cyan('search for "pattern"') + chalk.gray(' - Find files or code'));
      }
    }
  }

  /**
   * Check if command is an exit command
   */
  isExitCommand(command) {
    const exitCommands = ['exit', 'quit', 'q', 'bye', 'logout'];
    return exitCommands.includes(command.toLowerCase());
  }

  /**
   * Handle session exit
   */
  async handleExit() {
    console.log(chalk.yellow('\n👋 Ending KAPI session...'));
    
    const spinner = ora('Saving session data...').start();
    
    try {
      // Save session context
      await this.context.persist();
      
      // Close any active connections
      if (authService.isAuthenticated()) {
        await terminalAIClient.resetConversation();
      }
      
      this.running = false;
      spinner.succeed('Session saved');
      
      console.log(chalk.green('✨ Thanks for using KAPI! Your progress has been saved.'));
      console.log(chalk.gray('   Run "kapi" again anytime to continue where you left off.\n'));
      
    } catch (error) {
      spinner.fail('Error saving session');
      console.error(chalk.red('❌ Save error:'), error.message);
    }
  }

  /**
   * Cleanup session resources
   */
  async cleanup() {
    try {
      // Close any remaining connections
      if (this.context) {
        await this.context.cleanup();
      }
    } catch (error) {
      console.error(chalk.red('Cleanup error:'), error.message);
    }
  }

  /**
   * Get current session context
   */
  getContext() {
    return this.context;
  }

  /**
   * Get session ID
   */
  getSessionId() {
    return this.sessionId;
  }
}

export default InteractiveSession;