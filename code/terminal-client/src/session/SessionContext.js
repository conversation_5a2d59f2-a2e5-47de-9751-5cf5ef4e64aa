/**
 * KAPI Terminal Client - Session Context Manager
 * 
 * Manages persistent context, conversation history, and project understanding
 * across interactive terminal sessions.
 */

import fs from 'fs-extra';
import path from 'path';
import { LocalSearchService } from '../services/LocalSearchService.js';

export class SessionContext {
  constructor() {
    this.projectPath = process.cwd();
    this.projectInfo = {
      name: null,
      type: null,
      structure: null,
      lastAnalyzed: null
    };
    this.conversationHistory = [];
    this.commandHistory = [];
    this.sessionNumber = this.generateSessionNumber();
    this.sessionStartTime = new Date();
    this.kapiDir = path.join(this.projectPath, '.kapi');
    this.contextFile = path.join(this.kapiDir, 'context.json');
  }

  /**
   * Initialize session context
   */
  async initialize() {
    try {
      // Ensure .kapi directory exists
      await fs.ensureDir(this.kapiDir);
      
      // Load existing context if available
      await this.loadPersistedContext();
      
      // Auto-detect project info if not already known
      if (!this.projectInfo.name) {
        await this.detectProjectInfo();
      }
      
    } catch (error) {
      console.error('Error initializing session context:', error);
      // Continue with default context
    }
  }

  /**
   * Generate session number based on date and time
   */
  generateSessionNumber() {
    const now = new Date();
    return `${now.getMonth() + 1}${now.getDate()}-${now.getHours()}${now.getMinutes()}`;
  }

  /**
   * Load persisted context from .kapi directory
   */
  async loadPersistedContext() {
    try {
      if (await fs.pathExists(this.contextFile)) {
        const savedContext = await fs.readJson(this.contextFile);
        
        // Merge saved context with current
        this.projectInfo = { ...this.projectInfo, ...savedContext.projectInfo };
        this.conversationHistory = savedContext.conversationHistory || [];
        
        // Keep only recent conversation history (last 50 entries)
        this.conversationHistory = this.conversationHistory.slice(-50);
      }
    } catch (error) {
      console.error('Error loading persisted context:', error);
    }
  }

  /**
   * Auto-detect project information
   */
  async detectProjectInfo() {
    try {
      const searchService = new LocalSearchService();
      
      // Get project name from directory or package.json
      this.projectInfo.name = path.basename(this.projectPath);
      
      // Check for package.json
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (await fs.pathExists(packageJsonPath)) {
        const packageJson = await fs.readJson(packageJsonPath);
        this.projectInfo.name = packageJson.name || this.projectInfo.name;
        this.projectInfo.type = this.detectProjectType(packageJson);
      }
      
      // Check for other project indicators
      if (!this.projectInfo.type) {
        this.projectInfo.type = await this.detectProjectTypeFromFiles();
      }
      
      this.projectInfo.lastAnalyzed = new Date().toISOString();
      
    } catch (error) {
      console.error('Error detecting project info:', error);
    }
  }

  /**
   * Detect project type from package.json
   */
  detectProjectType(packageJson) {
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    if (dependencies.react) {
      return 'React Application';
    } else if (dependencies.vue) {
      return 'Vue.js Application';
    } else if (dependencies.angular || dependencies['@angular/core']) {
      return 'Angular Application';
    } else if (dependencies.express) {
      return 'Node.js/Express API';
    } else if (dependencies.next) {
      return 'Next.js Application';
    } else if (dependencies.gatsby) {
      return 'Gatsby Site';
    } else if (packageJson.type === 'module' || dependencies.typescript) {
      return 'Node.js Project';
    }
    
    return 'JavaScript Project';
  }

  /**
   * Detect project type from file patterns
   */
  async detectProjectTypeFromFiles() {
    try {
      const searchService = new LocalSearchService();
      
      // Check for Python files
      const pythonFiles = await searchService.searchFiles('*.py');
      if (pythonFiles.length > 0) {
        return 'Python Project';
      }
      
      // Check for Java files
      const javaFiles = await searchService.searchFiles('*.java');
      if (javaFiles.length > 0) {
        return 'Java Project';
      }
      
      // Check for Go files
      const goFiles = await searchService.searchFiles('*.go');
      if (goFiles.length > 0) {
        return 'Go Project';
      }
      
      // Check for Rust files
      const rustFiles = await searchService.searchFiles('*.rs');
      if (rustFiles.length > 0) {
        return 'Rust Project';
      }
      
      return 'Unknown Project';
      
    } catch (error) {
      return 'Unknown Project';
    }
  }

  /**
   * Add command to history
   */
  addToHistory(command) {
    this.commandHistory.push({
      command,
      timestamp: new Date().toISOString()
    });
    
    // Keep only recent commands (last 100)
    if (this.commandHistory.length > 100) {
      this.commandHistory = this.commandHistory.slice(-100);
    }
  }

  /**
   * Add conversation entry
   */
  addToConversation(input, response) {
    this.conversationHistory.push({
      input,
      response,
      timestamp: new Date().toISOString(),
      sessionId: this.sessionNumber
    });
    
    // Keep only recent conversation (last 50 entries)
    if (this.conversationHistory.length > 50) {
      this.conversationHistory = this.conversationHistory.slice(-50);
    }
  }

  /**
   * Update context with command result
   */
  updateWithResult(input, result) {
    // Add to conversation history
    this.addToConversation(input, result);
    
    // Update project info if result contains project analysis
    if (result.type === 'analysis' && result.data) {
      this.projectInfo.lastAnalyzed = new Date().toISOString();
      if (result.data.projectType) {
        this.projectInfo.type = result.data.projectType;
      }
    }
  }

  /**
   * Get project information
   */
  getProjectInfo() {
    return this.projectInfo;
  }

  /**
   * Get recent conversation history
   */
  getRecentConversation(count = 5) {
    return this.conversationHistory.slice(-count);
  }

  /**
   * Get command history
   */
  getCommandHistory(count = 10) {
    return this.commandHistory.slice(-count);
  }

  /**
   * Get context snapshot for AI requests
   */
  getContextSnapshot() {
    return {
      projectPath: this.projectPath,
      projectInfo: this.projectInfo,
      recentConversation: this.getRecentConversation(),
      recentCommands: this.getCommandHistory().map(h => h.command)
    };
  }

  /**
   * Persist context to .kapi directory
   */
  async persist() {
    try {
      const contextData = {
        projectInfo: this.projectInfo,
        conversationHistory: this.conversationHistory,
        sessionInfo: {
          sessionNumber: this.sessionNumber,
          startTime: this.sessionStartTime,
          endTime: new Date(),
          commandCount: this.commandHistory.length
        }
      };
      
      await fs.writeJson(this.contextFile, contextData, { spaces: 2 });
      
      // Also save a session log
      const logFile = path.join(this.kapiDir, `session-${this.sessionNumber}.log`);
      const logData = this.commandHistory.map(h => `[${h.timestamp}] ${h.command}`).join('\n');
      await fs.writeFile(logFile, logData, 'utf8');
      
    } catch (error) {
      console.error('Error persisting context:', error);
    }
  }

  /**
   * Cleanup context resources
   */
  async cleanup() {
    try {
      await this.persist();
    } catch (error) {
      console.error('Error during context cleanup:', error);
    }
  }
}

export default SessionContext;