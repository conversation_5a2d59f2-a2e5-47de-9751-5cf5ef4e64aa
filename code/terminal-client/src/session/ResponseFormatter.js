/**
 * KAPI Terminal Client - Response Formatter
 * 
 * Formats and displays various types of responses in the terminal with
 * rich styling, colors, and interactive elements.
 */

import chalk from 'chalk';

export class ResponseFormatter {
  
  /**
   * Display formatted response based on type
   */
  display(result) {
    if (!result) return;
    
    console.log(); // Empty line before response
    
    switch (result.type) {
      case 'help':
        this.displayHelp(result);
        break;
      case 'status':
        this.displayStatus(result);
        break;
      case 'analysis':
        this.displayAnalysis(result);
        break;
      case 'project_info':
        this.displayProjectInfo(result);
        break;
      case 'security':
        this.displaySecurity(result);
        break;
      case 'search':
        this.displaySearchResults(result);
        break;
      case 'grep':
        this.displayGrepResults(result);
        break;
      case 'ai_response':
        this.displayAIResponse(result);
        break;
      case 'error':
        this.displayError(result);
        break;
      case 'info':
        this.displayInfo(result);
        break;
      case 'login_required':
        this.displayLoginRequired(result);
        break;
      case 'logout':
        this.displayLogout(result);
        break;
      default:
        this.displayGeneral(result);
    }
    
    this.displaySuggestions(result.suggestions);
    console.log(); // Empty line after response
  }

  /**
   * Display help information
   */
  displayHelp(result) {
    console.log(chalk.blue.bold(result.title));
    console.log(chalk.gray('─'.repeat(60)));
    
    result.sections.forEach(section => {
      console.log(chalk.yellow.bold(`\n${section.title}`));
      section.commands.forEach(cmd => {
        const [command, description] = cmd.split(' - ');
        console.log(chalk.white(`  • `) + chalk.cyan(command) + chalk.gray(` - ${description}`));
      });
    });
    
    console.log(chalk.gray('\n💡 Pro tip: You can ask questions in natural language!'));
  }

  /**
   * Display session status
   */
  displayStatus(result) {
    console.log(chalk.blue.bold('📊 Session Status'));
    console.log(chalk.gray('─'.repeat(30)));
    
    const { session, project, activity } = result.data;
    
    console.log(chalk.green('💬 Session:'), chalk.white(`#${session}`));
    console.log(chalk.green('📂 Project:'), chalk.white(project.name));
    console.log(chalk.green('🏗️  Type:'), chalk.white(project.type));
    console.log(chalk.green('📁 Path:'), chalk.gray(project.path));
    
    const duration = Math.floor((new Date() - new Date(activity.startTime)) / 1000 / 60);
    console.log(chalk.green('⏱️  Duration:'), chalk.white(`${duration} minutes`));
    console.log(chalk.green('📝 Commands:'), chalk.white(activity.commands));
    console.log(chalk.green('💭 Conversations:'), chalk.white(activity.conversations));
  }

  /**
   * Display project analysis results
   */
  displayAnalysis(result) {
    console.log(chalk.blue.bold('📊 Project Analysis'));
    console.log(chalk.gray('─'.repeat(40)));
    
    if (result.data.explanation) {
      console.log(chalk.white(result.data.explanation));
    }
    
    if (result.data.issues && result.data.issues.length > 0) {
      console.log(chalk.yellow.bold('\n⚠️  Issues Found:'));
      result.data.issues.forEach((issue, i) => {
        const severity = this.getSeverityIcon(issue.severity);
        console.log(`  ${severity} ${chalk.white(issue.description)}`);
      });
    }
    
    if (result.data.metrics) {
      console.log(chalk.cyan.bold('\n📈 Metrics:'));
      Object.entries(result.data.metrics).forEach(([key, value]) => {
        console.log(`  ${chalk.green('●')} ${chalk.white(key)}: ${chalk.yellow(value)}`);
      });
    }
  }

  /**
   * Display project information
   */
  displayProjectInfo(result) {
    const { name, type, path, lastAnalyzed } = result.data;
    
    console.log(chalk.blue.bold('📂 Project Information'));
    console.log(chalk.gray('─'.repeat(30)));
    
    console.log(chalk.green('Name:'), chalk.white(name));
    console.log(chalk.green('Type:'), chalk.white(type));
    console.log(chalk.green('Path:'), chalk.gray(path));
    
    if (lastAnalyzed) {
      const analyzedDate = new Date(lastAnalyzed).toLocaleString();
      console.log(chalk.green('Last Analyzed:'), chalk.yellow(analyzedDate));
    }
  }

  /**
   * Display security analysis
   */
  displaySecurity(result) {
    console.log(chalk.red.bold('🔒 Security Analysis'));
    console.log(chalk.gray('─'.repeat(35)));
    
    if (result.data.explanation) {
      console.log(chalk.white(result.data.explanation));
    }
    
    if (result.data.vulnerabilities) {
      console.log(chalk.red.bold('\n🚨 Vulnerabilities Found:'));
      result.data.vulnerabilities.forEach(vuln => {
        console.log(`  ${chalk.red('●')} ${chalk.white(vuln)}`);
      });
    }
  }

  /**
   * Display file search results
   */
  displaySearchResults(result) {
    const { query, results, total } = result;
    
    console.log(chalk.blue.bold(`🔍 Search Results: "${query}"`));
    console.log(chalk.gray(`Found ${total} matches`));
    console.log(chalk.gray('─'.repeat(40)));
    
    if (results.length === 0) {
      console.log(chalk.yellow('No files found matching your search.'));
      return;
    }
    
    results.forEach((file, i) => {
      const number = chalk.blue(`${(i + 1).toString().padStart(2)}.`);
      const type = file.type ? chalk.gray(`[${file.type}]`) : '';
      const path = chalk.white(file.relativePath);
      console.log(`  ${number} ${type} ${path}`);
    });
    
    if (total > results.length) {
      console.log(chalk.gray(`\n... and ${total - results.length} more files`));
    }
  }

  /**
   * Display grep search results
   */
  displayGrepResults(result) {
    const { query, results, total } = result;
    
    console.log(chalk.blue.bold(`🔎 Content Search: "${query}"`));
    console.log(chalk.gray(`Found ${total} matches`));
    console.log(chalk.gray('─'.repeat(40)));
    
    if (results.length === 0) {
      console.log(chalk.yellow('No content matches found.'));
      return;
    }
    
    results.forEach((match, i) => {
      const number = chalk.blue(`${(i + 1).toString().padStart(2)}.`);
      const file = chalk.cyan(match.file);
      const line = chalk.gray(`:${match.lineNumber}`);
      
      console.log(`\n  ${number} ${file}${line}`);
      
      // Show context lines
      if (match.before && match.before.length > 0) {
        match.before.forEach((beforeLine, idx) => {
          const beforeNum = match.lineNumber - match.before.length + idx;
          console.log(chalk.gray(`      ${beforeNum}: ${beforeLine.trim()}`));
        });
      }
      
      // Show matching line with highlighting
      const highlightedLine = match.line.replace(
        new RegExp(query, 'gi'),
        chalk.black.bgYellow('$&')
      );
      console.log(`      ${chalk.green(match.lineNumber)}: ${highlightedLine}`);
      
      // Show after context
      if (match.after && match.after.length > 0) {
        match.after.forEach((afterLine, idx) => {
          const afterNum = match.lineNumber + 1 + idx;
          console.log(chalk.gray(`      ${afterNum}: ${afterLine.trim()}`));
        });
      }
    });
    
    if (total > results.length) {
      console.log(chalk.gray(`\n... and ${total - results.length} more matches`));
    }
  }

  /**
   * Display AI response
   */
  displayAIResponse(result) {
    console.log(chalk.blue.bold(`🤖 AI Response`));
    console.log(chalk.gray('─'.repeat(30)));
    
    if (result.question) {
      console.log(chalk.yellow.bold('Q:'), chalk.white(result.question));
      console.log();
    }
    
    console.log(chalk.white(result.response));
    
    if (result.commands && result.commands.length > 0) {
      console.log(chalk.cyan.bold('\n💻 Suggested Commands:'));
      result.commands.forEach((cmd, i) => {
        console.log(`  ${chalk.gray(`${i + 1}.`)} ${chalk.yellow(cmd)}`);
      });
    }
  }

  /**
   * Display error message
   */
  displayError(result) {
    console.log(chalk.red.bold('❌ Error'));
    console.log(chalk.red(result.message));
  }

  /**
   * Display info message
   */
  displayInfo(result) {
    console.log(chalk.blue.bold('ℹ️  Info'));
    console.log(chalk.white(result.message));
    
    if (result.user) {
      console.log(chalk.green('User:'), chalk.white(result.user.username || result.user.email));
    }
  }

  /**
   * Display login required message
   */
  displayLoginRequired(result) {
    console.log(chalk.yellow.bold('🔐 Authentication Required'));
    console.log(chalk.white(result.message));
    console.log(chalk.gray('Note: You can still use local features without authentication.'));
  }

  /**
   * Display logout confirmation
   */
  displayLogout(result) {
    console.log(chalk.green.bold('✅ Logout Successful'));
    console.log(chalk.white(result.message));
  }

  /**
   * Display general response
   */
  displayGeneral(result) {
    if (result.message) {
      console.log(chalk.white(result.message));
    } else if (result.data) {
      console.log(chalk.white(JSON.stringify(result.data, null, 2)));
    }
  }

  /**
   * Display actionable suggestions
   */
  displaySuggestions(suggestions) {
    if (!suggestions || suggestions.length === 0) return;
    
    console.log(chalk.cyan.bold('💡 Suggestions:'));
    suggestions.forEach((suggestion, i) => {
      console.log(`  ${chalk.gray(`${i + 1}.`)} ${chalk.white(suggestion)}`);
    });
  }

  /**
   * Get severity icon based on level
   */
  getSeverityIcon(severity) {
    switch (severity?.toLowerCase()) {
      case 'high':
      case 'critical':
        return chalk.red('🔴');
      case 'medium':
      case 'warning':
        return chalk.yellow('🟡');
      case 'low':
      case 'info':
        return chalk.green('🟢');
      default:
        return chalk.blue('🔵');
    }
  }
}

export default ResponseFormatter;