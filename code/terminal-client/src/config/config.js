/**
 * KAPI Terminal Client Configuration
 * 
 * Provides environment-based configuration for API endpoints,
 * authentication, and feature flags.
 */

// Environment detection
export const isDev = process.env.NODE_ENV === 'development';

// Feature flags
export const useMockAuth = process.env.KAPI_USE_MOCK_AUTH === 'true';
export const useDebugProviders = process.env.KAPI_USE_DEBUG_PROVIDERS === 'true';

// Authentication configuration (via backend)
export const useDevelopmentAuth = process.env.USE_DEVELOPMENT_AUTH === 'true';
export const DEV_BYPASS_TOKEN = process.env.DEV_BYPASS_TOKEN || 'kapi_dev_unified_2024';

// Base URL for API requests
export const API_BASE_URL = process.env.KAPI_API_URL || (() => {
  // For production, we should not default to localhost
  if (process.env.NODE_ENV === 'production') {
    throw new Error('KAPI_API_URL environment variable must be set for production');
  }
  return 'http://localhost:3000';
})();

// API version prefix - matches NodeJS backend (/api)
export const API_VERSION = '/api';

// Versioned API URL
export const VERSIONED_API_URL = `${API_BASE_URL}${API_VERSION}`;

// Development token for bypassing auth in development
export const MOCK_USER_TOKEN = 'kapi_dev_terminal_token_2024';

// Configuration logging
if (isDev) {
  console.log('[KAPI Terminal Config]', {
    API_BASE_URL,
    API_VERSION,
    VERSIONED_API_URL,
    useMockAuth,
    useDebugProviders
  });
}

// Feature flags
export const ENABLE_STREAMING = true;
export const ENABLE_WEBSOCKETS = true;
export const INACTIVITY_TIMEOUT = 15 * 60 * 1000; // 15 minutes

export default {
  API_BASE_URL,
  API_VERSION,
  VERSIONED_API_URL,
  MOCK_USER_TOKEN,
  isDev,
  useMockAuth,
  useDebugProviders,
  ENABLE_STREAMING,
  ENABLE_WEBSOCKETS,
  INACTIVITY_TIMEOUT
};