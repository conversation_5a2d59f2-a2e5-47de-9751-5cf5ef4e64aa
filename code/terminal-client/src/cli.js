#!/usr/bin/env node

/**
 * KAPI Terminal Client - Main CLI Entry Point
 * 
 * This is the main command-line interface for KAPI Terminal Client.
 * It sets up the CLI framework and routes commands to appropriate handlers.
 * 
 * Features implemented:
 * - Feature #0: Project Setup & Architecture Foundation
 * - Feature #1: Local Code Intelligence & Search Engine (foundation)
 */

import { program } from 'commander';
import chalk from 'chalk';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

// Get current directory and package.json
const __dirname = dirname(fileURLToPath(import.meta.url));
const require = createRequire(import.meta.url);
const packageJson = require('../package.json');

// Import services
import { LocalSearchService } from './services/LocalSearchService.js';
import { authService } from './services/AuthService.js';
import { conversationService } from './services/ConversationService.js';
import { terminalAIClient } from './services/TerminalAIClient.js';
import InteractiveSession from './session/InteractiveSession.js';
import ora from 'ora';
import inquirer from 'inquirer';

/**
 * Initialize KAPI CLI with version and description
 */
program
  .name('kapi')
  .description('🤖 KAPI Interactive Terminal - AI-powered development assistant\n\n' +
    chalk.cyan('💡 New Interactive Mode:') + ' Run ' + chalk.yellow('kapi') + ' with no arguments for an interactive AI session\n' +
    chalk.cyan('🚀 Try:') + ' kapi → then type "analyze project" or "help" for natural language commands\n\n' +
    chalk.gray('Legacy command mode (below) also available:'))
  .version(packageJson.version)
  .option('-v, --verbose', 'enable verbose output')
  .option('--config <path>', 'specify config file path')
  .option('--auth', 'force authentication check');

/**
 * Feature #1: Local Code Intelligence & Search Engine Commands
 */
program
  .command('search')
  .description('🔍 Search through local codebase')
  .argument('<pattern>', 'search pattern')
  .option('-t, --type <type>', 'file type filter (js, ts, py, etc)')
  .option('-d, --depth <number>', 'maximum directory depth', '5')
  .option('--semantic', 'enable semantic search')
  .action(async (pattern, options) => {
    console.log(chalk.blue('🔍 KAPI Local Search'));
    
    if (options.semantic) {
      console.log(chalk.yellow('⚡ Semantic search enabled (requires backend connection)'));
      console.log(chalk.red('⚠️  Semantic search not yet implemented'));
      return;
    }
    
    try {
      const searchService = new LocalSearchService({
        maxDepth: parseInt(options.depth) || 5
      });
      
      const results = await searchService.searchFiles(pattern, {
        type: options.type
      });
      
      if (results.length === 0) {
        console.log(chalk.yellow('❌ No files found matching pattern'));
        return;
      }
      
      console.log(chalk.green(`\n📄 Found ${results.length} matching files:\n`));
      
      results.forEach((file, index) => {
        const number = chalk.blue(`${(index + 1).toString().padStart(2)}.`);
        const type = file.type ? chalk.gray(`[${file.type}]`) : '';
        const path = chalk.white(file.relativePath);
        console.log(`  ${number} ${type} ${path}`);
      });
      
    } catch (error) {
      console.error(chalk.red('❌ Search failed:'), error.message);
    }
  });

program
  .command('grep')
  .description('🔎 Advanced grep with pattern matching')
  .argument('<pattern>', 'grep pattern')
  .option('--semantic', 'enable semantic understanding')
  .option('-A, --after <num>', 'lines after match', '0')
  .option('-B, --before <num>', 'lines before match', '0')
  .action(async (pattern, options) => {
    console.log(chalk.blue('🔎 KAPI Advanced Grep'));
    
    if (options.semantic) {
      console.log(chalk.yellow('⚡ Semantic search enabled (requires backend connection)'));
      console.log(chalk.red('⚠️  Semantic grep not yet implemented'));
      return;
    }
    
    try {
      const searchService = new LocalSearchService();
      
      const results = await searchService.searchContent(pattern, {
        before: parseInt(options.before) || 0,
        after: parseInt(options.after) || 0
      });
      
      if (results.length === 0) {
        console.log(chalk.yellow('❌ No content matches found'));
        return;
      }
      
      console.log(chalk.green(`\n🔍 Found ${results.length} matches:\n`));
      
      results.forEach((match, index) => {
        const number = chalk.blue(`${(index + 1).toString().padStart(2)}.`);
        const file = chalk.cyan(match.file);
        const line = chalk.gray(`:${match.lineNumber}`);
        
        console.log(`  ${number} ${file}${line}`);
        
        // Show before context
        if (match.before.length > 0) {
          match.before.forEach((beforeLine, i) => {
            const beforeNum = match.lineNumber - match.before.length + i;
            console.log(chalk.gray(`      ${beforeNum}: ${beforeLine.trim()}`));
          });
        }
        
        // Show matching line with highlighting
        const highlightedLine = match.line.replace(
          new RegExp(pattern, 'gi'),
          chalk.black.bgYellow('$&')
        );
        console.log(`      ${chalk.green(match.lineNumber)}: ${highlightedLine}`);
        
        // Show after context
        if (match.after.length > 0) {
          match.after.forEach((afterLine, i) => {
            const afterNum = match.lineNumber + 1 + i;
            console.log(chalk.gray(`      ${afterNum}: ${afterLine.trim()}`));
          });
        }
        
        console.log(''); // Empty line between matches
      });
      
    } catch (error) {
      console.error(chalk.red('❌ Grep failed:'), error.message);
    }
  });

program
  .command('index')
  .description('📚 Build or rebuild local file index')
  .option('--rebuild', 'force rebuild of entire index')
  .option('--incremental', 'incremental index update')
  .action(async (options) => {
    console.log(chalk.blue('📚 KAPI File Indexer'));
    
    if (options.rebuild) {
      console.log(chalk.yellow('🔄 Rebuilding entire index...'));
    } else if (options.incremental) {
      console.log(chalk.yellow('⚡ Incremental index update...'));  
    } else {
      console.log(chalk.yellow('📖 Building file index...'));
    }
    
    // TODO: Implement file indexing
    console.log(chalk.red('⚠️  File indexing not yet implemented'));
    console.log(chalk.gray('This will be implemented as part of Feature #1'));
  });

/**
 * Development and status commands
 */
program
  .command('init')
  .description('🚀 Initialize KAPI in current project')
  .action(async () => {
    console.log(chalk.blue('🚀 Initializing KAPI Terminal Client'));
    console.log(chalk.gray(`Working directory: ${process.cwd()}`));
    
    // TODO: Create .kapi directory and initial configuration
    console.log(chalk.yellow('📁 Creating .kapi directory structure...'));
    console.log(chalk.red('⚠️  Initialization not yet implemented'));
    console.log(chalk.gray('This will create local project cache similar to Python version'));
  });

program
  .command('status')
  .description('📊 Show KAPI client status and configuration')
  .action(async () => {
    const spinner = ora('Checking KAPI status...').start();
    
    try {
      // Initialize auth service first
      await authService.initialize();
      
      console.log(chalk.blue('📊 KAPI Terminal Client Status'));
      console.log(chalk.green(`✅ Version: ${packageJson.version}`));
      console.log(chalk.green(`✅ Node.js: ${process.version}`));
      console.log(chalk.green(`✅ Working Directory: ${process.cwd()}`));
      
      // Get detailed authentication status
      const authStatus = authService.getAuthStatus();
      const statusIcon = authStatus.authenticated ? '✅' : '❌';
      const statusColor = authStatus.authenticated ? chalk.green : chalk.red;
      console.log(`\n🔐 Authentication: ${statusIcon} ${statusColor(authStatus.message)}`);
      
      if (authStatus.authenticated && authStatus.user) {
        console.log(chalk.gray(`   User: ${authStatus.user.email || authStatus.user.username}`));
        console.log(chalk.gray(`   Role: ${authStatus.role}`));
        console.log(chalk.gray(`   ID: ${authStatus.user.clerk_id || authStatus.user.id}`));
      }
      
      // Check backend connectivity
      const isConnected = await terminalAIClient.checkConnection();
      const connectionStatus = isConnected ? chalk.green('✅ Connected') : chalk.red('❌ Disconnected');
      console.log(`🌐 Backend Connection: ${connectionStatus}`);
      
      console.log(chalk.gray('\n🔧 Configuration:'));
      console.log(chalk.gray(`  Backend URL: ${process.env.KAPI_API_URL || 'http://localhost:3000'}`));
      console.log(chalk.gray(`  Auth Method: ${process.env.USE_DEVELOPMENT_AUTH === 'true' ? 'Development Bypass' : 'Clerk OAuth'}`));
      console.log(chalk.gray(`  Development Mode: ${process.env.NODE_ENV === 'development' ? 'Yes' : 'No'}`));
      console.log(chalk.gray(`  Local Index: ${isConnected ? 'Available' : 'Not available'}`));
      
      spinner.succeed('Status check complete');
    } catch (error) {
      spinner.fail('Status check failed');
      console.error(chalk.red('❌ Error checking status:'), error.message);
    }
  });

/**
 * Feature #2: Backend Agent & Conversation Bridge Commands
 */
program
  .command('login')
  .description('🔐 Authenticate with KAPI backend using browser')
  .action(async () => {
    try {
      // Initialize auth service
      await authService.initialize();
      
      // Check if already authenticated
      if (authService.isAuthenticated()) {
        const user = authService.getUser();
        if (user) {
          console.log(chalk.green(`✅ Already logged in as: ${user.email || user.username}`));
          console.log(chalk.gray(`   Role: ${user.role || 'USER'}`));
        }
        return;
      }

      console.log(chalk.blue('🔐 KAPI Authentication'));
      console.log(chalk.gray('This will open your browser for secure authentication\n'));
      
      // Start browser-based login flow
      const result = await authService.login();
      
      if (result.success && result.user) {
        console.log(chalk.green(`\n✅ Welcome, ${result.user.first_name || result.user.email}!`));
        console.log(chalk.gray(`   Role: ${result.user.role || 'USER'}`));
        console.log(chalk.gray(`   You can now access all KAPI features\n`));
      }
    } catch (error) {
      console.error(chalk.red('❌ Authentication failed:'), error.message);
      
      if (error.message.includes('timeout') || error.message.includes('expired')) {
        console.log(chalk.yellow('💡 Try running the login command again'));
      }
    }
  });

program
  .command('logout')
  .description('🔓 Logout from KAPI backend')
  .action(async () => {
    const spinner = ora('Logging out...').start();
    
    try {
      await authService.logout();
      spinner.succeed('Logged out successfully');
      console.log(chalk.yellow('👋 See you later!'));
    } catch (error) {
      spinner.fail('Logout failed');
      console.error(chalk.red('❌ Logout error:'), error.message);
    }
  });

program
  .command('chat')
  .description('💬 Start an AI chat session')
  .option('-m, --model <model>', 'specify AI model to use')
  .option('-s, --strategy <strategy>', 'conversation strategy (chat, code_generation, etc.)')
  .action(async (options) => {
    const spinner = ora('Starting AI chat session...').start();
    
    try {
      // Check authentication
      if (!authService.isAuthenticated()) {
        spinner.fail('Authentication required');
        console.log(chalk.yellow('Please run "kapi login" first'));
        return;
      }

      // Initialize terminal AI client
      await terminalAIClient.initialize();
      spinner.succeed('AI chat session ready');
      
      console.log(chalk.blue('💬 KAPI AI Chat'));
      console.log(chalk.gray('Type "exit" or "quit" to end the session\n'));
      
      if (options.model) {
        console.log(chalk.gray(`Using model: ${options.model}`));
      }
      if (options.strategy) {
        console.log(chalk.gray(`Strategy: ${options.strategy}`));
      }

      // Interactive chat loop
      while (true) {
        const { message } = await inquirer.prompt([{
          type: 'input',
          name: 'message',
          message: 'You:',
        }]);

        if (message.toLowerCase() === 'exit' || message.toLowerCase() === 'quit') {
          console.log(chalk.yellow('👋 Chat session ended'));
          break;
        }

        if (!message.trim()) continue;

        const thinkingSpinner = ora('AI is thinking...').start();
        
        try {
          const response = await terminalAIClient.getAssistance(message, {
            currentDirectory: process.cwd(),
            strategy: options.strategy
          });
          
          thinkingSpinner.succeed('Response ready');
          console.log(chalk.green('AI:'), response.explanation);
          
          if (response.commands && response.commands.length > 0) {
            console.log(chalk.blue('\nSuggested commands:'));
            response.commands.forEach((cmd, i) => {
              console.log(chalk.gray(`  ${i + 1}. ${cmd}`));
            });
          }
          console.log('');
        } catch (error) {
          thinkingSpinner.fail('Failed to get response');
          console.error(chalk.red('❌ Error:'), error.message);
        }
      }
    } catch (error) {
      spinner.fail('Failed to start chat session');
      console.error(chalk.red('❌ Chat error:'), error.message);
    }
  });

program
  .command('ask')
  .description('❓ Ask AI a quick question')
  .argument('<question>', 'question to ask the AI')
  .option('-m, --model <model>', 'specify AI model to use')
  .option('-s, --strategy <strategy>', 'conversation strategy')
  .action(async (question, options) => {
    const spinner = ora('Getting AI response...').start();
    
    try {
      // Check authentication
      if (!authService.isAuthenticated()) {
        spinner.fail('Authentication required');
        console.log(chalk.yellow('Please run "kapi login" first'));
        return;
      }

      // Initialize and get response
      await terminalAIClient.initialize();
      const response = await terminalAIClient.getAssistance(question, {
        currentDirectory: process.cwd(),
        strategy: options.strategy
      });
      
      spinner.succeed('AI response ready');
      
      console.log(chalk.blue(`\n❓ Question: ${question}`));
      console.log(chalk.green(`🤖 Answer: ${response.explanation}`));
      
      if (response.commands && response.commands.length > 0) {
        console.log(chalk.blue('\n💡 Suggested commands:'));
        response.commands.forEach((cmd, i) => {
          console.log(chalk.gray(`  ${i + 1}. ${cmd}`));
        });
      }
    } catch (error) {
      spinner.fail('Failed to get AI response');
      console.error(chalk.red('❌ Error:'), error.message);
    }
  });

program
  .command('explain')
  .description('📖 Explain command or code with AI')
  .argument('<input>', 'command or code to explain')
  .action(async (input) => {
    const spinner = ora('Generating explanation...').start();
    
    try {
      if (!authService.isAuthenticated()) {
        spinner.fail('Authentication required');
        console.log(chalk.yellow('Please run "kapi login" first'));
        return;
      }

      await terminalAIClient.initialize();
      const documentation = await terminalAIClient.generateDocumentation(input, {
        currentDirectory: process.cwd()
      });
      
      spinner.succeed('Explanation ready');
      console.log(chalk.blue(`\n📖 Explanation for: ${input}`));
      console.log(chalk.white(documentation));
    } catch (error) {
      spinner.fail('Failed to generate explanation');
      console.error(chalk.red('❌ Error:'), error.message);
    }
  });

program
  .command('translate')
  .description('🔄 Translate natural language to terminal commands')
  .argument('<description>', 'natural language description of what you want to do')
  .action(async (description) => {
    const spinner = ora('Translating to commands...').start();
    
    try {
      if (!authService.isAuthenticated()) {
        spinner.fail('Authentication required');
        console.log(chalk.yellow('Please run "kapi login" first'));
        return;
      }

      await terminalAIClient.initialize();
      const commands = await terminalAIClient.translateToCommand(description, {
        currentDirectory: process.cwd()
      });
      
      spinner.succeed('Translation complete');
      
      console.log(chalk.blue(`\n🔄 Request: ${description}`));
      console.log(chalk.green('💻 Suggested commands:'));
      
      commands.forEach((cmd, i) => {
        console.log(chalk.white(`  ${i + 1}. ${cmd}`));
      });
      
      if (commands.length > 0) {
        console.log(chalk.gray('\nReview commands carefully before running them.'));
      }
    } catch (error) {
      spinner.fail('Failed to translate');
      console.error(chalk.red('❌ Error:'), error.message);
    }
  });

program
  .command('analyze-project')
  .description('🔍 Analyze current project with AI')
  .option('-p, --path <path>', 'project path to analyze', process.cwd())
  .action(async (options) => {
    const spinner = ora('Analyzing project...').start();
    
    try {
      if (!authService.isAuthenticated()) {
        spinner.fail('Authentication required');
        console.log(chalk.yellow('Please run "kapi login" first'));
        return;
      }

      await terminalAIClient.initialize();
      const analysis = await terminalAIClient.analyzeProject(options.path);
      
      spinner.succeed('Project analysis complete');
      
      console.log(chalk.blue(`\n🔍 Project Analysis: ${options.path}`));
      console.log(chalk.white(analysis.explanation));
      
      if (analysis.suggestions && analysis.suggestions.length > 0) {
        console.log(chalk.green('\n💡 Suggestions:'));
        analysis.suggestions.forEach((suggestion, i) => {
          console.log(chalk.gray(`  ${i + 1}. ${suggestion}`));
        });
      }
      
      if (analysis.commands && analysis.commands.length > 0) {
        console.log(chalk.blue('\n💻 Recommended commands:'));
        analysis.commands.forEach((cmd, i) => {
          console.log(chalk.gray(`  ${i + 1}. ${cmd}`));
        });
      }
    } catch (error) {
      spinner.fail('Failed to analyze project');
      console.error(chalk.red('❌ Error:'), error.message);
    }
  });

/**
 * Global error handling
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ Unhandled Promise Rejection:'), reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ Uncaught Exception:'), error.message);
  process.exit(1);
});

/**
 * Start interactive session if no arguments provided, otherwise use legacy CLI mode
 */
async function startInteractiveSession() {
  try {
    const session = new InteractiveSession();
    await session.start();
  } catch (error) {
    console.error(chalk.red('❌ Session error:'), error.message);
    process.exit(1);
  }
}

if (!process.argv.slice(2).length) {
  // No arguments - start interactive session
  startInteractiveSession();
} else if (process.argv.includes('--help') || process.argv.includes('-h')) {
  // Show enhanced help that promotes interactive mode
  console.log(chalk.blue.bold('🤖 KAPI Interactive Terminal'));
  console.log(chalk.gray('AI-powered development assistant\n'));
  
  console.log(chalk.green.bold('🌟 Recommended: Interactive Mode'));
  console.log(chalk.white('  Just run ') + chalk.yellow('kapi') + chalk.white(' to start an interactive AI session'));
  console.log(chalk.gray('  ├─ Natural language commands (no syntax to remember)'));
  console.log(chalk.gray('  ├─ Persistent context and conversation history'));
  console.log(chalk.gray('  ├─ Project analysis and AI assistance'));
  console.log(chalk.gray('  └─ Rich terminal interface with suggestions\n'));
  
  console.log(chalk.cyan('💡 Interactive Examples:'));
  console.log(chalk.gray('  kapi> ') + chalk.yellow('analyze this project'));
  console.log(chalk.gray('  kapi> ') + chalk.yellow('find all TODO comments'));
  console.log(chalk.gray('  kapi> ') + chalk.yellow('how do I implement JWT auth?'));
  console.log(chalk.gray('  kapi> ') + chalk.yellow('help') + chalk.gray(' - show all commands'));
  console.log(chalk.gray('  kapi> ') + chalk.yellow('exit') + chalk.gray(' - end session\n'));
  
  console.log(chalk.yellow.bold('⚡ Legacy Command Mode'));
  console.log(chalk.gray('  Traditional CLI commands (for automation/scripting):\n'));
  
  // Show the original help
  program.parse(process.argv);
} else {
  // Arguments provided - use legacy command mode
  program.parse(process.argv);
}