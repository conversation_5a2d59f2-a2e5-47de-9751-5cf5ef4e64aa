#!/usr/bin/env node

// KAPI Terminal Client Entry Point
// This is the executable script that users will invoke with the 'kapi' command

import { dirname, join } from 'path';
import { fileURLToPath } from 'url';

// Get the directory of this script
const __dirname = dirname(fileURLToPath(import.meta.url));

// Import and run the main CLI module
const cliPath = join(__dirname, '..', 'src', 'cli.js');
import(cliPath).catch((error) => {
  console.error('Failed to start KAPI Terminal Client:', error.message);
  process.exit(1);
});