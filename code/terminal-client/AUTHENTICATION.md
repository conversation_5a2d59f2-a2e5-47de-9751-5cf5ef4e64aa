# KAPI Terminal Client - Authentication Setup

This document explains how to set up authentication for the KAPI Terminal Client with unified Clerk integration.

## 🚀 Quick Start (Development)

### 1. Copy Environment Configuration
```bash
cp .env.example .env
```

### 2. Enable Development Authentication
Edit `.env` file:
```bash
NODE_ENV=development
USE_DEVELOPMENT_AUTH=true
DEV_BYPASS_TOKEN=kapi_dev_unified_2024
KAPI_API_URL=http://localhost:3000
```

### 3. Start the Terminal Client
```bash
./bin/kapi status
./bin/kapi login
```

## 🔐 Authentication Modes

### Development Mode (Recommended for Development)
**Configuration:**
```bash
USE_DEVELOPMENT_AUTH=true
DEV_BYPASS_TOKEN=kapi_dev_unified_2024
```

**Features:**
- ✅ Instant authentication bypass
- ✅ Mock admin user with full permissions
- ✅ No browser required
- ✅ Consistent with backend development mode
- ✅ Persistent session across restarts

**Usage:**
```bash
$ ./bin/kapi login
✅ Already logged in as: <EMAIL>
   Role: ADMIN

$ ./bin/kapi status
🔐 Authentication: ✅ <NAME_EMAIL>
   User: <EMAIL>
   Role: ADMIN
   ID: dev_clerk_123
```

### Production Mode (Clerk OAuth)
**Configuration:**
```bash
USE_DEVELOPMENT_AUTH=false
CLERK_PUBLISHABLE_KEY=pk_test_...
KAPI_API_URL=https://api.kapihq.com
```

**Features:**
- ✅ Real Clerk authentication
- ✅ Browser-based OAuth flow
- ✅ Secure token storage in `.kapi/auth/`
- ✅ Role-based access control
- ✅ Token encryption and persistence

**Usage:**
```bash
$ ./bin/kapi login
🔐 KAPI Authentication
This will open your browser for secure authentication

🔐 Please authenticate in your browser:
   https://auth.kapihq.com/device
   Device code: ABCD-EFGH

⠙ Waiting for authentication...
✅ Authentication successful!

✅ Welcome, John Doe!
   Role: USER
   You can now access all KAPI features
```

## 🛡️ Security Features

### Token Storage
- **Location:** `.kapi/auth/token.json`
- **Encryption:** AES-256 with system-specific key
- **Permissions:** Owner-only (0600)
- **Auto-cleanup:** On logout or token expiration

### System-Specific Encryption
The authentication system uses system-specific information to encrypt stored tokens:
```typescript
// Encryption key based on system information
const systemInfo = `${process.platform}-${process.arch}-${process.env.HOME}`;
const encryptionKey = crypto.createHash('sha256').update(systemInfo).digest('hex');
```

### Token Validation
- JWT signature verification with backend
- Automatic token refresh when needed
- Secure backend validation on each API call

## 🔧 Backend Integration

### Required Backend Configuration
Your backend must support the unified authentication system:

```bash
# Backend .env
CLERK_SECRET_KEY=sk_test_...
CLERK_PUBLISHABLE_KEY=pk_test_...
DEV_BYPASS_TOKEN=kapi_dev_unified_2024
NODE_ENV=development
```

### API Endpoints Required
The terminal client expects these backend endpoints:

- `POST /api/auth/device/authorize` - Device authorization request
- `POST /api/auth/device/token` - Token polling endpoint  
- `GET /api/auth/me` - User profile retrieval
- All protected endpoints accept `Authorization: Bearer <token>`

### Development Bypass
Backend should implement development bypass for consistent experience:

```javascript
// Backend middleware
if (process.env.NODE_ENV === 'development' && 
    token === process.env.DEV_BYPASS_TOKEN) {
  req.user = {
    id: 'dev_user_123',
    email: '<EMAIL>',
    role: 'ADMIN',
    clerk_id: 'dev_clerk_123'
  };
  next();
}
```

## 🧪 Testing Authentication

### Test Development Mode
```bash
# 1. Enable development authentication
export USE_DEVELOPMENT_AUTH=true

# 2. Test login
./bin/kapi login

# 3. Check status
./bin/kapi status

# 4. Test AI features
./bin/kapi
kapi> analyze project
```

### Test Production Mode
```bash
# 1. Configure Clerk credentials
export USE_DEVELOPMENT_AUTH=false
export CLERK_PUBLISHABLE_KEY=pk_test_...

# 2. Clear existing session
rm -rf .kapi/auth/

# 3. Test browser login
./bin/kapi login

# 4. Verify token persistence
./bin/kapi status
```

## 🔍 Troubleshooting

### Common Issues

**1. "Authentication timeout - device code expired"**
```bash
# Solution: Try login again, complete authentication faster
./bin/kapi login
```

**2. "Failed to decrypt token data"**
```bash
# Solution: Clear corrupted token data
rm -rf .kapi/auth/
./bin/kapi login
```

**3. "Backend token validation failed"**
```bash
# Solution: Check backend is running and configured correctly
./bin/kapi status
```

**4. "Could not automatically open browser"**
```bash
# Solution: Manually open the provided URL in your browser
# The device code will be displayed in the terminal
```

### Debug Mode
Enable verbose logging:
```bash
export DEBUG=kapi:auth
./bin/kapi login
```

### Reset Authentication
```bash
# Clear all authentication data
rm -rf .kapi/auth/
./bin/kapi logout
./bin/kapi login
```

## 🔄 Migration from Legacy Auth

If you're upgrading from the old authentication system:

1. **Remove old tokens:**
   ```bash
   rm -rf .kapi/legacy_tokens/
   ```

2. **Update environment:**
   ```bash
   # Remove deprecated variables
   unset KAPI_USE_MOCK_AUTH
   unset MOCK_USER_TOKEN
   
   # Add new variables
   export USE_DEVELOPMENT_AUTH=true
   export DEV_BYPASS_TOKEN=kapi_dev_unified_2024
   ```

3. **Test new system:**
   ```bash
   ./bin/kapi login
   ./bin/kapi status
   ```

## 📚 Related Documentation

- [Unified Authentication Specification](../docs/02-products/01-core-features/06-admin-security/authentication-authorization-system.md)
- [Backend Authentication Setup](../backend/README.md#authentication)
- [IDE Authentication Integration](../ide/AUTHENTICATION.md)