# KAPI Production Deployment Guide

_Complete deployment guide for the 3-service KAPI architecture on `kapihq.com`_

## 🎯 Architecture Overview

| Service | Port | Type | Purpose | PM2 Name | Node Project |
|---------|------|------|---------|----------|-------------|
| **Backend** | 3000 | cluster (2 instances) | API + Admin + Auth | `kapi-backend` | `/backend` |
| **Frontend** | 3001 | fork | Next.js UI | `kapi-frontend` | `/backend/src/next` |
| **Voice Service** | 3005 | fork | Nova Sonic Audio | `nova-sonic-service` | `/services/nova-sonic-service` |

### Why 2 Backend Instances?
Your `kapi-backend` runs in **cluster mode** with 2 instances for:
- ✅ **Load balancing** across CPU cores
- ✅ **High availability** - if one crashes, the other continues  
- ✅ **Zero-downtime restarts** with `pm2 reload`
- ✅ **Better performance** under concurrent load

This is a **production best practice** - keep it as-is!

---

## 🚀 Quick Deployment (15 minutes)

### 1. Connect & Deploy Code
```bash
# Connect to production server
ssh <EMAIL>
cd /home/<USER>/kapi/kapi

# Deploy latest changes
git pull origin main

# Install dependencies for all 3 Node.js projects
cd backend && npm install                    # Backend deps
cd src/next && npm install && cd ../..              # Frontend deps  
cd ../services/nova-sonic-service && npm install    # Voice service deps
cd ../../backend                                 # Back to main backend

# Build all 3 projects
npm run build:all  # This builds: Prisma + Backend + Frontend
cd ../services/nova-sonic-service && npm run build  # Build voice service
cd ../nodejs_backend

# Restart all services
pm2 restart all
```

### 2. Verify Services Running
```bash
pm2 status
# Expected output:
# │ kapi-backend          │ cluster │ online │
# │ kapi-frontend         │ fork    │ online │  
# │ nova-sonic-service    │ fork    │ online │
```

### 3. Test Health Endpoints
```bash
curl https://kapihq.com/api/health
curl https://kapihq.com/api/admin/auth/clerk-config
curl https://kapihq.com/nova-sonic/health
```

---

## 🔐 Authentication System Setup

### Configure Clerk for Production

1. **Go to**: https://dashboard.clerk.com
2. **Select your production app**: `leading-monkfish-43`
3. **Domains** → Add:
   - `https://kapihq.com`
   - `https://www.kapihq.com`
4. **Paths** → Set:
   - **After sign-in URL**: `/admin/device/callback`
   - **After sign-up URL**: `/admin/device/callback`

### Update Environment Variables
```bash
# Edit production environment
nano /home/<USER>/kapi/kapi/nodejs_backend/.env
```

**Ensure these auth variables are set:**
```bash
# Clerk Configuration (Production)
CLERK_PUBLISHABLE_KEY=pk_live_your_production_key
CLERK_SECRET_KEY=sk_live_your_production_secret
CLERK_FRONTEND_API=https://leading-monkfish-43.clerk.accounts.dev
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret

# JWT & Auth
JWT_SECRET=your-super-secure-jwt-secret-min-32-chars
KAPI_API_URL=https://kapihq.com
NODE_ENV=production

# Test Users
ADMIN_EMAIL=<EMAIL>
```

### Test Authentication Flow
```bash
# Test device auth endpoint
curl -X POST https://kapihq.com/api/auth/device/authorize \
  -H "Content-Type: application/json" \
  -d '{"client_type":"terminal"}'

# Test device auth page
curl https://kapihq.com/admin/device?code=TEST-1234
```

---

## 🔧 Service Management Commands

### Status & Monitoring
```bash
# Check service status
pm2 status
pm2 monit

# View logs
pm2 logs --lines 50                    # All services
pm2 logs kapi-backend --lines 50       # Backend only
pm2 logs kapi-frontend --lines 20      # Frontend only
pm2 logs nova-sonic-service --lines 20 # Voice service
```

### Restart Services
```bash
# Restart all services
pm2 restart all

# Restart individual services
pm2 restart kapi-backend      # For API/auth changes
pm2 restart kapi-frontend     # For UI changes
pm2 restart nova-sonic-service # For voice changes

# Graceful restart (zero downtime)
pm2 reload kapi-backend
```

### Deploy Updates
```bash
# Standard deployment (all 3 projects)
cd /home/<USER>/kapi/kapi
git pull origin main

# Install & build all projects
cd nodejs_backend && npm install
cd src/next && npm install && cd ../..
cd ../services/nova-sonic-service && npm install && cd ../nodejs_backend
npm run build:all  # Prisma + Backend + Frontend
cd ../services/nova-sonic-service && npm run build && cd ../nodejs_backend
pm2 restart all

# Backend-only deployment
cd /home/<USER>/kapi/kapi/nodejs_backend
npm install && npm run build
pm2 restart kapi-backend

# Frontend-only deployment  
cd /home/<USER>/kapi/kapi/nodejs_backend/src/next
npm install && npm run build
cd ../../..
pm2 restart kapi-frontend

# Voice service-only deployment
cd /home/<USER>/kapi/kapi/services/nova-sonic-service  
npm install && npm run build
pm2 restart nova-sonic-service
```

---

## 👤 Admin User Management

### Create Admin User (Production)
```bash
# Connect to server
ssh <EMAIL>
cd /home/<USER>/kapi/kapi/nodejs_backend

# Method 1: Interactive admin user creation
npm run create-admin-user

# Method 2: Simple admin user creation (non-interactive)
npm run create-admin-user-simple

# Method 3: Manual database admin user
psql -h localhost -U kapiadmin -d kapi_prod -c "
UPDATE users SET role = 'ADMIN', is_admin = true 
WHERE email = '<EMAIL>';"
```

### Make Existing User Admin
```bash
# Connect to database directly
psql -h localhost -U kapiadmin -d kapi_prod

# Make user admin by email
UPDATE users SET role = 'ADMIN', is_admin = true 
WHERE email = '<EMAIL>';

# Or by Clerk ID (if you know it)  
UPDATE users SET role = 'ADMIN', is_admin = true
WHERE clerk_id = 'user_clerk_abc123';

# Verify admin status
SELECT id, email, role, is_admin, clerk_id FROM users WHERE role = 'ADMIN';
\q
```

### Admin Access URLs
After creating admin user:
- **Admin Panel**: https://kapihq.com/admin
- **Login**: Use the email you set as admin
- **Device Management**: https://kapihq.com/admin/device

---

## 🔍 Health Checks & Troubleshooting

### Service Health Verification
```bash
# Production endpoints
curl https://kapihq.com/api/health
curl https://kapihq.com/api/admin/auth/clerk-config
curl https://kapihq.com/nova-sonic/health

# Local service checks
curl localhost:3000/api/health
curl localhost:3001
curl localhost:3005/health

# Port status
sudo netstat -tlnp | grep -E ':3000|:3001|:3005'
```

### Common Issues & Fixes

| Issue | Check | Fix |
|-------|-------|-----|
| **503 Service Unavailable** | `pm2 status` | `pm2 restart all` |
| **Authentication Failing** | `curl /api/admin/auth/clerk-config` | Check Clerk env vars |
| **High Memory Usage** | `pm2 monit` | `pm2 restart <service>` |
| **SSL Certificate Issues** | `sudo nginx -t` | Check cert files |
| **Database Connection Error** | `sudo systemctl status postgresql` | Restart PostgreSQL |

### Debug Commands
```bash
# Search logs for errors
pm2 logs kapi-backend | grep -i "error\|auth\|clerk"

# Export logs for debugging
pm2 logs --lines 1000 > /tmp/kapi-logs-$(date +%Y%m%d-%H%M).txt

# System resource usage
htop
df -h
free -h
```

---

## 🚨 Emergency Recovery

### Full Service Recovery
```bash
# If services are completely down
pm2 delete all
cd /home/<USER>/kapi/kapi
pm2 start nodejs_backend/ecosystem.config.json --env production
pm2 save
```

### After System Reboot
```bash
# Services should auto-start via PM2 startup
# If not:
pm2 resurrect
# or manually start:
pm2 start nodejs_backend/ecosystem.config.json --env production
```

### Rollback to Previous Version
```bash
cd /home/<USER>/kapi/kapi
git log --oneline -5  # See recent commits
git reset --hard <previous-commit-hash>
npm run build:all
pm2 restart all
```

---

## 🎯 Success Verification Checklist

After deployment, verify these endpoints work:

- [ ] **Main Site**: https://kapihq.com ✅
- [ ] **Admin Panel**: https://kapihq.com/admin ✅
- [ ] **API Health**: https://kapihq.com/api/health returns `{"status":"ok"}` ✅
- [ ] **Auth Config**: https://kapihq.com/api/admin/auth/clerk-config returns Clerk config ✅
- [ ] **Device Auth Page**: https://kapihq.com/admin/device?code=TEST loads ✅
- [ ] **Voice Service**: https://kapihq.com/nova-sonic/health returns status ✅
- [ ] **PM2 Status**: All 3 services show "online" ✅

### Test Authentication Flow
1. Update terminal client to use `https://kapihq.com`
2. Run `login` command in terminal
3. Visit device auth URL in browser
4. Sign in with `<EMAIL>` or `<EMAIL>`
5. Verify terminal authentication completes

---

## 📞 Quick Reference Commands

### Daily Operations
```bash
# Check everything is healthy
pm2 status && curl -s https://kapihq.com/api/health

# View recent logs
pm2 logs --lines 50

# Restart after code changes
git pull && npm run build:all && pm2 restart all
```

### System Information
```bash
# Service details
pm2 jlist | jq '.[] | {name, status: .pm2_env.status, uptime: .pm2_env.pm_uptime}'

# Resource usage
pm2 monit

# Disk space
df -h
```

---

## 🎉 Production URLs

Your KAPI platform with unified authentication is deployed at:

- **🌐 Main Application**: https://kapihq.com
- **🔧 Admin Panel**: https://kapihq.com/admin
- **🔐 Device Authentication**: https://kapihq.com/admin/device
- **🎤 Voice Service**: https://kapihq.com/nova-sonic
- **📊 Health Monitoring**: https://kapihq.com/api/health

**Authentication**: Uses real Clerk with `<EMAIL>` (admin) and `<EMAIL>` (user) for testing.