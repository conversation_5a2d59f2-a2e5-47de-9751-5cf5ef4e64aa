# Unified Authentication & Authorization System

_Feature ID: CORE-AUTH-001_  
_Last updated: July 21, 2025_  
_Document type: Simplified Unified Authentication Specification_

## Test Users

For testing and development across all KAPI systems:

- **Admin User**: `<EMAIL>` - Full administrative privileges
- **Regular User**: `<EMAIL>` - Standard user access

These users are configured in Clerk and provide consistent testing across:
- Admin interface (`/admin/*`)
- Device login for IDE
- Terminal client authentication

## Overview

The KAPI Authentication System provides **unified Clerk-based authentication** across all platform clients with a simplified **2-tier role system**. This system ensures consistent login experiences across web admin, IDE, and terminal clients while maintaining security and development convenience.

## 🎯 Multi-Client Architecture

```mermaid
graph TB
    subgraph "KAPI Platform Clients"
        A[🌐 Web Admin Panel<br/>kapihq.com/admin] 
        B[💻 IDE Application<br/>Electron Desktop]
        C[⌨️ Terminal Client<br/>CLI Interface]
    end
    
    subgraph "Unified Authentication Backend"
        D[🔐 Clerk Authentication Service]
        E[🛡️ JWT Token Validation]
        F[📊 User Database Integration]
    end
    
    subgraph "Authentication Flow"
        G[User Login] --> H[Clerk OAuth/Email]
        H --> I[JWT Token Generation]
        I --> J[Role Assignment]
        J --> K[Client Access]
    end
    
    A --> D
    B --> D  
    C --> D
    D --> E
    E --> F
    
    style A fill:#e3f2fd
    style B fill:#e8f5e9
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

## 👤 Simplified Role System

### Two-Tier Role Hierarchy

| Role | Access Level | Permissions | Use Cases |
|------|-------------|-------------|-----------|
| **ADMIN** | Full System Access | System administration, user management, all features | Platform administrators, developers |
| **USER** | Standard Access | Personal projects, AI features, own data | Regular users, customers |

### Role-Based Access Control

| Resource | ADMIN | USER |
|----------|-------|------|
| **Admin Panel** | ✅ Full Access | ❌ No Access |
| **User Management** | ✅ All Users | ✅ Own Profile |
| **System Configuration** | ✅ Full Control | ❌ Read Only |
| **AI Features** | ✅ Unlimited | ✅ Standard |
| **Projects** | ✅ All Projects | ✅ Own Projects |
| **Analytics** | ✅ Global Analytics | ✅ Usage Stats |

## 🔐 Unified Device Authentication Flow

**All desktop clients (IDE and Terminal) use the same OAuth 2.0 device authorization grant flow for consistent user experience and security.**

### Authentication Flow Architecture

```mermaid
sequenceDiagram
    participant C as Client (IDE/Terminal)
    participant B as KAPI Backend
    participant U as User Browser
    participant CL as Clerk Service

    C->>B: POST /api/auth/device/authorize
    B->>B: Generate device_code + user_code
    B-->>C: {device_code, user_code, verification_uri}
    
    C->>C: Display user_code + open browser
    C->>U: Open: backend.com/admin/device?code={user_code}
    
    U->>CL: Complete Clerk authentication
    CL->>B: Validate user + generate JWT
    B->>B: Store device completion
    
    loop Polling (every 5s)
        C->>B: POST /api/auth/device/token
        B-->>C: {pending} or {access_token}
    end
    
    C->>C: Store encrypted token locally
```

### 1. **🌐 Web Admin Panel** (`kapihq.com/admin`)
```typescript
// Clerk Web Integration - Direct browser authentication
import { ClerkProvider, SignIn, useUser } from '@clerk/nextjs'

// Admin-only access enforcement
const AdminGuard = ({ children }) => {
  const { user } = useUser()
  if (user?.publicMetadata?.role !== 'ADMIN') {
    return <AccessDenied />
  }
  return children
}
```

### 2. **💻 IDE Application** (Electron Desktop)
```typescript
// Device Authorization Flow (same as Terminal)
class IDEAuthService {
  async login(): Promise<void> {
    // 1. Request device code from backend
    const { device_code, user_code, verification_uri } = 
      await this.requestDeviceCode()
    
    // 2. Display user code and open browser
    this.showAuthModal(user_code)
    shell.openExternal(`${verification_uri}?code=${user_code}`)
    
    // 3. Poll for completion
    const token = await this.pollForToken(device_code)
    
    // 4. Store encrypted token
    await this.storeToken(token)
  }
}
```

### 3. **⌨️ Terminal Client** (CLI Interface)
```typescript
// Device Authorization Flow (same as IDE)
class TerminalAuthService {
  async login(): Promise<void> {
    // 1. Request device code from backend
    const { device_code, user_code, verification_uri } = 
      await this.requestDeviceCode()
    
    // 2. Display instructions and open browser
    console.log(`Visit: ${verification_uri}`)
    console.log(`Code: ${user_code}`)
    open(verification_uri)
    
    // 3. Poll for completion
    const token = await this.pollForToken(device_code)
    
    // 4. Store encrypted token in .kapi/auth/
    await this.storeEncryptedToken(token)
  }
}
```

## 🛡️ Unified Backend Authentication

### JWT Token Structure
```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user_123",
    "clerk_id": "user_clerk_abc",
    "role": "ADMIN | USER",
    "email": "<EMAIL>",
    "exp": **********,
    "iss": "https://your-app.clerk.accounts.dev"
  }
}
```

### Backend Integration Points

| Component | Authentication Method | Token Source |
|-----------|----------------------|--------------|
| **Express API** | JWT Bearer Header | `Authorization: Bearer <token>` |
| **WebSocket** | Query Parameter | `?token=<jwt_token>` |
| **Database** | User ID from JWT | `users.clerk_id = jwt.sub` |
| **Admin Routes** | Role Validation | `jwt.role === 'ADMIN'` |

## 🔄 Implementation Status & Migration Plan

### Current State Analysis

| Component | Current Implementation | Target State | Gap |
|-----------|----------------------|--------------|-----|
| **Terminal Client** | ✅ Device auth + development bypass | ✅ Device auth only | Remove `kapi_dev_unified_2024` bypass |
| **IDE Client** | ⚠️ Mock auth + development bypass | ✅ Device auth flow | Implement device flow, remove `kapi_dev_ide_token_2024` |
| **Backend** | ✅ Unified auth middleware | ✅ Remove dev bypasses | Remove development token validation |
| **Web Admin** | ⚠️ Independent auth | ✅ Clerk integration | Integrate with unified system |

### Environment Configuration

| Environment | Auth Method | Session Duration | Test Users |
|-------------|-------------|------------------|------------|
| **Production** | Device Flow + Clerk JWT | 24 hours | Live Users |
| **Development** | Device Flow + Clerk JWT | 24 hours | <EMAIL>, <EMAIL> |
| **Testing** | Device Flow + Clerk JWT | 1 hour | <EMAIL>, <EMAIL> |

### Role Assignment Logic
```typescript
// Email-based role assignment for test users
const assignUserRole = (email: string): 'ADMIN' | 'USER' => {
  if (email.endsWith('@kapihq.com')) {
    return 'ADMIN';
  }
  return 'USER'; // Default role for all other users
}

// Backend middleware for role validation
const validateUserRole = async (clerkUserId: string): Promise<UserRole> => {
  const clerkUser = await clerk.users.getUser(clerkUserId);
  const email = clerkUser.emailAddresses[0]?.emailAddress;
  return assignUserRole(email);
}
```

## 🚀 Migration Implementation Plan

### Phase 1: Backend Development Bypass Removal ⚡
**Priority**: IMMEDIATE - Remove all development authentication workarounds

**Current Implementation Gap**: Both terminal and IDE clients still use development bypass tokens (`kapi_dev_unified_2024`, `kapi_dev_ide_token_2024`) that bypass real Clerk authentication.

**Implementation Steps**:
1. **Remove Development Token Support**: Remove validation logic for development bypass tokens
2. **Enforce Real Clerk JWT**: Require valid Clerk JWT tokens in all environments  
3. **Update Device Auth Flow**: Ensure device completion uses real Clerk OAuth (not mock)
4. **Configure Test User Access**: Ensure `<EMAIL>` and `<EMAIL>` work in development

**Critical Files to Modify**:
- `backend/src/middleware/unified-auth.ts:45-67` - Remove development token validation
- `backend/src/api/auth/controllers/auth.controller.ts:198-220` - Ensure real Clerk validation
- `backend/src/views/admin/device-auth.html` - Complete Clerk integration

### Phase 2: IDE Device Authentication Migration 🔄
**Priority**: HIGH - Standardize IDE to use device auth flow (same as terminal)

**Current Implementation Gap**: IDE uses mock authentication instead of the OAuth device flow that terminal client uses.

**Implementation Steps**:
1. **Implement Device Flow in IDE**: Replace mock auth with device authorization grant
2. **Reuse Terminal Auth Logic**: Adapt `ClerkAuthService.js` device flow for Electron
3. **Update UI Components**: Create device auth modal/screens for IDE
4. **Token Storage Migration**: Move from localStorage to encrypted file storage
5. **Remove Mock Authentication**: Disable `kapi_dev_ide_token_2024` completely

**Critical Files to Modify**:
- `ide/src/renderer/services/ClerkAuthService.ts` - Implement device flow
- `ide/src/renderer/contexts/AuthContext.tsx` - Update auth state management
- `ide/src/renderer/pages/LoginScreen.tsx` - Add device auth UI
- `ide/src/config.ts` - Remove `useMockAuth` configuration

### Phase 3: Web Admin Unified Integration 🌐
**Priority**: MEDIUM - Complete unified authentication across all clients

**Implementation Steps**:
1. **Clerk Web SDK Integration**: Full `@clerk/nextjs` implementation
2. **Role-Based Access Control**: Restrict admin panel to ADMIN role only
3. **Consistent Branding**: Match login UX across all clients
4. **Cross-Client Session State**: Consider shared authentication state

### Phase 4: Production Hardening 🛡️
**Priority**: MEDIUM - Security and reliability improvements

**Implementation Steps**:
1. **Token Refresh Logic**: Implement automatic token refresh before expiration
2. **Enhanced Encryption**: Upgrade token storage encryption methods
3. **Audit Logging**: Add authentication event logging
4. **Rate Limiting**: Implement device flow rate limiting

## 🔧 Technical Requirements

### Backend Dependencies (Already Implemented ✅)
```json
{
  "clerk": "Clerk user management API",
  "jsonwebtoken": "JWT token validation",
  "jwk-to-pem": "JWKS key conversion",
  "prisma": "Database user management"
}
```

### Frontend Dependencies (To Add)
```json
{
  "@clerk/clerk-js": "Web authentication",
  "@clerk/nextjs": "Next.js integration", 
  "@clerk/electron": "Electron integration"
}
```

### Environment Variables
```bash
# Required for all clients
CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
NODE_ENV=development

# DEPRECATED - To be removed in Phase 1
# DEV_BYPASS_TOKEN=kapi_dev_unified_2024  # ❌ Remove
# USE_DEVELOPMENT_AUTH=true               # ❌ Remove
```

## 📊 Success Metrics

| Metric | Target | Measurement |
|--------|---------|-------------|
| **Single Sign-On** | 100% | Users can access all clients with one login |
| **Development Speed** | <2 min setup | Time to authenticate in development |
| **Token Validation** | <10ms | Backend JWT validation time |
| **Cross-Client UX** | Consistent | Same login flow across all clients |

---

## 🎯 Implementation Priority Summary

**IMMEDIATE (Phase 1)**: Remove development bypasses - both clients have working Clerk integration but still use development tokens that bypass real authentication.

**HIGH (Phase 2)**: Migrate IDE from mock auth to device flow - standardize authentication experience across desktop clients.

**Key Files Requiring Changes**:
- `backend/src/middleware/unified-auth.ts` - Remove development token validation  
- `ide/src/renderer/services/ClerkAuthService.ts` - Implement device authorization flow
- Terminal client already implements device auth correctly, just remove bypass tokens

**Next Action**: Begin Phase 1 - remove development authentication bypasses across backend and clients.