# Unified Authentication & Authorization System

_Feature ID: CORE-AUTH-001_  
_Last updated: July 21, 2025_  
_Document type: Simplified Unified Authentication Specification_

## Test Users

For testing and development across all KAPI systems:

- **Admin User**: `<EMAIL>` - Full administrative privileges
- **Regular User**: `<EMAIL>` - Standard user access

These users are configured in Clerk and provide consistent testing across:
- Admin interface (`/admin/*`)
- Device login for IDE
- Terminal client authentication

## Overview

The KAPI Authentication System provides **unified Clerk-based authentication** across all platform clients with a simplified **2-tier role system**. This system ensures consistent login experiences across web admin, IDE, and terminal clients while maintaining security and development convenience.

## 🎯 Multi-Client Architecture

```mermaid
graph TB
    subgraph "KAPI Platform Clients"
        A[🌐 Web Admin Panel<br/>kapihq.com/admin] 
        B[💻 IDE Application<br/>Electron Desktop]
        C[⌨️ Terminal Client<br/>CLI Interface]
    end
    
    subgraph "Unified Authentication Backend"
        D[🔐 Clerk Authentication Service]
        E[🛡️ JWT Token Validation]
        F[📊 User Database Integration]
    end
    
    subgraph "Authentication Flow"
        G[User Login] --> H[Clerk OAuth/Email]
        H --> I[JWT Token Generation]
        I --> J[Role Assignment]
        J --> K[Client Access]
    end
    
    A --> D
    B --> D  
    C --> D
    D --> E
    E --> F
    
    style A fill:#e3f2fd
    style B fill:#e8f5e9
    style C fill:#fff3e0
    style D fill:#f3e5f5
```

## 👤 Simplified Role System

### Two-Tier Role Hierarchy

| Role | Access Level | Permissions | Use Cases |
|------|-------------|-------------|-----------|
| **ADMIN** | Full System Access | System administration, user management, all features | Platform administrators, developers |
| **USER** | Standard Access | Personal projects, AI features, own data | Regular users, customers |

### Role-Based Access Control

| Resource | ADMIN | USER |
|----------|-------|------|
| **Admin Panel** | ✅ Full Access | ❌ No Access |
| **User Management** | ✅ All Users | ✅ Own Profile |
| **System Configuration** | ✅ Full Control | ❌ Read Only |
| **AI Features** | ✅ Unlimited | ✅ Standard |
| **Projects** | ✅ All Projects | ✅ Own Projects |
| **Analytics** | ✅ Global Analytics | ✅ Usage Stats |

## 🔐 Client-Specific Authentication

### 1. **🌐 Web Admin Panel** (`kapihq.com/admin`)
```typescript
// Clerk Web Integration
import { ClerkProvider, SignIn, useUser } from '@clerk/nextjs'

// Admin-only access enforcement
const AdminGuard = ({ children }) => {
  const { user } = useUser()
  if (user?.publicMetadata?.role !== 'ADMIN') {
    return <AccessDenied />
  }
  return children
}
```

### 2. **💻 IDE Application** (Electron Desktop)
```typescript
// Clerk Electron Integration  
import { ClerkProvider } from '@clerk/clerk-react'

// Development bypass for testing
const authConfig = {
  clerkPublishableKey: process.env.CLERK_PUBLISHABLE_KEY,
  developmentBypass: process.env.NODE_ENV === 'development',
  devToken: process.env.DEV_IDE_TOKEN // kapi_dev_unified_2024
}
```

### 3. **⌨️ Terminal Client** (CLI Interface)
```typescript
// Device-based authentication flow
class TerminalAuthService {
  async login(): Promise<void> {
    // 1. Open browser for Clerk OAuth
    // 2. Receive callback with JWT token
    // 3. Store token locally (.kapi/auth)  
    // 4. Validate with backend
  }
  
  async getToken(): Promise<string> {
    return fs.readFileSync('.kapi/token', 'utf8')
  }
}
```

## 🛡️ Unified Backend Authentication

### JWT Token Structure
```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT"
  },
  "payload": {
    "sub": "user_123",
    "clerk_id": "user_clerk_abc",
    "role": "ADMIN | USER",
    "email": "<EMAIL>",
    "exp": **********,
    "iss": "https://your-app.clerk.accounts.dev"
  }
}
```

### Backend Integration Points

| Component | Authentication Method | Token Source |
|-----------|----------------------|--------------|
| **Express API** | JWT Bearer Header | `Authorization: Bearer <token>` |
| **WebSocket** | Query Parameter | `?token=<jwt_token>` |
| **Database** | User ID from JWT | `users.clerk_id = jwt.sub` |
| **Admin Routes** | Role Validation | `jwt.role === 'ADMIN'` |

## 🔄 Development vs Production

### Environment Configuration

| Environment | Auth Method | Session Duration | Test Users |
|-------------|-------------|------------------|------------|
| **Production** | Full Clerk + JWT | 24 hours | Live Users |
| **Development** | Full Clerk + JWT | 24 hours | <EMAIL>, <EMAIL> |
| **Testing** | Full Clerk + JWT | 1 hour | <EMAIL>, <EMAIL> |

### Role Assignment Logic
```typescript
// Email-based role assignment for test users
const assignUserRole = (email: string): 'ADMIN' | 'USER' => {
  if (email.endsWith('@kapihq.com')) {
    return 'ADMIN';
  }
  return 'USER'; // Default role for all other users
}

// Backend middleware for role validation
const validateUserRole = async (clerkUserId: string): Promise<UserRole> => {
  const clerkUser = await clerk.users.getUser(clerkUserId);
  const email = clerkUser.emailAddresses[0]?.emailAddress;
  return assignUserRole(email);
}
```

## 🚀 Implementation Phases

### Phase 1: Remove Development Authentication Workarounds ⚡
**Priority**: Replace all development bypasses with real Clerk authentication

**Implementation Steps**:
1. **Remove Development Tokens**: Remove `kapi_dev_unified_2024` and similar bypasses
2. **Update Unified Auth Middleware**: Remove development token validation logic
3. **Implement Real Clerk JWT**: Add full Clerk JWT validation in all environments
4. **Configure Role Assignment**: Implement email-based role assignment (@kapihq.com = ADMIN)
5. **Update Device Auth Flow**: Replace mock completion with real Clerk OAuth

**Files to Modify**:
- `/Users/<USER>/Code/KAPI/code/backend/src/middleware/unified-auth.ts`
- `/Users/<USER>/Code/KAPI/code/backend/src/views/admin/device-auth.html`
- `/Users/<USER>/Code/KAPI/code/backend/src/api/auth/controllers/auth.controller.ts`

### Phase 2: IDE Authentication Cleanup 🧹
**Priority**: Standardize IDE authentication to use real Clerk

**Implementation Steps**:
1. **Remove Mock Auth**: Disable development bypass tokens
2. **Clerk Integration**: Enable real Clerk authentication
3. **Consistent Tokens**: Use same token format as backend
4. **Role Enforcement**: Implement admin/user role checks

### Phase 3: Web Admin Integration 🌐
**Priority**: Migrate admin panel to unified Clerk system

**Implementation Steps**:
1. **Clerk Web SDK**: Integrate `@clerk/nextjs`
2. **Admin Role Guard**: Restrict access to ADMIN role only
3. **Consistent UX**: Match authentication flow across clients
4. **Session Sharing**: Consider shared authentication state

## 🔧 Technical Requirements

### Backend Dependencies (Already Implemented ✅)
```json
{
  "clerk": "Clerk user management API",
  "jsonwebtoken": "JWT token validation",
  "jwk-to-pem": "JWKS key conversion",
  "prisma": "Database user management"
}
```

### Frontend Dependencies (To Add)
```json
{
  "@clerk/clerk-js": "Web authentication",
  "@clerk/nextjs": "Next.js integration", 
  "@clerk/electron": "Electron integration"
}
```

### Environment Variables
```bash
# Required for all clients
CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...

# Development only
NODE_ENV=development
DEV_BYPASS_TOKEN=kapi_dev_unified_2024
USE_DEVELOPMENT_AUTH=true
```

## 📊 Success Metrics

| Metric | Target | Measurement |
|--------|---------|-------------|
| **Single Sign-On** | 100% | Users can access all clients with one login |
| **Development Speed** | <2 min setup | Time to authenticate in development |
| **Token Validation** | <10ms | Backend JWT validation time |
| **Cross-Client UX** | Consistent | Same login flow across all clients |

---

**Next Action**: Begin Phase 1 implementation with terminal client Clerk authentication integration.