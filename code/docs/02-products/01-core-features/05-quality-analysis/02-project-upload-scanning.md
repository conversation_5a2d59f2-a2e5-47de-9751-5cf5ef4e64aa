# 📤 Project Upload & Scanning

_Core functionality for project ingestion and initial analysis_

**Feature ID**: CORE-SCAN-001  
**Last updated**: July 18, 2025  
**Status**: ✅ Built  
**Dependencies**: File system access, Security scanning

## Overview

Enables developers to upload their abandoned AI projects through folder selection for comprehensive analysis. This is the entry point to the KAPI brutal honesty experience.

## User Story

As a developer with an abandoned AI project, I want to easily upload my project folder so that KAPI can analyze its current state and help me ship it.

## Functional Requirements

### FR-001: File Upload Interface
- Support folder selection through file browser
- Show upload progress indicator  
- Display warning message: "We'll tell you the truth about your code. Ready for that?"
- Integration with existing onboarding flow

### FR-002: Project Validation
- Verify project contains code files
- Check for package.json, requirements.txt, or other project indicators
- Reject non-code uploads (images only, etc.)
- Support JavaScript, TypeScript, Python, and React projects

### FR-003: Initial Scan
- Parse project structure
- Identify main technologies used
- Count files and lines of code
- Detect git repository if present
- Create initial project fingerprint

## Technical Specifications

### Input
- Project folder (up to 100MB)
- Supported file types: .js, .jsx, .ts, .tsx, .py, .html, .css, .json, .md

### Processing Flow

```mermaid
graph TB
    subgraph "Upload Process"
        A[Select Project Folder] --> B[Validate Project Structure]
        B --> C[Security Scan]
        C --> D[Parse File Structure]
        D --> E[Generate Project Metadata]
        E --> F[Queue for Analysis]
    end
    
    subgraph "Validation Checks"
        B --> G[Check File Types]
        B --> H[Verify Project Indicators]
        B --> I[Size Limit Check]
    end
    
    subgraph "Security Scanning"
        C --> J[Virus Scan]
        C --> K[Malware Detection]
        C --> L[Content Validation]
    end
    
    subgraph "Structure Analysis"
        D --> M[Directory Mapping]
        D --> N[Dependency Detection]
        D --> O[Framework Identification]
    end
    
    F --> P[Ready for Brutal Honesty Analysis]
    
    style A fill:#e3f2fd
    style P fill:#66bb6a
    style C fill:#ffebee
```

### Output
- Project ID for tracking
- Initial project summary
- Technology stack identification
- File structure overview
- Readiness to proceed to brutal honesty analysis

## Processing Pipeline

### 1. Upload to Temporary Storage
```typescript
interface ProjectUpload {
  projectId: string;
  originalPath: string;
  tempStoragePath: string;
  uploadTimestamp: Date;
  sizeBytes: number;
  fileCount: number;
}
```

### 2. Security Scanning
```typescript
interface SecurityScanResult {
  isClean: boolean;
  threats: SecurityThreat[];
  scanTimestamp: Date;
  confidence: number;
}

interface SecurityThreat {
  type: 'virus' | 'malware' | 'suspicious_content';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  filePath: string;
}
```

### 3. Project Structure Analysis
```typescript
interface ProjectStructure {
  rootPath: string;
  directories: DirectoryInfo[];
  files: FileInfo[];
  technologies: TechnologyStack;
  projectType: 'frontend' | 'backend' | 'fullstack' | 'library';
}

interface TechnologyStack {
  primary: string; // 'react', 'node', 'python', etc.
  framework: string[];
  buildTool: string[];
  packageManager: string;
  languages: LanguageDistribution;
}
```

### 4. File Analysis
```typescript
interface FileInfo {
  path: string;
  size: number;
  extension: string;
  mimeType: string;
  lineCount?: number;
  tokenCount?: number;
  language?: string;
  isGenerated?: boolean;
  lastModified: Date;
}
```

## Error Handling

### Network & Upload Errors
```
┌─────────────────────────────────────────┐
│  ⚠️  Upload Issue Detected               │
├─────────────────────────────────────────┤
│                                         │
│  It looks like your connection hiccupped │
│  during upload. No worries!             │
│                                         │
│  📊 Progress: 67% complete              │
│  🔄 Resuming from where we left off...  │
│                                         │
│  [Resume Upload] [Start Over]           │
│                                         │
└─────────────────────────────────────────┘
```

### Invalid Project Errors
```
┌─────────────────────────────────────────┐
│  🤔 This doesn't look like a code project│
├─────────────────────────────────────────┤
│                                         │
│  I found mostly images and documents,   │
│  but no actual code files.              │
│                                         │
│  Looking for:                           │
│  • .js, .ts, .py files                 │
│  • package.json or requirements.txt    │
│  • src/ or lib/ directories            │
│                                         │
│  Try uploading your actual project      │
│  folder instead of a design folder.     │
│                                         │
│  [Try Different Folder] [Learn More]    │
│                                         │
└─────────────────────────────────────────┘
```

### Size Limit Exceeded
```
┌─────────────────────────────────────────┐
│  📦 Project is a bit too chunky (150MB) │
├─────────────────────────────────────────┤
│                                         │
│  Your project is larger than our 100MB  │
│  limit. Let's slim it down:             │
│                                         │
│  🗂️  Large directories I can exclude:   │
│  • node_modules/ (45MB) ✅              │
│  • .git/ (23MB) ✅                      │
│  • dist/ (12MB) ✅                      │
│  • coverage/ (8MB) ✅                   │
│                                         │
│  New size: 62MB ✅                      │
│                                         │
│  [Upload Without These Folders]         │
│                                         │
└─────────────────────────────────────────┘
```

## Security Considerations

### Scan for Malware
- Never execute uploaded code
- Scan all files for known malware signatures
- Check for suspicious patterns in code
- Validate all file types and contents

### Isolated Processing
- Store in isolated sandbox environment
- No network access during analysis
- Separate processing containers
- Auto-delete after 30 days

### Data Protection
```typescript
interface SecurityPolicy {
  isolation: {
    sandboxed: true;
    networkIsolated: true;
    fileSystemIsolated: true;
  };
  retention: {
    maxDays: 30;
    autoDelete: true;
    userControlled: true;
  };
  scanning: {
    virusScan: true;
    malwareDetection: true;
    contentValidation: true;
  };
}
```

## Success Metrics

- **Upload completion rate**: >95%
- **Time to upload**: <30 seconds for 50MB project
- **User proceeds to analysis**: >80%
- **Security incidents**: 0 (malware uploads blocked)
- **False positives**: <2% (legitimate projects blocked)

## Integration Points

### With Onboarding Flow
```typescript
// ProjectOnboarding.tsx integration
const handleFolderSelection = async (folderPath: string) => {
  setCurrentStage('analysis');
  
  // Upload and scan project
  const uploadResult = await uploadProject(folderPath);
  
  if (uploadResult.success) {
    // Proceed to brutal honesty analysis
    const analysisResult = await analyzeProject(uploadResult.projectId);
    setCurrentStage('brutal-honesty-reality-check');
  }
};
```

### With Analysis Engine
```typescript
// Queue project for brutal honesty analysis
interface AnalysisQueueItem {
  projectId: string;
  userId: string;
  uploadResult: ProjectUpload;
  structureAnalysis: ProjectStructure;
  priority: 'high' | 'normal' | 'low';
  queuedAt: Date;
}
```

### With .kapi Storage
```typescript
// Initial project metadata stored in .kapi folder
interface InitialProjectReport {
  uploadTimestamp: string;
  projectStructure: ProjectStructure;
  technologyStack: TechnologyStack;
  securityScanResult: SecurityScanResult;
  initialMetrics: {
    fileCount: number;
    lineCount: number;
    estimatedComplexity: 'low' | 'medium' | 'high';
  };
}
```

## File Structure Analysis

### Directory Patterns
```typescript
const COMMON_PATTERNS = {
  frontend: ['src/', 'public/', 'components/', 'pages/'],
  backend: ['src/', 'lib/', 'routes/', 'controllers/'],
  fullstack: ['client/', 'server/', 'frontend/', 'backend/'],
  library: ['src/', 'lib/', 'dist/', 'build/']
};

const EXCLUDE_PATTERNS = [
  'node_modules/',
  '.git/',
  'dist/',
  'build/',
  'coverage/',
  '.next/',
  '.nuxt/',
  'vendor/',
  '__pycache__/',
  '.pytest_cache/'
];
```

### Technology Detection
```typescript
interface TechnologyDetector {
  detectByPackageJson(packageJson: any): TechnologyStack;
  detectByFileExtensions(files: FileInfo[]): LanguageDistribution;
  detectByDirectoryStructure(dirs: string[]): ProjectType;
  detectFrameworks(files: FileInfo[]): string[];
}
```

## Performance Optimizations

### Parallel Processing
```mermaid
graph LR
    subgraph "Parallel Upload Processing"
        A[Upload Stream] --> B[File Validation]
        A --> C[Security Scanning]
        A --> D[Structure Analysis]
        
        B --> E[Results Aggregator]
        C --> E
        D --> E
        
        E --> F[Ready for Analysis]
    end
    
    style A fill:#e3f2fd
    style E fill:#c5e1a5
    style F fill:#66bb6a
```

### Streaming Analysis
- Process files as they upload
- Early detection of issues
- Progressive structure building
- Immediate feedback to user

## Related Features

- [Brutal Honesty Analysis](./01-brutal-honesty-analysis.md) - What happens after upload
- [Health Monitoring System](./03-health-monitoring-system.md) - Advanced analysis capabilities
- [Implementation Details](./implementation/) - Technical implementation specifics

---

**Next**: Proceed to [Health Monitoring System](./03-health-monitoring-system.md) for advanced analytics capabilities