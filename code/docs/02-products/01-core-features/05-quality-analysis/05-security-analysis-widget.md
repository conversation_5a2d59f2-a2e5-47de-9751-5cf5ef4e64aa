# 🛡️ Security Analysis Widget

**Feature ID**: WIDGET-SECURITY-001  
**Category**: Quality Analysis  
**Status**: ✅ Implemented  
**Priority**: Critical

## Overview

Real-time security monitoring with actionable fixes for common vulnerabilities. This widget provides honest assessment of security posture with immediate fix recommendations.

## Visual Design

```
┌─────────────────────────────────────────┐
│  🛡️ Security Analysis                   │
├─────────────────────────────────────────┤
│                                         │
│  Security Score: 12/100 🚨              │
│  ▓▓▓░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░ │
│                                         │
│  🚨 Critical Issues (Fix Now!):         │
│  ├─ 🔑 API keys in code (3 found)       │
│  │   config.js:12, auth.js:34          │
│  ├─ 💉 SQL injection risk (2 queries)   │
│  │   userService.js:fetchUser()        │
│  └─ 🔓 No input validation (5 endpoints)│
│                                         │
│  ⚠️ High Priority:                      │
│  ├─ Outdated dependencies (18)          │
│  ├─ Missing CORS configuration          │
│  └─ Weak password requirements          │
│                                         │
│  ✅ What's Actually Secure:             │
│  • HTTPS enforced                       │
│  • No eval() usage detected             │
│                                         │
│  🎯 One-Click Fixes Available: 7        │
│                                         │
│  [🔧 Fix All] [🔍 Scan Again] [📊 Report]│
└─────────────────────────────────────────┘
```

## Security Tool Integration

### Tool Pipeline
```mermaid
graph LR
    subgraph "Security Scanners"
        A[npm audit] --> D[LLM Analysis]
        B[snyk scan] --> D
        C[semgrep] --> D
        E[bandit] --> D
    end
    
    subgraph "Analysis Engine"
        D --> F[Risk Assessment]
        F --> G[Priority Ranking]
        G --> H[Fix Generation]
    end
    
    subgraph "Output"
        H --> I[Security Score]
        H --> J[Fix Recommendations]
        H --> K[One-Click Fixes]
    end
    
    style D fill:#e1bee7
    style I fill:#ffebee
    style K fill:#c8e6c9
```

### npm audit Integration
```yaml
npm_audit:
  command: "npm audit --json"
  parsing:
    - Extract vulnerabilities by severity
    - Group by package and vulnerability type
    - Calculate CVSS scores
    - LLM interprets impact in plain English

llm_analysis:
  prompt: |
    Analyze this npm audit report and provide:
    1. Security score (0-100)
    2. Critical issues in order of priority
    3. One-sentence explanation for each issue
    4. Suggested fixes with time estimates
    
    Report: {audit_json}
```

### Static Analysis Tools
```yaml
semgrep_rules:
  - "security.javascript.lang.hardcoded-credentials"
  - "security.javascript.express.security.audit.express-session-cookie-settings"
  - "security.javascript.express.security.audit.express-cookie-session-no-httponly"
  - "security.javascript.lang.eval-use"

snyk_integration:
  - Code vulnerability scanning
  - Dependency vulnerability detection
  - License compliance checking
  - Custom rule sets for common patterns

bandit_python:
  - Hardcoded password detection
  - SQL injection patterns
  - Shell injection risks
  - Crypto implementation issues
```

## Vulnerability Classification

### Severity Levels
```typescript
interface SecurityVulnerability {
  id: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  category: 'injection' | 'auth' | 'crypto' | 'config' | 'dependency';
  title: string;
  description: string;
  location: {
    file: string;
    line?: number;
    function?: string;
  };
  impact: string;
  fixable: boolean;
  autoFixAvailable: boolean;
  timeToFix: string;
  brutalMessage: string;
}
```

### Sample Brutal Security Messages

**Critical Issues**:
- "Your API keys are visible from space 🛰️"
- "SQL injection? Welcome to 1998!"
- "Your auth is like a screen door on a submarine"

**High Priority**:
- "18 outdated packages with known exploits"
- "CORS is more open than a 24/7 diner"
- "Password validation: 'password123' passes ✅"

**Medium Issues**:
- "Missing security headers - hackers appreciate the welcome mat"
- "Cookie settings would make a privacy lawyer cry"
- "Rate limiting? Never heard of it"

## One-Click Fix System

### Auto-Fixable Issues
```typescript
interface SecurityFix {
  vulnerabilityId: string;
  fixType: 'environment' | 'dependency' | 'code' | 'config';
  autoApplicable: boolean;
  changes: FileChange[];
  validation: ValidationStep[];
  rollbackPossible: boolean;
}

// Example: API Key Exposure Fix
const apiKeyFix: SecurityFix = {
  vulnerabilityId: 'hardcoded-api-key',
  fixType: 'environment',
  autoApplicable: true,
  changes: [
    {
      file: 'config.js',
      action: 'replace',
      oldContent: 'const API_KEY = "sk-abc123"',
      newContent: 'const API_KEY = process.env.OPENAI_API_KEY'
    },
    {
      file: '.env.example',
      action: 'create',
      content: 'OPENAI_API_KEY=your_api_key_here'
    },
    {
      file: '.gitignore',
      action: 'append',
      content: '.env'
    }
  ],
  validation: [
    'Check .env file exists',
    'Verify API_KEY environment variable is set',
    'Test API functionality'
  ],
  rollbackPossible: true
};
```

### Fix Application Flow
```mermaid
sequenceDiagram
    participant User
    participant Widget
    participant FixEngine
    participant FileSystem
    
    User->>Widget: Click "🔧 Fix All"
    Widget->>FixEngine: Apply security fixes
    FixEngine->>FileSystem: Backup current files
    FixEngine->>FileSystem: Apply changes
    FixEngine->>FixEngine: Run validation
    FixEngine-->>Widget: Report success/failure
    Widget-->>User: Show results + new security score
```

## Real-Time Monitoring

### File Watching
```typescript
class SecurityMonitor {
  private fileWatcher: FileWatcher;
  private scanQueue: ScanQueue;

  startMonitoring(projectPath: string): void {
    this.fileWatcher = new FileWatcher(projectPath);
    
    // Monitor for security-relevant changes
    this.fileWatcher.watch([
      '**/*.js', '**/*.ts', '**/*.py',
      'package.json', 'requirements.txt',
      '.env*', 'config/**/*'
    ]);

    this.fileWatcher.on('change', (filePath) => {
      this.queueSecurityScan(filePath);
    });
  }

  private async queueSecurityScan(filePath: string): Promise<void> {
    // Debounce rapid changes
    this.scanQueue.add({
      file: filePath,
      scanTypes: this.determineScanTypes(filePath),
      priority: this.calculatePriority(filePath)
    });
  }
}
```

## Integration with Production Readiness

### Score Contribution
```typescript
// Security contributes 30% to overall readiness score
function calculateSecurityScore(vulnerabilities: SecurityVulnerability[]): number {
  const weights = {
    critical: -25,  // Each critical = -25 points
    high: -10,      // Each high = -10 points  
    medium: -3,     // Each medium = -3 points
    low: -1         // Each low = -1 point
  };

  let deductions = 0;
  for (const vuln of vulnerabilities) {
    deductions += weights[vuln.severity];
  }

  // Start at 100, apply deductions, minimum 0
  return Math.max(0, 100 + deductions);
}
```

### Security Milestones
```typescript
const SECURITY_MILESTONES = [
  {
    score: 90,
    badge: "🔒 Fort Knox",
    message: "Your security game is tight!"
  },
  {
    score: 75, 
    badge: "🛡️ Well Protected",
    message: "Solid security posture"
  },
  {
    score: 50,
    badge: "⚠️ Getting Better", 
    message: "On the right track"
  },
  {
    score: 25,
    badge: "🚨 Security Aware",
    message: "At least you're trying"
  }
];
```

## User Interactions

### Detailed Vulnerability View
```
┌─────────────────────────────────────────┐
│  🔑 API Key Exposure (Critical)         │
├─────────────────────────────────────────┤
│                                         │
│  📍 Location: config.js:12              │
│  🕒 Introduced: 3 days ago (commit a4f2c1) │
│                                         │
│  🎯 The Problem:                        │
│  Your OpenAI API key is hardcoded in    │
│  config.js. Anyone with access to your  │
│  code can steal it and rack up charges  │
│  on your account.                       │
│                                         │
│  💥 Potential Impact:                   │
│  • Unauthorized API usage               │
│  • Unexpected charges ($$$)             │
│  • Account suspension                   │
│  • Data exposure                        │
│                                         │
│  🔧 How to Fix (2 minutes):             │
│  1. Move key to .env file               │
│  2. Add .env to .gitignore              │
│  3. Update code to use process.env      │
│                                         │
│  [🚀 Auto-Fix] [👀 Show Code] [❓ Help]  │
└─────────────────────────────────────────┘
```

## Success Metrics

- **Critical Issue Resolution**: 95% of critical issues fixed within 24 hours
- **Auto-Fix Adoption**: 80% of auto-fixable issues resolved via one-click
- **Security Score Improvement**: Average +40 points per security session
- **Scan Frequency**: Real-time monitoring with <5 second update latency
- **False Positive Rate**: <2% (high confidence in LLM analysis)

## Related Components

- [Production Readiness Widget](./04-production-readiness-widget.md) - Overall scoring integration
- [Code Quality Widget](./06-code-quality-widget.md) - Code security patterns
- [Dependency Health Check](../03-user-interface/widgets.md#widget-8-dependency-health-check) - Package vulnerabilities

---

**Security should never be an afterthought. This widget makes it impossible to ignore.** 🔒