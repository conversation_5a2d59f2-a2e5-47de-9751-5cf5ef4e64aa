# 📸 Visual Analysis Report

**Feature ID**: ANALYSIS-VISUAL-001  
**Category**: Quality Analysis  
**Status**: 🚧 In Development  
**Priority**: High

## Overview

Automated visual analysis of web projects using <PERSON><PERSON> for screenshot capture and multimodal LLM analysis to assess UI/UX quality, design consistency, accessibility, and user experience. This system provides comprehensive visual quality assessment that complements code-based analysis.

## Visual Analysis Pipeline

```mermaid
graph TB
    subgraph "Project Detection"
        A[Project Scan] --> B{Web Project?}
        B -->|Yes| C[Detect Framework]
        B -->|No| D[Skip Visual Analysis]
    end
    
    subgraph "Environment Setup"
        C --> E[Install Dependencies]
        E --> F[Configure Build]
        F --> G[Start Dev Server]
    end
    
    subgraph "Screenshot Capture"
        G --> H[Playwright Launch]
        H --> I[Capture Screenshots]
        I --> J[Multiple Viewports]
        J --> K[User Interactions]
        K --> L[State Variations]
    end
    
    subgraph "Multimodal Analysis"
        L --> M[Upload to LLM]
        M --> N[Visual Analysis]
        N --> O[Accessibility Review]
        O --> P[UX Assessment]
        P --> Q[Design Consistency]
    end
    
    subgraph "Report Generation"
        Q --> R[Generate Report]
        R --> S[Store in .kapi]
        S --> T[Update Dashboard]
    end
    
    style C fill:#e1bee7
    style M fill:#ba68c8
    style R fill:#9c27b0
```

## Project Detection & Setup

### 1. Web Project Detection
```typescript
interface ProjectDetectionResult {
  isWebProject: boolean;
  framework: 'react' | 'vue' | 'angular' | 'svelte' | 'next' | 'nuxt' | 'vanilla' | 'unknown';
  buildCommand?: string;
  devCommand?: string;
  buildOutputDir?: string;
  devPort?: number;
  hasPackageJson: boolean;
  hasIndexHtml: boolean;
}

class WebProjectDetector {
  async detectWebProject(projectPath: string): Promise<ProjectDetectionResult> {
    const packageJsonPath = path.join(projectPath, 'package.json');
    const indexHtmlPath = path.join(projectPath, 'index.html');
    
    const hasPackageJson = await fs.pathExists(packageJsonPath);
    const hasIndexHtml = await fs.pathExists(indexHtmlPath);
    
    if (!hasPackageJson && !hasIndexHtml) {
      return { isWebProject: false, framework: 'unknown', hasPackageJson: false, hasIndexHtml: false };
    }
    
    const framework = await this.detectFramework(projectPath);
    const commands = await this.detectCommands(projectPath, framework);
    
    return {
      isWebProject: true,
      framework,
      ...commands,
      hasPackageJson,
      hasIndexHtml
    };
  }
  
  private async detectFramework(projectPath: string): Promise<string> {
    try {
      const packageJson = await fs.readJSON(path.join(projectPath, 'package.json'));
      const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
      
      if (deps['next']) return 'next';
      if (deps['nuxt']) return 'nuxt';
      if (deps['react']) return 'react';
      if (deps['vue']) return 'vue';
      if (deps['@angular/core']) return 'angular';
      if (deps['svelte']) return 'svelte';
      
      return 'vanilla';
    } catch {
      return 'unknown';
    }
  }
  
  private async detectCommands(projectPath: string, framework: string): Promise<Partial<ProjectDetectionResult>> {
    const packageJsonPath = path.join(projectPath, 'package.json');
    
    try {
      const packageJson = await fs.readJSON(packageJsonPath);
      const scripts = packageJson.scripts || {};
      
      // Common build/dev command patterns
      const buildCommand = scripts.build || scripts['build:prod'] || 'npm run build';
      const devCommand = scripts.dev || scripts.start || scripts.serve || 'npm run dev';
      
      return {
        buildCommand,
        devCommand,
        devPort: this.getDefaultPort(framework),
        buildOutputDir: this.getDefaultBuildDir(framework)
      };
    } catch {
      return {};
    }
  }
  
  private getDefaultPort(framework: string): number {
    const portMap = {
      'react': 3000,
      'next': 3000,
      'vue': 8080,
      'nuxt': 3000,
      'angular': 4200,
      'svelte': 5000,
      'vanilla': 3000
    };
    return portMap[framework] || 3000;
  }
}
```

### 2. Environment Setup
```typescript
class EnvironmentSetup {
  async setupWebProject(projectPath: string, detection: ProjectDetectionResult): Promise<SetupResult> {
    const setupSteps = [
      () => this.installDependencies(projectPath),
      () => this.configureBuild(projectPath, detection),
      () => this.startDevServer(projectPath, detection)
    ];
    
    const results = [];
    for (const step of setupSteps) {
      try {
        const result = await step();
        results.push(result);
      } catch (error) {
        return {
          success: false,
          error: error.message,
          failedStep: step.name
        };
      }
    }
    
    return {
      success: true,
      serverUrl: `http://localhost:${detection.devPort}`,
      buildOutputDir: detection.buildOutputDir
    };
  }
  
  private async installDependencies(projectPath: string): Promise<void> {
    // Check if node_modules exists
    const nodeModulesPath = path.join(projectPath, 'node_modules');
    if (await fs.pathExists(nodeModulesPath)) {
      return; // Dependencies already installed
    }
    
    // Install dependencies
    await this.runCommand('npm install', projectPath, { timeout: 300000 }); // 5 minutes timeout
  }
  
  private async startDevServer(projectPath: string, detection: ProjectDetectionResult): Promise<void> {
    const devCommand = detection.devCommand || 'npm run dev';
    
    // Start dev server in background
    const serverProcess = spawn(devCommand.split(' ')[0], devCommand.split(' ').slice(1), {
      cwd: projectPath,
      detached: true,
      stdio: 'pipe'
    });
    
    // Wait for server to be ready
    await this.waitForServer(`http://localhost:${detection.devPort}`, 60000);
    
    return serverProcess;
  }
  
  private async waitForServer(url: string, timeout: number): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(url);
        if (response.ok) return;
      } catch {
        // Server not ready yet
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error(`Server at ${url} did not start within ${timeout}ms`);
  }
}
```

## Screenshot Capture System

### 1. Playwright Integration
```typescript
interface ScreenshotOptions {
  viewports: Viewport[];
  pages: string[];
  interactions: UserInteraction[];
  stateVariations: StateVariation[];
  captureOptions: CaptureOptions;
}

interface Viewport {
  width: number;
  height: number;
  deviceScaleFactor: number;
  isMobile: boolean;
  name: string;
}

interface UserInteraction {
  type: 'click' | 'hover' | 'scroll' | 'form_fill' | 'keyboard';
  selector: string;
  value?: string;
  description: string;
}

interface StateVariation {
  name: string;
  setup: () => Promise<void>;
  description: string;
}

class ScreenshotCapture {
  private browser: Browser;
  private context: BrowserContext;
  
  async captureScreenshots(baseUrl: string, options: ScreenshotOptions): Promise<Screenshot[]> {
    this.browser = await chromium.launch({ headless: true });
    const screenshots: Screenshot[] = [];
    
    for (const viewport of options.viewports) {
      this.context = await this.browser.newContext({
        viewport: { width: viewport.width, height: viewport.height },
        deviceScaleFactor: viewport.deviceScaleFactor,
        userAgent: viewport.isMobile ? 'mobile' : 'desktop'
      });
      
      const page = await this.context.newPage();
      
      for (const pagePath of options.pages) {
        const url = `${baseUrl}${pagePath}`;
        
        try {
          // Navigate to page
          await page.goto(url, { waitUntil: 'networkidle' });
          
          // Initial screenshot
          const initialScreenshot = await this.capturePageScreenshot(page, {
            viewport: viewport.name,
            page: pagePath,
            state: 'initial',
            url
          });
          screenshots.push(initialScreenshot);
          
          // Interaction screenshots
          for (const interaction of options.interactions) {
            try {
              await this.performInteraction(page, interaction);
              
              const interactionScreenshot = await this.capturePageScreenshot(page, {
                viewport: viewport.name,
                page: pagePath,
                state: `after_${interaction.type}`,
                interaction: interaction.description,
                url
              });
              screenshots.push(interactionScreenshot);
            } catch (error) {
              console.warn(`Failed to perform interaction ${interaction.type}:`, error.message);
            }
          }
          
          // State variation screenshots
          for (const stateVariation of options.stateVariations) {
            try {
              await stateVariation.setup();
              
              const stateScreenshot = await this.capturePageScreenshot(page, {
                viewport: viewport.name,
                page: pagePath,
                state: stateVariation.name,
                description: stateVariation.description,
                url
              });
              screenshots.push(stateScreenshot);
            } catch (error) {
              console.warn(`Failed to setup state ${stateVariation.name}:`, error.message);
            }
          }
          
        } catch (error) {
          console.warn(`Failed to capture ${url}:`, error.message);
        }
      }
      
      await this.context.close();
    }
    
    await this.browser.close();
    return screenshots;
  }
  
  private async capturePageScreenshot(page: Page, metadata: ScreenshotMetadata): Promise<Screenshot> {
    const screenshotBuffer = await page.screenshot({ 
      fullPage: true,
      type: 'png'
    });
    
    // Save screenshot to temp file
    const filename = `screenshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.png`;
    const filepath = path.join(os.tmpdir(), filename);
    await fs.writeFile(filepath, screenshotBuffer);
    
    return {
      filepath,
      buffer: screenshotBuffer,
      metadata,
      timestamp: new Date().toISOString()
    };
  }
  
  private async performInteraction(page: Page, interaction: UserInteraction): Promise<void> {
    const element = await page.waitForSelector(interaction.selector, { timeout: 5000 });
    
    switch (interaction.type) {
      case 'click':
        await element.click();
        break;
      case 'hover':
        await element.hover();
        break;
      case 'scroll':
        await element.scrollIntoView();
        break;
      case 'form_fill':
        await element.fill(interaction.value || '');
        break;
      case 'keyboard':
        await element.press(interaction.value || 'Enter');
        break;
    }
    
    // Wait for any animations or state changes
    await page.waitForTimeout(1000);
  }
}
```

### 2. Smart Page Discovery
```typescript
class PageDiscovery {
  async discoverPages(baseUrl: string): Promise<string[]> {
    const browser = await chromium.launch({ headless: true });
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const discoveredPages = new Set<string>();
    const visitedUrls = new Set<string>();
    
    // Start with homepage
    await page.goto(baseUrl);
    discoveredPages.add('/');
    
    // Extract navigation links
    const navLinks = await page.$$eval('nav a, header a, [role="navigation"] a', 
      links => links.map(link => link.getAttribute('href')).filter(href => href)
    );
    
    // Extract common route patterns
    const commonRoutes = await this.extractRoutePatterns(page);
    
    // Extract footer links
    const footerLinks = await page.$$eval('footer a', 
      links => links.map(link => link.getAttribute('href')).filter(href => href)
    );
    
    const allLinks = [...navLinks, ...commonRoutes, ...footerLinks];
    
    // Filter and normalize links
    for (const link of allLinks) {
      const normalizedPath = this.normalizeLink(link, baseUrl);
      if (normalizedPath && !visitedUrls.has(normalizedPath)) {
        discoveredPages.add(normalizedPath);
        visitedUrls.add(normalizedPath);
      }
    }
    
    await browser.close();
    
    return Array.from(discoveredPages).slice(0, 10); // Limit to 10 pages
  }
  
  private async extractRoutePatterns(page: Page): Promise<string[]> {
    // Look for common SPA route patterns
    const routePatterns = [
      '/about',
      '/contact',
      '/services',
      '/products',
      '/portfolio',
      '/blog',
      '/login',
      '/register',
      '/dashboard',
      '/profile'
    ];
    
    const existingRoutes = [];
    
    for (const pattern of routePatterns) {
      try {
        const response = await page.goto(`${page.url()}${pattern}`, { 
          waitUntil: 'networkidle',
          timeout: 5000 
        });
        
        if (response && response.ok()) {
          existingRoutes.push(pattern);
        }
      } catch {
        // Route doesn't exist or failed to load
      }
    }
    
    return existingRoutes;
  }
  
  private normalizeLink(link: string, baseUrl: string): string | null {
    if (!link) return null;
    
    // Skip external links, anchors, and non-HTTP protocols
    if (link.startsWith('http') && !link.startsWith(baseUrl)) return null;
    if (link.startsWith('#')) return null;
    if (link.startsWith('mailto:') || link.startsWith('tel:')) return null;
    
    // Normalize relative links
    if (link.startsWith('/')) {
      return link;
    }
    
    if (link.startsWith('./')) {
      return link.substring(1);
    }
    
    if (!link.startsWith('http')) {
      return `/${link}`;
    }
    
    // Extract path from full URL
    try {
      const url = new URL(link);
      return url.pathname;
    } catch {
      return null;
    }
  }
}
```

## Multimodal LLM Analysis

### 1. Integration with Unified Conversation Service
```typescript
class VisualAnalysisService {
  constructor(
    private conversationService: UnifiedConversationService,
    private memoryService: MemoryService
  ) {}
  
  async analyzeScreenshots(screenshots: Screenshot[], projectContext: ProjectContext): Promise<VisualAnalysisReport> {
    // Create conversation for visual analysis
    const conversation = await this.conversationService.createConversation({
      strategy: 'multimodal-analysis',
      initialMessage: this.buildVisualAnalysisPrompt(projectContext),
      projectId: projectContext.projectId,
      userId: projectContext.userId
    });
    
    const analysisResults = [];
    
    // Analyze screenshots in batches (multimodal models have image limits)
    const batchSize = 4;
    for (let i = 0; i < screenshots.length; i += batchSize) {
      const batch = screenshots.slice(i, i + batchSize);
      
      const batchAnalysis = await this.analyzeBatch(conversation.id, batch, projectContext);
      analysisResults.push(batchAnalysis);
    }
    
    // Generate comprehensive analysis
    const finalAnalysis = await this.generateComprehensiveAnalysis(
      conversation.id,
      analysisResults,
      projectContext
    );
    
    return {
      overallScore: finalAnalysis.overallScore,
      categories: finalAnalysis.categories,
      screenshots: screenshots.map(s => ({
        ...s,
        analysis: analysisResults.find(r => r.screenshots.includes(s.filepath))
      })),
      recommendations: finalAnalysis.recommendations,
      improvements: finalAnalysis.improvements,
      timestamp: new Date().toISOString()
    };
  }
  
  private async analyzeBatch(
    conversationId: string,
    screenshots: Screenshot[],
    projectContext: ProjectContext
  ): Promise<BatchAnalysisResult> {
    // Prepare images for multimodal analysis
    const images = screenshots.map(s => ({
      path: s.filepath,
      description: `${s.metadata.page} - ${s.metadata.viewport} - ${s.metadata.state}`
    }));
    
    const prompt = this.buildBatchAnalysisPrompt(screenshots, projectContext);
    
    // Send to multimodal LLM (Gemini or Azure with vision capabilities)
    const response = await this.conversationService.sendMessage(conversationId, {
      content: prompt,
      images: images,
      expectStructuredResponse: true
    });
    
    return this.parseBatchAnalysisResponse(response.content);
  }
  
  private buildVisualAnalysisPrompt(projectContext: ProjectContext): string {
    return `
      You are KAPI's Visual Analysis Expert, specializing in UI/UX assessment through screenshot analysis.
      
      PROJECT CONTEXT:
      - Framework: ${projectContext.framework}
      - Project Type: ${projectContext.projectType}
      - Target Users: ${projectContext.targetUsers || 'General web users'}
      
      ANALYSIS FOCUS:
      1. Visual Design Quality (25%)
         - Color scheme and consistency
         - Typography and readability
         - Layout and spacing
         - Brand consistency
      
      2. User Experience (30%)
         - Navigation clarity
         - Content hierarchy
         - Interactive elements
         - Mobile responsiveness
      
      3. Accessibility (20%)
         - Color contrast
         - Text size and readability
         - Interactive element sizes
         - Visual indicators
      
      4. Professional Polish (15%)
         - Attention to detail
         - Consistent styling
         - Loading states
         - Error handling
      
      5. Modern Design Standards (10%)
         - Current design trends
         - Framework-specific patterns
         - Industry best practices
      
      Provide honest, actionable feedback with specific recommendations for improvement.
    `;
  }
  
  private buildBatchAnalysisPrompt(screenshots: Screenshot[], projectContext: ProjectContext): string {
    const screenshotDescriptions = screenshots.map(s => 
      `- ${s.metadata.page} (${s.metadata.viewport}) - ${s.metadata.state}`
    ).join('\n');
    
    return `
      Analyze these screenshots from a ${projectContext.framework} web application:
      
      SCREENSHOTS:
      ${screenshotDescriptions}
      
      For each screenshot, provide:
      1. Visual design assessment (colors, typography, layout)
      2. UX evaluation (navigation, usability, clarity)
      3. Accessibility review (contrast, readability, interaction)
      4. Specific improvement recommendations
      5. Score out of 100 for each category
      
      Be brutally honest but constructive in your feedback.
      
      Respond in structured JSON format:
      {
        "screenshots": [
          {
            "path": "screenshot_path",
            "scores": {
              "visual_design": 0-100,
              "user_experience": 0-100,
              "accessibility": 0-100,
              "professional_polish": 0-100,
              "modern_standards": 0-100
            },
            "feedback": {
              "strengths": ["list of strengths"],
              "issues": ["list of issues"],
              "recommendations": ["specific improvements"]
            }
          }
        ]
      }
    `;
  }
}
```

### 2. Multimodal Analysis Strategy
```typescript
class MultimodalAnalysisStrategy extends BaseTaskStrategy implements TaskStrategy {
  readonly strategyName = 'multimodal-analysis';
  readonly defaultProvider = 'gemini'; // Gemini has strong multimodal capabilities
  
  protected buildSystemPrompt(): string {
    return `
      You are KAPI's Visual Analysis Expert, specializing in comprehensive UI/UX assessment through screenshot analysis.
      
      EXPERTISE AREAS:
      - Visual design evaluation (color theory, typography, layout)
      - User experience assessment (navigation, usability, accessibility)
      - Modern web design standards and best practices
      - Framework-specific design patterns
      - Responsive design evaluation
      - Accessibility compliance (WCAG guidelines)
      
      ANALYSIS APPROACH:
      1. Systematic evaluation across multiple dimensions
      2. Honest, actionable feedback with specific recommendations
      3. Consideration of target audience and use case
      4. Identification of quick wins and long-term improvements
      5. Integration with overall project quality assessment
      
      RESPONSE FORMAT:
      - Structured analysis with scores and categories
      - Specific, actionable recommendations
      - Examples of improvements when possible
      - Honest assessment without being discouraging
      
      Always provide constructive feedback that helps developers improve their visual design and user experience.
    `;
  }
  
  protected optimizeContextForProvider(
    context: any,
    provider: string
  ): any {
    // For multimodal analysis, prioritize visual context
    return {
      ...context,
      // Include project visual context
      designSystem: context.project?.design_system,
      brandGuidelines: context.project?.brand_guidelines,
      targetAudience: context.project?.target_audience,
      // Include user preferences for design feedback
      designPreferences: context.personal?.design_preferences,
      // Include relevant code context for framework-specific patterns
      frameworkContext: context.technical?.framework_patterns
    };
  }
}
```

## Visual Analysis Report Structure

### 1. Report Generation
```typescript
interface VisualAnalysisReport {
  projectId: string;
  analysisId: string;
  timestamp: string;
  
  // Overall Assessment
  overallScore: number;
  overallGrade: 'A' | 'B' | 'C' | 'D' | 'F';
  
  // Category Scores
  categories: {
    visualDesign: CategoryScore;
    userExperience: CategoryScore;
    accessibility: CategoryScore;
    professionalPolish: CategoryScore;
    modernStandards: CategoryScore;
  };
  
  // Screenshot Analysis
  screenshots: ScreenshotAnalysis[];
  
  // Recommendations
  recommendations: Recommendation[];
  improvements: Improvement[];
  
  // Metadata
  metadata: {
    framework: string;
    screenshotCount: number;
    viewportsCovered: string[];
    pagesAnalyzed: string[];
  };
}

interface CategoryScore {
  score: number;
  maxScore: number;
  percentage: number;
  grade: string;
  feedback: string;
  improvements: string[];
}

interface ScreenshotAnalysis {
  filepath: string;
  metadata: ScreenshotMetadata;
  scores: {
    visualDesign: number;
    userExperience: number;
    accessibility: number;
    professionalPolish: number;
    modernStandards: number;
  };
  feedback: {
    strengths: string[];
    issues: string[];
    recommendations: string[];
  };
  brutalHonestyMessage: string;
}

interface Recommendation {
  category: string;
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  effort: 'quick' | 'medium' | 'large';
  impact: 'high' | 'medium' | 'low';
  screenshots: string[];
}

interface Improvement {
  type: 'color' | 'typography' | 'layout' | 'accessibility' | 'interaction';
  current: string;
  suggested: string;
  reason: string;
  effort: string;
  impact: string;
}
```

### 2. Report Storage and Integration
```typescript
class VisualAnalysisReportGenerator {
  async generateReport(analysis: VisualAnalysisReport, projectPath: string): Promise<string> {
    const reportContent = this.buildMarkdownReport(analysis);
    const reportPath = path.join(projectPath, '.kapi', `visual-analysis-${Date.now()}.md`);
    
    await fs.ensureDir(path.dirname(reportPath));
    await fs.writeFile(reportPath, reportContent);
    
    // Update project health metrics
    await this.updateProjectHealth(projectPath, analysis);
    
    return reportPath;
  }
  
  private buildMarkdownReport(analysis: VisualAnalysisReport): string {
    return `
# 📸 Visual Analysis Report

**Generated**: ${new Date(analysis.timestamp).toLocaleString()}  
**Framework**: ${analysis.metadata.framework}  
**Screenshots**: ${analysis.metadata.screenshotCount}  
**Pages Analyzed**: ${analysis.metadata.pagesAnalyzed.join(', ')}

## 🎯 Overall Assessment

**Visual Quality Score**: ${analysis.overallScore}/100 (${analysis.overallGrade})

${this.getBrutalHonestyMessage(analysis.overallScore)}

## 📊 Category Breakdown

### 🎨 Visual Design (${analysis.categories.visualDesign.percentage}%)
${analysis.categories.visualDesign.feedback}

**Key Issues:**
${analysis.categories.visualDesign.improvements.map(i => `- ${i}`).join('\n')}

### 👤 User Experience (${analysis.categories.userExperience.percentage}%)
${analysis.categories.userExperience.feedback}

**Key Issues:**
${analysis.categories.userExperience.improvements.map(i => `- ${i}`).join('\n')}

### ♿ Accessibility (${analysis.categories.accessibility.percentage}%)
${analysis.categories.accessibility.feedback}

**Key Issues:**
${analysis.categories.accessibility.improvements.map(i => `- ${i}`).join('\n')}

### ✨ Professional Polish (${analysis.categories.professionalPolish.percentage}%)
${analysis.categories.professionalPolish.feedback}

**Key Issues:**
${analysis.categories.professionalPolish.improvements.map(i => `- ${i}`).join('\n')}

### 🔥 Modern Standards (${analysis.categories.modernStandards.percentage}%)
${analysis.categories.modernStandards.feedback}

**Key Issues:**
${analysis.categories.modernStandards.improvements.map(i => `- ${i}`).join('\n')}

## 🎯 Priority Recommendations

${analysis.recommendations
  .filter(r => r.priority === 'high')
  .map(r => `
### ${r.title}
**Priority**: ${r.priority.toUpperCase()} | **Effort**: ${r.effort} | **Impact**: ${r.impact}

${r.description}

**Affected Screenshots**: ${r.screenshots.join(', ')}
`).join('\n')}

## 🔧 Quick Wins

${analysis.recommendations
  .filter(r => r.effort === 'quick')
  .map(r => `- **${r.title}**: ${r.description}`)
  .join('\n')}

## 📸 Screenshot Analysis

${analysis.screenshots.map(s => `
### ${s.metadata.page} - ${s.metadata.viewport}

**State**: ${s.metadata.state}  
**Overall Score**: ${Math.round((s.scores.visualDesign + s.scores.userExperience + s.scores.accessibility + s.scores.professionalPolish + s.scores.modernStandards) / 5)}

${s.brutalHonestyMessage}

**Strengths:**
${s.feedback.strengths.map(strength => `- ${strength}`).join('\n')}

**Issues:**
${s.feedback.issues.map(issue => `- ${issue}`).join('\n')}

**Recommendations:**
${s.feedback.recommendations.map(rec => `- ${rec}`).join('\n')}
`).join('\n')}

## 🚀 Next Steps

1. **Immediate Actions** (0-2 hours):
   ${analysis.recommendations.filter(r => r.effort === 'quick').map(r => `- ${r.title}`).join('\n   ')}

2. **Short-term Improvements** (2-8 hours):
   ${analysis.recommendations.filter(r => r.effort === 'medium').map(r => `- ${r.title}`).join('\n   ')}

3. **Long-term Enhancements** (8+ hours):
   ${analysis.recommendations.filter(r => r.effort === 'large').map(r => `- ${r.title}`).join('\n   ')}

---

*Visual analysis powered by KAPI's multimodal AI system* 🎨
`;
  }
  
  private getBrutalHonestyMessage(score: number): string {
    if (score >= 90) return "🏆 **Outstanding!** Your visual design is production-ready and impressive.";
    if (score >= 80) return "🎯 **Solid work!** Good visual design with room for polish.";
    if (score >= 70) return "⚠️ **Functional but needs work** - Users will notice the rough edges.";
    if (score >= 60) return "😬 **Yikes!** Your design needs serious attention before users see it.";
    return "💥 **Visual disaster zone!** This needs a complete design overhaul.";
  }
}
```

## Integration with Quality Analysis System

### 1. Production Readiness Integration
```typescript
// Visual analysis contributes 20% to overall readiness score
interface VisualContribution {
  dimension: 'visual_quality';
  weight: 0.20;
  factors: {
    visualDesign: number;      // 30% - visual appeal and consistency
    userExperience: number;    // 35% - usability and navigation
    accessibility: number;     // 25% - accessibility compliance
    professionalPolish: number; // 10% - attention to detail
  };
}

function calculateVisualScore(visualAnalysis: VisualAnalysisReport): number {
  const factors = visualAnalysis.categories;
  
  return Math.round(
    factors.visualDesign.percentage * 0.30 +
    factors.userExperience.percentage * 0.35 +
    factors.accessibility.percentage * 0.25 +
    factors.professionalPolish.percentage * 0.10
  );
}
```

### 2. Dashboard Widget Integration
```typescript
interface VisualAnalysisWidget {
  id: 'visual-analysis-widget';
  title: '📸 Visual Analysis';
  status: 'active' | 'pending' | 'error';
  data: {
    overallScore: number;
    lastAnalysis: string;
    screenshotCount: number;
    improvements: number;
    trending: 'up' | 'down' | 'stable';
  };
}

// Widget display
const visualAnalysisWidget = `
┌─────────────────────────────────────────┐
│  📸 Visual Analysis                     │
├─────────────────────────────────────────┤
│                                         │
│  Visual Quality: 78/100 🟡              │
│  ████████████████████████████████▒▒▒▒▒▒▒▒│
│                                         │
│  📊 Category Breakdown:                 │
│  ├─ 🎨 Visual Design: 82% ✅            │
│  ├─ 👤 User Experience: 75% ⚠️          │
│  ├─ ♿ Accessibility: 68% ⚠️            │
│  └─ ✨ Professional Polish: 85% ✅      │
│                                         │
│  📸 Last Analysis: 2 hours ago          │
│  🔄 Screenshots: 24 across 6 pages      │
│                                         │
│  🎯 Priority Issues:                    │
│  ├─ Color contrast too low (8 issues)   │
│  ├─ Mobile navigation unclear           │
│  └─ Interactive elements too small      │
│                                         │
│  📈 Trend: +12 points this week         │
│                                         │
│  [📸 Re-analyze] [🔍 Details] [🛠️ Fix]  │
└─────────────────────────────────────────┘
`;
```

## Success Metrics

- **Analysis Completion**: 95% of web projects successfully analyzed
- **Screenshot Coverage**: Average 20+ screenshots per project
- **Issue Detection**: 85% accuracy in identifying visual problems
- **Improvement Tracking**: 40% average visual score improvement after fixes
- **User Adoption**: 75% of visual recommendations implemented
- **Time to Analysis**: Complete visual analysis in <10 minutes
- **Framework Support**: 90% compatibility with major web frameworks

## Related Components

- [Production Readiness Widget](./04-production-readiness-widget.md) - Visual quality score integration
- [Unified Conversation Service](../01-ai-intelligence/unified-conversation-service.md) - Multimodal analysis capabilities
- [Project Analysis Health System](./03-health-monitoring-system.md) - Visual quality tracking

---

**Great design is invisible - until it's missing. Let's make sure yours doesn't disappear.** 📸✨