# 📟 KAPI Interactive Terminal - Command Reference

_Natural language commands within interactive sessions_

## 🎯 Interactive Session Model

### How It Works
```bash
# Start interactive session
$ kapi
🤖 KAPI Terminal Assistant ready
   Project: /Users/<USER>/my-app (React + Node.js) 
   Context: Session #42 loaded

kapi> analyze this project              # Natural language
kapi> find all TODO comments            # No complex syntax
kapi> chat about security issues        # Conversational
kapi> exit                             # End session
```

### Session Features
- **Natural Language**: No command syntax to remember
- **Persistent Context**: Remembers conversation across commands  
- **Smart Suggestions**: Proactive next actions
- **Rich Output**: Colors, progress bars, formatted responses

---

## 🗣️ Natural Language Commands

### 📊 Project Analysis
```bash
kapi> analyze this project
kapi> what's wrong with my code?
kapi> check for security issues
kapi> how can I improve performance?
kapi> review this codebase
kapi> show me project status
kapi> what type of project is this?
```

### 🔍 File Operations  
```bash
kapi> find all TODO comments
kapi> search for authentication code
kapi> show me the largest files
kapi> locate configuration files
kapi> search for "password" in code
kapi> find files modified today
```

### 🤖 AI Conversations
```bash
kapi> explain this function [paste code]
kapi> how do I implement JWT auth?
kapi> what does this error mean?
kapi> help me refactor this component
kapi> write tests for this module
kapi> translate "list all JavaScript files"
```

### 🎯 Agent Interactions
```bash
kapi> chat with security expert
kapi> ask documentation agent about README
kapi> get brutal honesty feedback
kapi> switch to code review agent
kapi> talk to the testing specialist
```

---

## ⚙️ Built-in Session Commands

### Session Management
```bash
kapi> help                    # Show available commands
kapi> status                  # Current session info
kapi> history                 # Show command history
kapi> clear                   # Clear screen
kapi> exit                    # End session
```

### Context & Memory
```bash
kapi> context                 # Show project context
kapi> save session           # Persist current session
kapi> load session <name>    # Restore saved session
kapi> reset context          # Clear project memory
```

### Agent Management
```bash
kapi> agents                 # List available AI agents
kapi> agents info <agent>    # Show agent details
kapi> switch to <agent>      # Change active agent
kapi> agent status           # Show active agents
```

---

## 🎨 Response Types & Formatting

### Analysis Results
```bash
kapi> analyze project

📊 Project Analysis
├─ Type: React + Node.js application
├─ Files: 234 JavaScript/TypeScript files
├─ Dependencies: 67 packages (3 outdated)
└─ Issues: 12 potential improvements found

⚠️  Key Issues:
  🔴 Security: 3 high-priority vulnerabilities
  🟡 Performance: Large bundle size (2.3MB)
  🟢 Quality: Missing tests for 40% of components

💡 Next Actions:
  1. Update vulnerable dependencies
  2. Implement code splitting
  3. Add test coverage for critical components
```

### Search Results
```bash
kapi> find all TODO comments

🔍 Search Results: TODO comments
├─ Found 15 TODO items across 8 files
└─ Sorted by priority and recency

📄 High Priority:
  src/auth.js:42     # TODO: Add rate limiting
  api/users.js:156   # TODO: Validate input data

📄 Medium Priority:
  components/Modal.js:28   # TODO: Add accessibility
  utils/helpers.js:91      # TODO: Optimize performance

💡 Suggestions:
  - Create GitHub issues for high-priority TODOs
  - Schedule technical debt cleanup sprint
```

### AI Conversations
```bash
kapi> how do I implement JWT authentication?

🤖 AI Response: JWT Authentication Implementation

Here's a secure approach for your React + Node.js app:

Backend (Node.js):
```javascript
// Generate JWT token
const jwt = require('jsonwebtoken');
const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET);
```

Frontend (React):
```javascript
// Store and use token
localStorage.setItem('token', token);
const authHeader = { Authorization: `Bearer ${token}` };
```

🔒 Security Best Practices:
  1. Use environment variables for secrets
  2. Set appropriate expiration times
  3. Implement refresh token rotation
  4. Add rate limiting to auth endpoints

💡 Next Steps:
  - Set up JWT middleware
  - Create auth context in React
  - Add token refresh logic
```

---

## 📋 Command Processing Flow

### Intent Recognition
```
User Input: "find all React components"
    ↓
Intent Parser: { type: 'search', target: 'React components' }
    ↓
Handler: FileOperationsHandler.searchFiles()
    ↓
Execution: Search for .jsx/.tsx files with component patterns
    ↓
Response: Formatted file list with component details
```

### Context Enhancement
```
Command: "explain this function"
    ↓
Context: + Current file path
         + Recent conversation history
         + Project type (React app)
         + User coding style preferences
    ↓
AI Response: Tailored explanation with project-specific insights
```

---

## 🚀 Advanced Session Features

### Multi-turn Conversations
```bash
kapi> analyze security issues
# ... shows security analysis ...

kapi> fix the hardcoded secrets issue
# ... generates fixes for secrets ...

kapi> what about the SQL injection risk?
# ... explains SQL injection prevention ...

kapi> implement those changes
# ... applies security improvements ...
```

### Agent Switching
```bash
kapi> review my authentication code
# ... general code review ...

kapi> switch to security agent
🔒 Now talking with Security Specialist Agent

kapi> review the same code
# ... detailed security-focused analysis ...
```

### Session Persistence
```bash
kapi> save session security-review
✅ Session saved as 'security-review'

# Later...
$ kapi
kapi> load session security-review
✅ Restored session with full context and history
```

---

## 🎯 Implementation Status

### ✅ Current Capabilities
- Interactive session framework
- Natural language command parsing  
- Context-aware AI responses
- Rich terminal formatting
- Session persistence

### 🔄 In Development  
- Enhanced intent recognition
- Agent switching and orchestration
- Multi-session management
- Advanced context understanding

### 🚀 Future Features
- Voice input support
- Collaborative sessions  
- Workflow automation
- Plugin ecosystem

---

*The interactive terminal provides natural, conversational AI assistance with persistent context, eliminating the need to remember complex command syntax while maintaining full access to KAPI's capabilities.*