# Unified Conversation Service Specification

_Feature ID: CORE-CONVERSATION-001_  
_Last updated: July 18, 2025_

## 🔗 Implementation References

### **Core Service Files**
- **Unified Conversation Service**: [`/backend/src/services/conversation/unified-conversation.service.ts`](../../../backend/src/services/conversation/unified-conversation.service.ts) - Main service with lifecycle management
- **Task Strategy Registry**: [`/backend/src/services/conversation/task-strategy-registry.ts`](../../../backend/src/services/conversation/task-strategy-registry.ts) - Strategy pattern implementation
- **Base Task Strategy**: [`/backend/src/services/conversation/base-task-strategy.ts`](../../../backend/src/services/conversation/base-task-strategy.ts) - Abstract strategy base class

### **LLM Provider Integrations**
- **AI Service Hub**: [`/backend/src/services/ai/index.ts`](../../../backend/src/services/ai/index.ts) - Unified provider interface
- **Claude Service**: [`/backend/src/services/ai/claude.service.ts`](../../../backend/src/services/ai/claude.service.ts) - AWS Bedrock integration
- **Gemini Service**: [`/backend/src/services/ai/gemini.service.ts`](../../../backend/src/services/ai/gemini.service.ts) - Google Gemini integration
- **Nova Service**: [`/backend/src/services/ai/nova.service.ts`](../../../backend/src/services/ai/nova.service.ts) - Amazon Nova models
- **Azure Service**: [`/backend/src/services/ai/azure.service.ts`](../../../backend/src/services/ai/azure.service.ts) - Azure OpenAI integration

### **Strategy Implementations**
- **Task Strategies**: [`/backend/src/services/conversation/strategies/`](../../../backend/src/services/conversation/strategies/) - All task strategy implementations
  - `chat-task-strategy.ts` - General chat conversations
  - `code-generation-task-strategy.ts` - Code generation tasks
  - `multimodal-task-strategy.ts` - Multimodal content handling
  - `slide-generation-task-strategy.ts` - Presentation generation
  - `project-onboarding-task-strategy.ts` - Project setup flows

### **Message Management**
- **Conversation Memory**: [`/backend/src/services/memory/conversation-memory.service.ts`](../../../backend/src/services/memory/conversation-memory.service.ts) - Advanced memory management
- **Conversation Repository**: [`/backend/src/db/repositories/conversation.repository.ts`](../../../backend/src/db/repositories/conversation.repository.ts) - Data persistence

### **API Layer**
- **Conversation Routes**: [`/backend/src/routes/conversation/index.ts`](../../../backend/src/routes/conversation/index.ts) - REST API endpoints
- **Conversation Types**: [`/backend/src/common/types/conversation.types.ts`](../../../backend/src/common/types/conversation.types.ts) - TypeScript definitions

## Overview

The Unified Conversation Service manages all AI interactions across KAPI by providing a standardized interface over multiple LLM providers. It handles conversation lifecycle, message management, and provider abstraction while integrating with the memory system for context-aware responses.

## 🤖 AI Provider Integration

### Supported Providers

```mermaid
graph TB
    subgraph "Unified Conversation Service"
        UCS[Conversation Service]
        PM[Provider Manager]
        CL[Conversation Lifecycle]
    end
    
    subgraph "AI Providers"
        Claude[Claude - Anthropic]
        Gemini[Gemini - Google]
        Azure[Azure OpenAI]
        Nova[Nova Sonic - Voice]
    end
    
    subgraph "Provider Capabilities"
        C1[Text Generation]
        C2[Code Generation]
        C3[Image Analysis]
        C4[Voice Processing]
        C5[Streaming Responses]
    end
    
    UCS --> PM
    PM --> Claude
    PM --> Gemini
    PM --> Azure
    PM --> Nova
    
    Claude --> C1
    Claude --> C2
    Claude --> C5
    
    Gemini --> C1
    Gemini --> C3
    Gemini --> C5
    
    Azure --> C1
    Azure --> C2
    Azure --> C5
    
    Nova --> C4
    Nova --> C5
    
    style UCS fill:#e1bee7
    style PM fill:#ba68c8
```

### Provider Configuration

| Provider | Models Available | Streaming | Multimodal | Capabilities |
|----------|------------------|-----------|------------|--------------|
| **Claude** | claude-3-5-sonnet, claude-3-haiku | ✅ | ❌ | Text generation, code analysis |
| **Gemini** | gemini-1.5-pro, gemini-1.5-flash | ✅ | ✅ | Image analysis, multimodal |
| **Azure OpenAI** | gpt-4o, gpt-4o-mini | ✅ | ✅ | Structured content, enterprise |
| **Nova Sonic** | voice-to-text, text-to-voice | ✅ | ❌ | Voice interactions |

## 🔄 Conversation Management

### Conversation Lifecycle

```mermaid
sequenceDiagram
    participant Client as Client
    participant UCS as Conversation Service
    participant Memory as Memory System
    participant Provider as AI Provider
    participant DB as Database
    
    Client->>UCS: Create conversation
    UCS->>DB: Store conversation metadata
    UCS->>Client: Return conversation ID
    
    Client->>UCS: Send message
    UCS->>Memory: Assemble context
    Memory->>UCS: Return context data
    UCS->>Provider: Execute with context
    Provider->>UCS: Stream response
    UCS->>DB: Store message & response
    UCS->>Client: Stream response
    
    Client->>UCS: Continue conversation
    UCS->>Memory: Update context
    Note over Memory: Context grows with conversation
```

### Message Management

```mermaid
graph TB
    subgraph "Message Flow"
        A[User Message] --> B[Conversation Service]
        B --> C[Context Assembly]
        C --> D[Provider Selection]
        D --> E[AI Processing]
        E --> F[Response Processing]
        F --> G[Database Storage]
        G --> H[Response Delivery]
    end
    
    subgraph "Message Storage"
        I[Message ID]
        J[Conversation ID]
        K[User ID]
        L[Content]
        M[Metadata]
        N[Timestamp]
    end
    
    subgraph "Response Processing"
        O[Stream Handling]
        P[Token Counting]
        Q[Cost Tracking]
        R[Error Handling]
    end
    
    B --> I
    F --> O
    F --> P
    F --> Q
    F --> R
    
    style C fill:#fff9c4
    style D fill:#e3f2fd
    style E fill:#e8f5e9
```

## 🎯 Strategy Pattern Implementation

### Available Strategies

The conversation service uses task-specific strategies for different types of interactions:

| Strategy | Purpose | Context Needs |
|----------|---------|---------------|
| **Chat** | General conversation | User preferences, recent history |
| **Code Generation** | Writing/modifying code | Project context, tech stack |
| **Slide Generation** | Creating presentations | Document structure, content |
| **SVG Mockup** | Visual design creation | Design requirements, branding |
| **Test Generation** | Writing test cases | Code context, testing framework |
| **Multimodal Analysis** | Image/file analysis | File content, analysis goals |
| **User Onboarding** | New user setup | User profile, preferences |
| **Project Onboarding** | Project setup | Project structure, goals |
| **Code Planning** | Architecture planning | Requirements, constraints |

### Strategy Selection Flow

```mermaid
flowchart TD
    A[User Message] --> B[Strategy Selector]
    B --> C{Contains Code Keywords?}
    C -->|Yes| D[Code Generation Strategy]
    C -->|No| E{Contains Image/File?}
    E -->|Yes| F[Multimodal Analysis Strategy]
    E -->|No| G{Onboarding Context?}
    G -->|Yes| H[Onboarding Strategy]
    G -->|No| I[Chat Strategy - Default]
    
    D --> J[Dynamic Provider Selection]
    F --> J
    H --> J
    I --> J
    
    style B fill:#e1bee7
    style J fill:#9c27b0
```

## 💾 Memory Integration

### Context Assembly Process

```mermaid
graph TB
    subgraph "Memory Context Types"
        A[User Context]
        B[Technical Context]
        C[Code Context]
        D[Business Context]
        E[Conversation History]
    end
    
    subgraph "Context Assembly"
        F[Memory Service]
        G[Token Budget Manager]
        H[Context Optimizer]
        I[Final Context]
    end
    
    subgraph "Provider Delivery"
        J[Claude Messages]
        K[Gemini Messages]
        L[Azure Messages]
    end
    
    A --> F
    B --> F
    C --> F
    D --> F
    E --> F
    
    F --> G
    G --> H
    H --> I
    
    I --> J
    I --> K
    I --> L
    
    style F fill:#ba68c8
    style G fill:#e1bee7
    style H fill:#9c27b0
```

### Context Optimization

The service optimizes context delivery based on token budgets and provider capabilities:

| Provider | Token Limit | Context Priority | Optimization Strategy |
|----------|-------------|------------------|----------------------|
| **Claude** | 200K | Code > User > Technical | Preserve code context |
| **Gemini** | 1M | User > Visual > Technical | Include more history |
| **Azure** | 128K | Business > Technical > User | Focus on structured data |
| **Nova** | 32K | User > Conversation | Minimal context |

## 🔄 Dynamic Provider Routing

### Configuration-Based Selection

The conversation service uses dynamic provider selection based on real-time configuration:

```mermaid
flowchart TD
    A[Strategy Determined] --> B[Load Configuration]
    B --> C[config.yaml]
    B --> D[models.tsv]
    
    C --> E[Provider Priorities]
    D --> F[Model Performance Data]
    
    E --> G[Routing Engine]
    F --> G
    
    G --> H[Provider Selection]
    H --> I[Model Selection]
    I --> J[Execute Request]
    
    style B fill:#e1bee7
    style G fill:#9c27b0
    style H fill:#ba68c8
```

### Configuration Files

#### config.yaml Structure
```yaml
ai_providers:
  claude:
    enabled: true
    priority: 1
    models:
      - claude-3-5-sonnet
      - claude-3-haiku
  
  gemini:
    enabled: true
    priority: 2
    models:
      - gemini-1.5-pro
      - gemini-1.5-flash
  
  azure:
    enabled: true
    priority: 3
    models:
      - gpt-4o
      - gpt-4o-mini
```

#### models.tsv Structure
```
provider	model	strategy	success_rate	avg_response_time	cost_per_token
claude	claude-3-5-sonnet	code_generation	0.94	2.1	0.000015
gemini	gemini-1.5-pro	multimodal_analysis	0.91	2.8	0.000001
azure	gpt-4o	slide_generation	0.88	3.2	0.000010
```

### Real-Time Priority Updates

The routing system continuously updates provider priorities based on:

| Factor | Update Frequency | Impact on Routing |
|--------|------------------|------------------|
| **Success Rate** | Per request | High priority adjustment |
| **Response Time** | Real-time monitoring | Performance-based weighting |
| **Cost Efficiency** | Hourly analysis | Budget-conscious routing |
| **Model Availability** | Health check intervals | Failover triggering |

### Routing Algorithm

```mermaid
sequenceDiagram
    participant Strategy as Strategy
    participant Router as Routing Engine
    participant Config as Configuration
    participant Health as Health Monitor
    
    Strategy->>Router: Request provider for task
    Router->>Config: Get current priorities
    Config->>Router: Return weighted providers
    Router->>Health: Check provider status
    Health->>Router: Return availability
    Router->>Strategy: Select optimal provider
    
    Note over Router,Config: Dynamic priority loading
    Note over Router,Health: Real-time health checks
```

## 🌐 Provider Abstraction

### Unified Interface

```mermaid
graph LR
    subgraph "Conversation Service API"
        A[createConversation]
        B[addMessage]
        C[streamResponse]
        D[getHistory]
        E[updateTitle]
    end
    
    subgraph "Provider Abstraction"
        F[Provider Manager]
        G[Request Formatter]
        H[Response Parser]
        I[Stream Handler]
        J[Error Handler]
    end
    
    subgraph "Provider Implementations"
        K[Claude Service]
        L[Gemini Service]
        M[Azure Service]
        N[Nova Service]
    end
    
    A --> F
    B --> F
    C --> F
    D --> F
    E --> F
    
    F --> G
    G --> H
    H --> I
    I --> J
    
    J --> K
    J --> L
    J --> M
    J --> N
    
    style F fill:#e1bee7
    style G fill:#ba68c8
    style H fill:#9c27b0
```

### Provider-Specific Implementation

Each provider has its own service implementation handling:

- **Authentication**: API keys, tokens, session management
- **Request Formatting**: Converting unified requests to provider-specific formats
- **Response Parsing**: Standardizing responses across providers
- **Streaming**: Real-time response delivery
- **Error Handling**: Provider-specific error recovery

## 📊 Conversation Features

### Title Generation

```mermaid
flowchart LR
    A[New Conversation] --> B[First Message]
    B --> C[Generate Title]
    C --> D[Use Gemini]
    D --> E[Analyze Content]
    E --> F[Generate Concise Title]
    F --> G[Update Conversation]
    
    style C fill:#e3f2fd
    style D fill:#e3f2fd
    style F fill:#e3f2fd
```

### Advanced Message Management

Conversations support sophisticated message handling:

- **Message Ordering**: Chronological message sequence with pagination
- **Context Preservation**: Maintaining conversation state across sessions
- **Memory Integration**: Continuous context building with token optimization
- **Title Updates**: Dynamic title generation based on content
- **User Ratings**: Rating system for message quality
- **Conversation Pinning**: Pin important conversations
- **Hierarchical Conversations**: Parent-child relationships via `parent_id`
- **File Associations**: Link messages to specific files and line ranges

### Agent Integration

```mermaid
graph TB
    subgraph "Agent-Enhanced Conversations"
        A[Conversation] --> B[Agent Status]
        A --> C[Progress Tracking]
        A --> D[Iteration Count]
        A --> E[Allowed Actions]
        A --> F[Commands Executed]
        A --> G[Completion Summary]
    end
    
    subgraph "Agent Workflow"
        H[Agent Assigned] --> I[Execute Actions]
        I --> J[Update Progress]
        J --> K[Track Iterations]
        K --> L[Complete Task]
    end
    
    B --> H
    C --> I
    
    style A fill:#e1bee7
    style H fill:#ba68c8
    style L fill:#c8e6c9
```

### Server-Sent Events (SSE) Streaming

```mermaid
sequenceDiagram
    participant Client as Client
    participant UCS as Conversation Service
    participant Memory as Memory Service
    participant Provider as AI Provider
    
    Client->>UCS: POST /conversations/:id/stream
    UCS->>Memory: Optimize context
    Memory->>UCS: Return optimized context
    UCS->>Provider: Stream request
    
    loop Streaming Response
        Provider->>UCS: Content chunk
        UCS->>UCS: Track tokens & cost
        UCS->>Client: SSE: content chunk
    end
    
    Provider->>UCS: Stream complete
    UCS->>UCS: Calculate final metrics
    UCS->>Client: SSE: usage data
    UCS->>Client: SSE: close connection
    
    Note over UCS,Provider: Real-time token tracking
    Note over Client,UCS: Progressive content display
```

### Streaming Data Structure

```typescript
interface StreamChunk {
  type: 'content' | 'done' | 'error' | 'close';
  content?: string;
  taskType?: string;
  routedBy?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
    cost: number;
    durationMs: number;
  };
  model?: string;
  error?: string;
}
```

## 🔧 Technical Implementation

### Database Schema

```mermaid
erDiagram
    CONVERSATION {
        int id PK
        int user_id FK
        int project_id FK
        string title
        string status
        string category
        string key_objective
        int user_rating
        string user_feedback
        boolean is_pinned
        string agent_status
        json agent_progress
        int agent_iteration_count
        json agent_allowed_actions
        json agent_commands_executed
        string agent_completion_summary
        string last_activity_type
        int parent_id FK
        int agent_id FK
        json file_paths
        int total_tokens
        json meta_data
        json settings
        datetime created_at
        datetime updated_at
    }
    
    MESSAGE {
        int id PK
        int conversation_id FK
        string role "user|assistant|system"
        text content
        string model
        int prompt_tokens
        int completion_tokens
        decimal cost
        int duration_ms
        string user_feedback
        int project_id FK
        string code_language
        text code_snippet
        string file_path
        int line_start
        int line_end
        json meta_data
        datetime created_at
    }
    
    CONVERSATION ||--o{ MESSAGE : contains
    CONVERSATION ||--o{ CONVERSATION : parent_child
```

### Advanced Memory Optimization

```mermaid
flowchart TD
    subgraph "Memory Service Integration"
        A[Raw Conversation History] --> B[ConversationMemoryService]
        B --> C[Message Importance Scoring]
        C --> D[Token Budget Allocation]
        D --> E[Context Window Optimization]
        E --> F[Final Context Assembly]
    end
    
    subgraph "Scoring Criteria"
        G[Message Position Weight]
        H[Content Relevance Score]
        I[Keyword Matching]
        J[Temporal Proximity]
    end
    
    subgraph "Optimization Strategies"
        K[Sliding Window Approach]
        L[Priority-Based Selection]
        M[Semantic Relevance Filtering]
        N[Token Limit Enforcement]
    end
    
    C --> G
    C --> H
    C --> I
    C --> J
    
    D --> K
    D --> L
    D --> M
    D --> N
    
    style B fill:#e1bee7
    style E fill:#ba68c8
    style F fill:#9c27b0
```

### Cost Tracking & Analytics

```mermaid
graph LR
    subgraph "Real-Time Cost Tracking"
        A[Token Usage] --> B[Model-Specific Pricing]
        B --> C[Cost Calculation]
        C --> D[Usage Analytics]
        D --> E[Budget Monitoring]
    end
    
    subgraph "Performance Metrics"
        F[Response Time]
        G[Success Rate]
        H[Error Tracking]
        I[Provider Performance]
    end
    
    subgraph "Analytics Dashboard"
        J[Cost per Conversation]
        K[Token Efficiency]
        L[Model Performance]
        M[User Patterns]
    end
    
    A --> F
    C --> J
    D --> K
    I --> L
    
    style C fill:#fff9c4
    style D fill:#e8f5e9
    style E fill:#ffcdd2
```

### API Endpoints

#### Core Conversation Management
```
GET    /conversations              - List user conversations with pagination
GET    /conversations/:id          - Get conversation details
PUT    /conversations/:id          - Update conversation metadata
DELETE /conversations/:id          - Delete conversation
POST   /conversations              - Create new conversation
```

#### Message Management
```
GET    /conversations/:id/messages - Get conversation messages
POST   /conversations/:id/stream   - Stream AI responses (SSE)
```

#### Task-Specific Endpoints
```
POST   /conversations/tasks/chat              - General chat
POST   /conversations/tasks/code-generation   - Code generation
POST   /conversations/tasks/code-planning     - Architecture planning
POST   /conversations/invoke-model            - Direct model invocation
POST   /conversations/invoke-for-task         - Task-optimized invocation
```

### Service Architecture

The unified conversation service is built with:

- **Modular Design**: Separate provider implementations
- **Strategy Pattern**: Task-specific conversation handling with 10+ strategies
- **Memory Integration**: Context-aware responses with token optimization
- **Streaming Support**: Real-time response delivery via Server-Sent Events
- **Error Resilience**: Fallback mechanisms and retry logic
- **Agent Integration**: Multi-agent conversation orchestration
- **Performance Monitoring**: Real-time cost and performance tracking
- **Database Layer**: Prisma ORM with PostgreSQL
- **Caching**: Redis-based caching for frequently accessed data

## 🔗 Integration Points

### System Integrations

| System | Integration Method | Purpose |
|--------|-------------------|---------|
| **Memory System** | Context assembly API | Provides conversation context |
| **Task Routing** | Strategy selection | Determines conversation type |
| **User Management** | Authentication | User identification and permissions |
| **Project Management** | Context injection | Project-aware conversations |
| **WebSocket Service** | Real-time streaming | Live response delivery |

## 🚀 Future Enhancements

### Planned Improvements

1. **Multi-Turn Optimization**: Better conversation state management
2. **Custom Provider Integration**: Plugin system for new AI providers
3. **Conversation Analytics**: Usage patterns and optimization insights
4. **Enhanced Streaming**: Progressive markdown rendering
5. **Conversation Branching**: Multiple conversation paths

## 📚 Related Features

- [Memory System](memory.md) - Context management and assembly
- [Task Routing](task-routing.md) - Intelligent task classification
- [AI Agents](agent-architecture.md) - Agent-driven conversation handling
- [Nova Sonic Integration](nova-sonic-integration-layer.md) - Voice conversation support