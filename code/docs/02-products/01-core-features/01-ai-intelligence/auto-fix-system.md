# Auto-Fix System Specification

_Feature ID: CORE-AUTOFIX-001_  
_Last updated: July 17, 2025_

## Overview

An intelligent system that automatically fixes common code issues with one click, while providing transparency about what changes are being made. This system includes pattern recognition to find and fix similar issues across the entire codebase and integrates with the memory system for context-aware fixes.

## Core Capabilities

### Memory-Enhanced Fix Categories

```typescript
enum FixCategory {
  SECURITY = 'security',
  ERROR_HANDLING = 'errorHandling',
  PERFORMANCE = 'performance',
  CODE_QUALITY = 'codeQuality',
  DOCUMENTATION = 'documentation',
  DEPENDENCIES = 'dependencies'
}

interface MemoryEnhancedFix {
  category: FixCategory;
  confidence: 'high' | 'medium' | 'low';
  contextAware: boolean;
  userPreferences: any;
  projectPatterns: string[];
  requiresReview: boolean;
}
```

### Fix Types by Category

| Category | Fix Type | Confidence | Memory Integration |
|----------|----------|------------|-------------------|
| **Security** | Exposed secrets | High | Uses project env patterns |
| **Security** | SQL injection | High | Learns from DB context |
| **Security** | XSS vulnerabilities | Medium | Applies framework patterns |
| **Error Handling** | Unhandled promises | High | Learns from user style |
| **Error Handling** | Missing try-catch | High | Follows project conventions |
| **Performance** | Bundle size | Medium | Uses tech stack context |
| **Performance** | N+1 queries | Low | Learns from DB patterns |
| **Code Quality** | Dead code | High | Respects project structure |
| **Documentation** | Missing JSDoc | High | Follows project doc style |

## Memory System Integration

### Context-Aware Fix Generation

```typescript
class MemoryEnhancedAutoFixer {
  constructor(
    private memoryService: MemoryService,
    private fixEngine: AutoFixEngine
  ) {}

  async generateFix(
    issue: CodeIssue,
    projectId: number,
    userId: string
  ): Promise<ContextAwareFix> {
    // Assemble relevant context for fix generation
    const context = await this.memoryService.assembleContext({
      userRequest: `fix ${issue.type} in ${issue.filePath}`,
      taskType: 'code_review',
      projectId,
      userId,
      tokenBudget: 1500
    });

    // Generate fix with memory context
    const baseFix = await this.fixEngine.generateFix(issue);
    
    // Enhance fix with context
    const enhancedFix = await this.enhanceFixWithContext(baseFix, context);

    // Record fix pattern for learning
    await this.recordFixPattern(enhancedFix, context);

    return enhancedFix;
  }

  private async enhanceFixWithContext(
    fix: Fix,
    context: any
  ): Promise<ContextAwareFix> {
    const userPreferences = context.personal;
    const projectPatterns = context.code?.code_patterns || [];
    const techStack = context.technical?.tech_stack || {};

    return {
      ...fix,
      contextEnhanced: true,
      userStyle: userPreferences.codingStyle,
      projectPatterns,
      techStackAware: techStack,
      confidence: this.calculateContextAwareConfidence(fix, context)
    };
  }
}
```

### Pattern Learning from Memory

```typescript
class FixPatternLearner {
  constructor(private memoryService: MemoryService) {}

  async learnFromSuccessfulFix(
    fix: ContextAwareFix,
    projectId: number,
    userId: string
  ): Promise<void> {
    // Extract learnable patterns
    const pattern = {
      issueType: fix.issue.type,
      fixApproach: fix.approach,
      userStyle: fix.userStyle,
      projectContext: fix.projectPatterns,
      successRate: 1.0,
      timestamp: new Date().toISOString()
    };

    // Update code context with successful pattern
    await this.updateCodeContext(projectId, {
      successful_fix_patterns: [pattern]
    });

    // Record interaction for memory learning
    await this.memoryService.recordInteraction({
      userId,
      projectId,
      request: `fix ${fix.issue.type}`,
      response: `Applied ${fix.approach} fix successfully`,
      taskType: 'auto_fix',
      outcome: 'success',
      learningPoints: [
        `Fix type: ${fix.issue.type}`,
        `Approach: ${fix.approach}`,
        `User style: ${fix.userStyle?.preferred_patterns?.join(', ')}`
      ]
    });
  }
}
```

## User Experience

### Memory-Informed Fix Preview

```
┌─────────────────────────────────────────┐
│  🔧 Smart Fix: Exposed API Key          │
├─────────────────────────────────────────┤
│                                         │
│  I remember you prefer environment      │
│  variables in .env files. Here's what   │
│  I'll do based on your project:         │
│                                         │
│  1. Create .env file (matches your      │
│     project structure)                  │
│  2. Move API key to .env                │
│  3. Update code to use process.env      │
│     (TypeScript style, as preferred)    │
│  4. Add .env to .gitignore              │
│  5. Create .env.example                 │
│                                         │
│  📝 Code Changes:                       │
│  ┌─────────────────────────────────┐   │
│  │ config.ts                       │   │
│  ├─────────────────────────────────┤   │
│  │ - const API_KEY = "sk-abc123"  │   │
│  │ + const API_KEY = process.env. │   │
│  │   OPENAI_KEY as string;        │   │
│  └─────────────────────────────────┘   │
│                                         │
│  Impact: +8% readiness score            │
│  Confidence: High (learned from         │
│  3 similar fixes in this project)       │
│                                         │
│  [Apply Fix] [Show Details] [Skip]      │
│                                         │
└─────────────────────────────────────────┘
```

### Intelligent Similar Issues Detection

```
┌─────────────────────────────────────────┐
│  🔍 Pattern Recognition Active          │
├─────────────────────────────────────────┤
│                                         │
│  Based on your project patterns, I      │
│  found 3 similar hardcoded secrets:     │
│                                         │
│  ✓ config.ts:15 - DATABASE_URL         │
│  ✓ auth.ts:8 - JWT_SECRET              │
│  ✓ email.ts:22 - SMTP_PASSWORD         │
│                                         │
│  All match your Express.js + TypeScript │
│  project structure. I'll use the same   │
│  patterns you've used before.           │
│                                         │
│  Previous fixes: 92% success rate       │
│  Estimated time: ~15 minutes total      │
│                                         │
│  [Fix All Similar] [Review Each]        │
│                                         │
└─────────────────────────────────────────┘
```

## Technical Implementation

### Memory-Enhanced Fix Engine

```typescript
class ContextAwareFixEngine {
  constructor(
    private memoryService: MemoryService,
    private fixers: Map<FixCategory, CategoryFixer>
  ) {}
  
  async generateFix(
    issue: CodeIssue,
    projectId: number,
    userId: string
  ): Promise<ContextAwareFix> {
    // Get relevant context
    const context = await this.memoryService.assembleContext({
      userRequest: `fix ${issue.type} in ${issue.filePath}`,
      taskType: 'code_review',
      projectId,
      userId,
      tokenBudget: 1500
    });

    // Get appropriate fixer
    const fixer = this.fixers.get(issue.category);
    if (!fixer) {
      throw new Error(`No fixer available for ${issue.category}`);
    }
    
    // Generate context-aware fix
    const fix = await fixer.generateContextAwareFix(issue, context);
    
    // Enhance with learned patterns
    const enhancedFix = await this.enhanceWithLearnedPatterns(fix, context);
    
    // Validate fix won't break code
    const validation = await this.validateFix(enhancedFix, context);
    if (!validation.isValid) {
      enhancedFix.requiresReview = true;
      enhancedFix.warnings = validation.warnings;
    }
    
    return enhancedFix;
  }

  private async enhanceWithLearnedPatterns(
    fix: Fix,
    context: any
  ): Promise<ContextAwareFix> {
    const learnedPatterns = context.code?.successful_fix_patterns || [];
    const userStyle = context.personal?.codingStyle || {};
    
    // Apply learned patterns
    const relevantPatterns = learnedPatterns.filter(pattern => 
      pattern.issueType === fix.issue.type
    );

    if (relevantPatterns.length > 0) {
      const bestPattern = relevantPatterns.reduce((best, current) => 
        current.successRate > best.successRate ? current : best
      );

      // Apply successful pattern
      fix.approach = bestPattern.fixApproach;
      fix.confidence = Math.min(fix.confidence + 0.2, 1.0);
      fix.learnedFromPattern = bestPattern;
    }

    // Apply user style preferences
    if (userStyle.preferred_patterns?.includes('typescript')) {
      fix.useTypeScript = true;
    }

    return fix as ContextAwareFix;
  }
}
```

### Security Fixer with Memory

```typescript
class MemoryEnhancedSecurityFixer implements CategoryFixer {
  constructor(private memoryService: MemoryService) {}

  async generateContextAwareFix(
    issue: SecurityIssue,
    context: any
  ): Promise<ContextAwareFix> {
    const userStyle = context.personal?.codingStyle || {};
    const projectPatterns = context.code?.code_patterns || [];
    const techStack = context.technical?.tech_stack || {};

    switch (issue.type) {
      case 'exposed_secret':
        return this.fixExposedSecretWithContext(issue, context);
      case 'sql_injection':
        return this.fixSqlInjectionWithContext(issue, context);
      case 'xss_vulnerability':
        return this.fixXssWithContext(issue, context);
      default:
        throw new Error(`Unknown security issue: ${issue.type}`);
    }
  }
  
  private async fixExposedSecretWithContext(
    issue: SecurityIssue,
    context: any
  ): Promise<ContextAwareFix> {
    const userStyle = context.personal?.codingStyle || {};
    const techStack = context.technical?.tech_stack || {};
    const projectPatterns = context.code?.code_patterns || [];

    const secretName = this.extractSecretName(issue);
    const envVarName = this.toEnvVarName(secretName);
    
    // Check if project already has .env patterns
    const hasEnvPattern = projectPatterns.some(p => 
      p.pattern.includes('.env') || p.pattern.includes('process.env')
    );

    // Adapt to TypeScript if preferred
    const useTypeScript = userStyle.language_preferences?.includes('typescript') ||
                          techStack.frontend?.includes('typescript') ||
                          techStack.backend?.includes('typescript');

    // Generate context-aware fix steps
    const steps: FixStep[] = [
      {
        type: 'create_file',
        path: '.env',
        content: `${envVarName}=${issue.exposedValue}\n`,
        mode: hasEnvPattern ? 'append' : 'create'
      },
      {
        type: 'create_file',
        path: '.env.example',
        content: `${envVarName}=your_${secretName}_here\n`,
        mode: hasEnvPattern ? 'append' : 'create'
      },
      {
        type: 'modify_file',
        path: issue.filePath,
        changes: [{
          from: issue.problematicCode,
          to: useTypeScript 
            ? `process.env.${envVarName} as string`
            : `process.env.${envVarName}`
        }]
      }
    ];

    // Add .gitignore update if not already present
    if (!hasEnvPattern) {
      steps.push({
        type: 'modify_file',
        path: '.gitignore',
        changes: [{
          from: 'EOF',
          to: '\n.env\n'
        }]
      });
    }
    
    return {
      id: this.generateFixId(),
      issue: issue,
      steps: steps,
      impact: this.calculateImpact(issue),
      confidence: hasEnvPattern ? 0.98 : 0.95, // Higher confidence if pattern exists
      requiresReview: false,
      contextAware: true,
      userStyle: userStyle,
      projectPatterns: projectPatterns,
      appliedPattern: hasEnvPattern ? 'existing_env_pattern' : 'new_env_pattern'
    };
  }
}
```

### Pattern Recognition with Memory

```typescript
class MemoryEnhancedPatternRecognizer {
  constructor(private memoryService: MemoryService) {}

  async recognizePatternWithContext(
    issue: CodeIssue,
    projectId: number,
    userId: string
  ): Promise<ContextAwarePattern> {
    // Get code context from memory
    const context = await this.memoryService.assembleContext({
      userRequest: `find similar issues to ${issue.type}`,
      taskType: 'code_review',
      projectId,
      userId,
      tokenBudget: 1000
    });

    const codeContext = context.code;
    const userStyle = context.personal?.codingStyle;

    // Base pattern recognition
    const basePattern = this.extractBasePattern(issue);
    
    // Enhance with memory context
    const enhancedPattern = {
      ...basePattern,
      userStyleVariations: this.generateUserStyleVariations(basePattern, userStyle),
      projectSpecificPatterns: this.extractProjectPatterns(codeContext),
      historicalMatches: this.findHistoricalMatches(basePattern, codeContext)
    };

    return enhancedPattern;
  }

  async scanForPatternWithMemory(
    pattern: ContextAwarePattern,
    projectFiles: ProjectFile[]
  ): Promise<SimilarIssue[]> {
    const matches: SimilarIssue[] = [];
    
    for (const file of projectFiles) {
      const fileAST = await this.parseFile(file);
      
      this.traverse(fileAST, (node) => {
        // Check base pattern
        let similarity = this.calculateBaseSimilarity(node, pattern);
        
        // Boost similarity for user style matches
        if (this.matchesUserStyle(node, pattern.userStyleVariations)) {
          similarity += 0.2;
        }
        
        // Boost similarity for project pattern matches
        if (this.matchesProjectPattern(node, pattern.projectSpecificPatterns)) {
          similarity += 0.1;
        }
        
        // Boost similarity for historical matches
        if (this.matchesHistoricalPattern(node, pattern.historicalMatches)) {
          similarity += 0.15;
        }
        
        if (similarity > 0.8) {
          matches.push({
            file: file.path,
            location: node.location,
            code: node.source,
            similarity: similarity,
            issue: this.inferIssue(node, pattern),
            contextFactors: {
              userStyleMatch: this.matchesUserStyle(node, pattern.userStyleVariations),
              projectPatternMatch: this.matchesProjectPattern(node, pattern.projectSpecificPatterns),
              historicalMatch: this.matchesHistoricalPattern(node, pattern.historicalMatches)
            }
          });
        }
      });
    }
    
    return matches.sort((a, b) => b.similarity - a.similarity);
  }
}
```

## Memory-Enhanced Success Metrics

- **Fix Success Rate**: >95% of auto-fixes work without errors
- **Pattern Recognition**: 85% accuracy with memory enhancement (up from 80%)
- **Context Relevance**: 92% of fixes match user/project patterns
- **Time Saved**: Average 12 minutes saved per fix (up from 10)
- **User Trust**: 93% of users apply suggested fixes (up from 90%)
- **Learning Effectiveness**: 88% of repeat similar issues use learned patterns

## Integration with Memory System

### Pre-Fix Context Assembly
```typescript
// Enhanced context assembly for auto-fix
const fixContext = await memoryService.assembleContext({
  userRequest: `fix ${issue.type} in ${issue.filePath}`,
  taskType: 'code_review',
  projectId: project.id,
  userId: user.id,
  tokenBudget: 1500
});
```

### Post-Fix Learning
```typescript
// Record successful fix for pattern learning
await memoryService.recordInteraction({
  userId: user.id,
  projectId: project.id,
  request: `fix ${issue.type}`,
  response: `Applied ${fix.approach} successfully`,
  taskType: 'auto_fix',
  outcome: 'success',
  learningPoints: [
    `Fix approach: ${fix.approach}`,
    `User style applied: ${fix.userStyle}`,
    `Pattern confidence: ${fix.confidence}`
  ]
});
```

## Safety Features

1. **Memory-Aware Backup**: Backup strategy based on user preferences
2. **Context-Aware Validation**: Validation considers project patterns
3. **User Style Rollback**: Rollback respects user coding style
4. **Pattern-Based Review**: Review mode for new patterns
5. **Memory-Enhanced Testing**: Run tests with awareness of test patterns

## Future Enhancements

1. **AI-Powered Context Fixes**: Use LLMs with rich memory context
2. **Cross-Project Pattern Sharing**: Share successful patterns across projects
3. **Team Memory Integration**: Learn from team fixing patterns
4. **Predictive Fix Suggestions**: Suggest fixes before issues occur
5. **Memory-Driven Fix Scheduling**: Queue fixes based on context priority

## Implementation References

### Frontend Components
- **AI Code Analyzer**: `/ide/src/renderer/features/quality/AiCodeAnalyzer.tsx`
  - React component with auto-fix capabilities
  - `CodeFinding` interface with `autoFixable` boolean (line 46)
  - `applyFix` method for applying auto-fixes (lines 235-258)
  - Preview and apply fix functionality (lines 339-348, 370-373)
  - Auto-fix buttons in UI (lines 408-411)

### Backend Services
- **Code Analysis Service**: `/backend/src/services/code-analysis.service.ts`
  - Pattern detection for missing error handling, high complexity, deep nesting
  - Suggestion generation for fixes (lines 477-530)
  - AI-powered analysis with fix recommendations (lines 670-720)

- **Local Code Analysis Service**: `/backend/src/services/local-code-analysis.service.ts`
  - `DetectedPattern` interface with `suggestion` field (line 74)
  - Pattern detection for various code issues (lines 957-1010)
  - Suggestions for fixes in detected patterns

- **Brutal Honesty Service**: `/backend/src/services/brutal-honesty-messaging.service.ts`
  - Helpful guidance for fixing detected issues (lines 271-341)
  - Next steps generation for fixing problems (lines 423-449)
  - Automated recommendations for code improvements

### IDE Services
- **Project Analysis Service**: `/ide/src/renderer/services/ProjectAnalysisService.ts`
  - `CodeIssue` interface with `autoFixable` boolean (line 28)
  - Security and performance pattern detection with auto-fix suggestions (lines 1468-1545)
  - Auto-fix capability flags for different types of issues

### Data Models
- **AI Code Suggestions**: `/legacy/fastapi-backend/app/models/code_analysis.py`
  - `AICodeSuggestion` model with fix tracking (lines 112-149)
  - Original code, suggested code, and implementation tracking
  - Confidence scoring for AI-generated fixes

### Implementation Status
- **✅ Implemented**: Basic auto-fix UI components, pattern detection systems, fix suggestion generation, confidence scoring, preview functionality
- **🚧 Partially Implemented**: Memory-enhanced fix generation, cross-project pattern sharing, advanced fix validation
- **❌ Missing**: Actual fix application engine, fix rollback mechanisms, automated testing of fixes, real-time fix validation, production-ready auto-fix workflows

### Auto-Fix Categories Implemented
- **Security Fixes**: Pattern detection for hardcoded credentials, SQL injection, XSS vulnerabilities
- **Error Handling Fixes**: Detection of async functions without error handling, missing try-catch blocks
- **Performance Fixes**: Pattern matching for inefficient loops, large bundles, N+1 queries
- **Code Quality Fixes**: High complexity functions, dead code, poor naming
- **Documentation Fixes**: Missing JSDoc, outdated documentation

## Related Features

- [Memory System](../01-agents/memory.md) - Core memory architecture
- [Task Routing](../01-agents/task-routing.md) - Auto-fix task routing
- [Code Analysis](../04-ide-core/code-analysis.md) - Issue detection
- [Progress Tracking](../02-dashboard/progress-tracking.md) - Fix progress tracking