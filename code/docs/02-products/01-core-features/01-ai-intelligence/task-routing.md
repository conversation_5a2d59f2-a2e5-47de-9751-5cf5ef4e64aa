# 🎯 Task Routing & Type Specifications

_Feature ID: CORE-ROUTING-001_  
_Last updated: July 17, 2025_

## 🌟 Overview

KAPI's intelligent task routing system automatically directs user requests to the most appropriate agent and model based on task complexity, type, and context. This system ensures optimal performance, cost efficiency, and quality outcomes.

---

## 📋 Task Type Definitions

### 🎯 **Core Task Types**

| Task Type | Description | Complexity | Default Agent | Model Priority |
|-----------|-------------|------------|---------------|----------------|
| **chat** | General conversation, onboarding, questions | Low | Evidence Collection | gpt-4.1 → nova-lite → gemini-2.0-flash |
| **code_review** | Code analysis, review, feedback | Medium | Quality Assurance | gemini-2.5-flash → o4-mini → claude-3.7-sonnet |
| **code_gen_big** | Large code generation, full features | High | Code Generation | claude-3.7-sonnet → o4-mini → nova-premier |
| **code_gen_agentic** | Incremental, iterative code generation | Medium | Code Generation | claude-3.7-sonnet → gpt-4.1 → nova-premier |
| **svg_mockup** | Visual design and mockup creation | High | Documentation | claude-3.7-sonnet → o4-mini → nova-premier |
| **slides** | Presentation and documentation creation | High | Documentation | claude-3.7-sonnet → o4-mini → nova-premier |
| **test_cases** | Test generation and validation | Medium | Quality Assurance | claude-3.7-sonnet → gpt-4.1 → nova-premier |
| **general** | Fallback for unclassified tasks | Variable | Evidence Collection | gpt-4.1 → nova-lite → gemini-2.0-flash |

---

## 🧠 Task Classification System

### 🎯 **Intent Recognition Patterns**

```typescript
interface TaskClassificationRules {
  // Code-related keywords
  codeGeneration: [
    'implement', 'create', 'build', 'generate', 'write code', 
    'add function', 'create component', 'build feature'
  ];
  
  // Review-related keywords
  codeReview: [
    'review', 'check', 'analyze', 'audit', 'improve',
    'refactor', 'optimize', 'fix', 'debug'
  ];
  
  // Documentation keywords
  documentation: [
    'document', 'explain', 'create docs', 'write spec',
    'generate slides', 'create presentation', 'API docs'
  ];
  
  // Testing keywords
  testing: [
    'test', 'unit test', 'integration test', 'coverage',
    'test cases', 'validate', 'verify'
  ];
  
  // Visual/Design keywords
  design: [
    'mockup', 'design', 'svg', 'diagram', 'wireframe',
    'UI', 'layout', 'visual'
  ];
  
  // Conversation keywords
  conversation: [
    'help', 'explain', 'what is', 'how to', 'question',
    'clarify', 'understand', 'learn'
  ];
}
```

### 🔍 **Complexity Assessment Algorithm**

```typescript
function assessTaskComplexity(request: string, context: ProjectContext): ComplexityScore {
  let score = 0;
  
  // Base complexity factors
  const factors = {
    // Code complexity indicators
    multipleFiles: /multiple files|several files|many files/.test(request) ? 3 : 0,
    architectureChange: /architecture|redesign|refactor/.test(request) ? 4 : 0,
    integration: /integrate|connect|api/.test(request) ? 2 : 0,
    
    // Context complexity
    largeCodebase: context.fileCount > 100 ? 2 : 0,
    complexTechStack: context.techStackComplexity > 7 ? 1 : 0,
    
    // Request complexity
    multipleRequirements: (request.match(/and|also|plus|additionally/g) || []).length,
    ambiguousLanguage: /maybe|perhaps|possibly|might/.test(request) ? 1 : 0,
    
    // Scale indicators
    production: /production|deploy|scale/.test(request) ? 2 : 0,
    security: /security|auth|permission/.test(request) ? 2 : 0
  };
  
  score = Object.values(factors).reduce((sum, val) => sum + val, 0);
  
  // Normalize to 0-10 scale
  return Math.min(10, Math.max(0, score));
}
```

---

## 🎯 Agent Routing Logic

### 🎯 **Primary Agent Selection**

```typescript
interface AgentRoutingRules {
  // Evidence Collection Agent
  evidenceCollection: {
    keywords: ['requirements', 'understand', 'analyze', 'clarify', 'what', 'how', 'why'];
    complexity: [0, 1, 2, 3]; // Simple to medium
    taskTypes: ['chat', 'general'];
    contextNeeded: true;
  };
  
  // Documentation Specialist
  documentation: {
    keywords: ['document', 'spec', 'slides', 'diagram', 'explain', 'architecture'];
    complexity: [3, 4, 5, 6]; // Medium to high
    taskTypes: ['slides', 'svg_mockup', 'general'];
    contextNeeded: true;
  };
  
  // Code Generation Agent
  codeGeneration: {
    keywords: ['implement', 'create', 'build', 'generate', 'code', 'function'];
    complexity: [4, 5, 6, 7, 8]; // Medium to very high
    taskTypes: ['code_gen_big', 'code_gen_agentic'];
    contextNeeded: true;
  };
  
  // Quality Assurance Agent
  qualityAssurance: {
    keywords: ['test', 'review', 'check', 'validate', 'security', 'optimize'];
    complexity: [3, 4, 5, 6, 7]; // Medium to high
    taskTypes: ['code_review', 'test_cases'];
    contextNeeded: true;
  };
}
```

### 🔄 **Agent Handoff Triggers**

```typescript
interface HandoffTriggers {
  // From Evidence Collection to Documentation
  evidenceToDocumentation: {
    conditions: ['requirements_complete', 'architecture_needed', 'complex_system'];
    contextTransfer: ['user_requirements', 'business_context', 'technical_constraints'];
  };
  
  // From Documentation to Code Generation
  documentationToCode: {
    conditions: ['specs_complete', 'implementation_requested', 'architecture_defined'];
    contextTransfer: ['technical_specs', 'architecture_diagrams', 'api_definitions'];
  };
  
  // From Code Generation to Quality Assurance
  codeToQuality: {
    conditions: ['code_complete', 'testing_needed', 'review_required'];
    contextTransfer: ['generated_code', 'implementation_notes', 'known_issues'];
  };
  
  // Cross-agent collaboration
  parallelExecution: {
    conditions: ['large_project', 'multiple_deliverables', 'complex_requirements'];
    coordination: ['shared_context', 'progress_updates', 'dependency_management'];
  };
}
```

---

## 🎯 Model Selection Strategy

### 🎯 **Model Prioritization Logic**

```typescript
interface ModelSelectionStrategy {
  // High-performance models for complex tasks
  complexTasks: {
    primary: ['claude-3.7-sonnet', 'o4-mini', 'gpt-4.1'];
    fallback: ['nova-premier', 'claude-3.5-sonnet', 'gemini-2.5-flash'];
    criteria: ['complexity > 6', 'code_generation', 'architecture_design'];
  };
  
  // Balanced models for medium tasks
  mediumTasks: {
    primary: ['gemini-2.5-flash', 'gpt-4.1', 'nova-premier'];
    fallback: ['claude-3.5-sonnet', 'nova-pro', 'gemini-2.0-flash'];
    criteria: ['complexity 3-6', 'code_review', 'documentation'];
  };
  
  // Efficient models for simple tasks
  simpleTasks: {
    primary: ['gpt-4.1', 'nova-lite', 'gemini-2.0-flash'];
    fallback: ['nova-micro', 'gemini-2.0-flash-lite'];
    criteria: ['complexity < 3', 'chat', 'simple_questions'];
  };
}
```

### 🎯 **Dynamic Model Switching**

```typescript
interface DynamicModelRules {
  // Context-aware switching
  contextSwitching: {
    largeCodebase: 'upgrade_to_premium', // More context capacity
    multipleFiles: 'use_long_context_model',
    realTimeNeeds: 'prioritize_speed',
    costConstraints: 'prioritize_efficiency'
  };
  
  // Performance-based switching
  performanceSwitching: {
    highErrorRate: 'upgrade_model',
    slowResponse: 'switch_to_faster',
    tokenLimit: 'use_higher_capacity',
    userFeedback: 'adjust_based_on_rating'
  };
  
  // Fallback strategies
  fallbackStrategies: {
    modelUnavailable: 'next_in_priority_list',
    rateLimited: 'switch_to_alternative',
    errorThreshold: 'downgrade_and_retry',
    timeoutOccurred: 'use_faster_model'
  };
}
```

---

## 🎯 Context Assembly Rules

### 🎯 **Context Prioritization**

```typescript
interface ContextPriority {
  // High priority context (always included)
  critical: {
    weight: 100;
    items: ['current_task', 'user_intent', 'active_file'];
    tokenBudget: 500;
  };
  
  // Medium priority context (included if space allows)
  important: {
    weight: 70;
    items: ['project_structure', 'recent_changes', 'tech_stack'];
    tokenBudget: 800;
  };
  
  // Low priority context (included for complex tasks)
  supplementary: {
    weight: 30;
    items: ['git_history', 'related_files', 'dependencies'];
    tokenBudget: 400;
  };
  
  // Background context (included if abundant space)
  background: {
    weight: 10;
    items: ['project_description', 'team_preferences', 'coding_patterns'];
    tokenBudget: 300;
  };
}
```

### 🎯 **Memory Integration Points**

```typescript
interface MemoryIntegration {
  // Pre-request memory assembly
  preRequest: {
    personalContext: 'retrieve_user_preferences',
    projectContext: 'load_project_memory',
    technicalContext: 'gather_architecture_info',
    taskContext: 'analyze_current_work'
  };
  
  // Post-response memory updates
  postResponse: {
    userFeedback: 'update_preference_model',
    codeChanges: 'update_project_memory',
    taskProgress: 'update_task_context',
    learningPoints: 'update_technical_memory'
  };
  
  // Continuous learning
  continuousLearning: {
    patternRecognition: 'identify_recurring_patterns',
    errorCorrection: 'learn_from_mistakes',
    successFactors: 'reinforce_successful_approaches',
    contextOptimization: 'improve_context_selection'
  };
}
```

---

## 🎯 Error Handling & Fallback

### 🎯 **Error Classification**

```typescript
interface ErrorHandling {
  // Classification of errors
  errorTypes: {
    modelError: {
      symptoms: ['api_timeout', 'rate_limit', 'model_unavailable'];
      response: 'switch_to_fallback_model';
      recovery: 'retry_with_different_model';
    };
    
    contextError: {
      symptoms: ['token_limit_exceeded', 'context_corruption'];
      response: 'compress_context';
      recovery: 'prioritize_essential_context';
    };
    
    taskError: {
      symptoms: ['unclear_intent', 'ambiguous_request'];
      response: 'request_clarification';
      recovery: 'evidence_collection_agent';
    };
    
    systemError: {
      symptoms: ['service_unavailable', 'network_error'];
      response: 'graceful_degradation';
      recovery: 'offline_mode_if_available';
    };
  };
  
  // Recovery strategies
  recoveryStrategies: {
    immediateRetry: 'for_transient_errors',
    modelDowngrade: 'for_capacity_errors',
    contextReduction: 'for_size_errors',
    agentHandoff: 'for_specialization_errors',
    userEscalation: 'for_unrecoverable_errors'
  };
}
```

---

## 🎯 Performance Optimization

### 🎯 **Caching Strategy**

```typescript
interface CachingRules {
  // Response caching
  responseCache: {
    cacheableRequests: ['documentation', 'code_examples', 'common_patterns'];
    ttl: 3600; // 1 hour
    invalidationTriggers: ['code_changes', 'context_updates'];
  };
  
  // Context caching
  contextCache: {
    projectContext: 'cache_until_structure_changes',
    userPreferences: 'cache_until_explicitly_updated',
    techStack: 'cache_until_dependencies_change'
  };
  
  // Model result caching
  modelCache: {
    similarQueries: 'cache_responses_for_similar_requests',
    templateGeneration: 'cache_common_code_patterns',
    documentationQueries: 'cache_explanatory_responses'
  };
}
```

### 🎯 **Load Balancing**

```typescript
interface LoadBalancing {
  // Request distribution
  distributionStrategy: {
    roundRobin: 'for_similar_complexity_tasks',
    weightedRandom: 'based_on_model_performance',
    leastConnections: 'for_real_time_requests'
  };
  
  // Model utilization
  modelUtilization: {
    highDemand: 'distribute_across_available_models',
    lowDemand: 'concentrate_on_efficient_models',
    peak: 'scale_up_premium_models'
  };
  
  // Geographic distribution
  geographicRouting: {
    latencyOptimization: 'route_to_nearest_available',
    costOptimization: 'route_to_cheapest_region',
    complianceRequirements: 'route_based_on_data_residency'
  };
}
```

---

## 🎯 Monitoring & Analytics

### 🎯 **Performance Metrics**

```typescript
interface PerformanceMetrics {
  // Task success metrics
  taskMetrics: {
    completionRate: 'percentage_of_successful_completions',
    accuracyScore: 'quality_rating_from_users',
    responseTime: 'average_time_to_completion',
    contextEfficiency: 'relevant_context_percentage'
  };
  
  // Agent performance
  agentMetrics: {
    specializationScore: 'performance_in_assigned_domain',
    handoffSuccess: 'successful_agent_transitions',
    errorRate: 'percentage_of_failed_requests',
    userSatisfaction: 'rating_for_agent_responses'
  };
  
  // System metrics
  systemMetrics: {
    throughput: 'requests_processed_per_second',
    latency: 'average_response_time',
    availability: 'system_uptime_percentage',
    costEfficiency: 'performance_per_dollar_spent'
  };
}
```

---

## 🎯 Integration Points

### 🎯 **Service Integration**

```typescript
interface ServiceIntegration {
  // Core services
  coreServices: {
    conversationService: 'main_request_handler',
    taskStrategyService: 'task_classification_and_routing',
    modelUsageService: 'usage_tracking_and_billing',
    memoryService: 'context_management_and_learning'
  };
  
  // External integrations
  externalIntegrations: {
    gitService: 'code_context_and_history',
    fileAnalysisService: 'code_structure_understanding',
    documentationService: 'spec_generation_and_updates',
    qualityService: 'code_review_and_testing'
  };
  
  // Memory integration
  memoryIntegration: {
    contextAssembly: 'gather_relevant_context_for_task',
    learningUpdates: 'update_models_based_on_outcomes',
    preferenceTracking: 'maintain_user_and_project_preferences',
    patternRecognition: 'identify_and_reuse_successful_patterns'
  };
}
```

---

## 🎯 Configuration Management

### 🎯 **Runtime Configuration**

```typescript
interface RuntimeConfig {
  // Dynamic routing adjustments
  routingAdjustments: {
    loadBasedRouting: 'adjust_based_on_current_load',
    performanceBasedRouting: 'route_to_best_performing_models',
    costBasedRouting: 'optimize_for_cost_efficiency',
    userPreferenceRouting: 'respect_user_model_preferences'
  };
  
  // Model availability
  modelAvailability: {
    healthChecks: 'continuous_model_health_monitoring',
    fallbackChains: 'predefined_fallback_sequences',
    capacityAwareness: 'route_based_on_available_capacity',
    regionalAvailability: 'consider_geographic_availability'
  };
  
  // Feature flags
  featureFlags: {
    experimentalRouting: 'enable_experimental_routing_algorithms',
    advancedCaching: 'enable_advanced_caching_strategies',
    predictiveRouting: 'enable_predictive_task_routing',
    memoryOptimization: 'enable_memory_optimization_features'
  };
}
```

---

## Implementation References

### Core Task Routing Service
- **Task Routing Service**: `/backend/src/services/task-routing.service.ts`
  - Main task routing service with intelligent routing logic (lines 1-555)
  - Task analysis for complexity, keywords, intent, and context (lines 118-155)
  - Agent selection with scoring and fit assessment (lines 324-354)
  - Context assembly with memory service integration (lines 305-319)
  - Fallback handling for failed analysis (lines 100-112)

### Task Classification and API
- **Task Router API**: `/backend/src/routes/task-router.ts`
  - REST API endpoint implementing intelligent task routing (lines 1-367)
  - Gemini function calling for task classification (lines 16-114)
  - Streaming support for real-time responses (lines 292-315)
  - Nova Sonic integration for service-to-service calls (lines 121-190)

- **Unified Conversation Service**: `/backend/src/services/conversation/unified-conversation.service.ts`
  - Intelligent task classification using Gemini LLM (lines 584-738)
  - Context-aware routing to appropriate strategies (lines 505-579)
  - Memory integration for context optimization (lines 506-541)
  - Multi-layer fallback logic for robust classification (lines 695-737)

### Strategy Pattern Implementation
- **Task Strategy Registry**: `/backend/src/services/conversation/task-strategy-registry.ts`
  - Registry system managing task-specific strategies (lines 1-75)
  - Registered strategies: Chat, Onboarding, Code Generation, Testing, Slides, SVG Mockup, Multimodal, Progressive Improvement

### Model Selection and Load Balancing
- **AI Service**: `/backend/src/services/ai/index.ts`
  - Central AI service for model selection and provider routing (lines 1-299)
  - Provider detection from model names (lines 93-112)
  - Model mapping to appropriate AI services (lines 118-169)
  - Streaming support across providers (lines 175-295)
  - Cost calculation based on token usage and model pricing (lines 42-77)

### Configuration-Driven Routing
- **Configuration File**: `/backend/config/config.yaml`
  - Task descriptions and routing targets (lines 5-13)
  - Model priorities and fallback chains (lines 16-57)
  - Agent routing rules (lines 87-114)
  - Memory system configuration (lines 117-142)

### Context Assembly and Memory Integration
- **Context Manager Service**: `/backend/src/services/memory/context-manager.service.ts`
  - Context assembly and prioritization for routing (lines 1-219)
  - Context providers registration (lines 22-39)
  - Token budget-based context assembly (lines 50-110)
  - Priority management for context selection (lines 90-95)
  - Usage tracking for optimization (lines 115-123)

### Error Handling and Fallback Systems
- **Model Fallbacks**: Configuration-driven cascading model priorities
- **Agent Fallbacks**: Multi-agent fallback chains in task routing service
- **Provider Fallbacks**: Automatic provider switching in AI service
- **Context Fallbacks**: Emergency classification fallbacks in conversation service

### Performance Optimization Features
- **Load Balancing**: Round-robin distribution, weighted random selection, geographic routing
- **Caching**: Response caching, context caching, model result caching
- **Dynamic Routing**: Real-time model health monitoring, performance-based switching
- **Context Optimization**: Memory-enhanced context assembly and prioritization

### Implementation Quality
- **Architecture Patterns**: Strategy Pattern, Observer Pattern, Factory Pattern, Circuit Breaker Pattern
- **Configuration-Driven**: Flexible YAML-based routing rules
- **Comprehensive Error Handling**: Multiple fallback layers
- **Performance Optimization**: Caching, load balancing, dynamic routing
- **Memory Integration**: Context-aware routing with learning capabilities

---

*The task routing system ensures every request finds its perfect match - the right agent, the right model, at the right time.* 🎯