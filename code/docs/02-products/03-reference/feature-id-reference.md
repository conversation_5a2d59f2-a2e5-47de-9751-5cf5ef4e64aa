# Feature ID Quick Reference

## How to Use Feature IDs

### Format: `[CATEGORY]-[FEATURE]-[NUMBER]`

## Current Features (CORE-*)

### AI Intelligence
- `CORE-AI-001` - [Agent Architecture](../01-core-features/01-ai-intelligence/agent-architecture.md)
  AI Agent System - Your Development Team. Specialized AI agents for different development tasks with intelligent orchestration and management.

- `CORE-CONVERSATION-001` - [Unified Conversation Service](../01-core-features/01-ai-intelligence/unified-conversation-service.md)
  Manages all AI interactions across KAPI with multiple LLM providers. Provides standardized interface with conversation lifecycle and provider abstraction.

- `CORE-MEMORY-001` - [Memory System](../01-core-features/01-ai-intelligence/memory.md)
  KAPI Memory System - Persistent, intelligent storage for coding style, project context, and development patterns. Enables context-aware AI interactions across sessions.

- `CORE-AUTOFIX-001` - [Auto-Fix System](../01-core-features/01-ai-intelligence/auto-fix-system.md)
  Intelligent system that automatically fixes common code issues with one click. Leverages AI analysis to provide context-aware code improvements and suggestions.

- `CORE-NOVA-001` - [Nova Sonic Integration Layer](../01-core-features/01-ai-intelligence/nova-sonic-integration-layer.md)
  AI voice infrastructure for voice-to-code generation and audio processing. Enables natural language voice commands for development tasks.

- `CORE-ROUTING-001` - [Task Routing](../01-core-features/01-ai-intelligence/task-routing.md)
  Intelligent routing that directs requests to appropriate agents and models. Optimizes AI resource allocation based on task complexity and requirements.

- `CORE-INTERVIEW-001` - [Text Interview System](../01-core-features/01-ai-intelligence/text-interview-system.md)
  Memory-Enhanced Project Interview System - Personalized project intelligence through conversation. Conducts adaptive interviews to understand user needs and project context.

### Development Environment
- `CORE-IDE-001` - [IDE Fundamentals](../01-core-features/02-development-environment/ide-fundamentals.md)
  Essential development environment components forming the foundation of KAPI IDE. Provides core editing, navigation, and development workflow capabilities.

- `CORE-DEPLOY-001` - [Deployment Integration](../01-core-features/02-development-environment/deployment-integration.md)
  One-click deployment to popular hosting platforms with context-aware decisions. Automates deployment workflows with intelligent configuration management.

- `CORE-SEARCH-001` - [Search System](../01-core-features/02-development-environment/search.md)
  Unified search system combining traditional IDE search with AI-powered semantic understanding. Provides intelligent code discovery and contextual search results.

- `CORE-REALTIME-001` - [Real-Time Communication System](../01-core-features/02-development-environment/real-time-communication-system.md)
  WebSocket-based instant bidirectional communication across KAPI. Enables real-time collaboration, live updates, and instant feedback mechanisms.

### User Interface
- `CORE-DASHBOARD-001` - [Comprehensive Dashboard System](../01-core-features/03-user-interface/comprehensive-dashboard-system.md)
  Central command center with real-time project health and quality monitoring. Provides comprehensive view of project status with intelligent insights and recommendations.

- `CORE-PROFILE-001` - [User Profile & Personalization System](../01-core-features/03-user-interface/user-profile-personalization-system.md)
  Tailored experiences based on user behavior, preferences, and skill development. Adapts interface and recommendations to individual developer patterns and growth.

- `CORE-PROJECT-ONBOARDING-001` - [Project Onboarding System](../01-core-features/03-user-interface/project-onboarding-system.md)
  Intelligent interview-style experience for project analysis and understanding. Guides users through comprehensive project setup with contextual discovery.

- `UI-ONBOARDING-001` - [Intelligent Onboarding Experience](../01-core-features/03-user-interface/onboarding.md)
  Memory-driven progressive discovery through conversation-based learning. Introduces users to KAPI features through personalized, adaptive onboarding flows.

- `WIDGET-MEMORY-001` - [Memory Dashboard Widget](../01-core-features/03-user-interface/memory-dashboard-widget.md)
  Complete visibility and control over KAPI's conversation-based memory system. Provides interface for managing, viewing, and optimizing AI memory and context.

- `CORE-FEEDBACK-001` - [Brutal Honesty Message Generation](../01-core-features/03-user-interface/CORE-FEEDBACK-001.md)
  Honest, humorous feedback messages that tell developers the truth about code quality. Generates engaging, memorable messages that motivate improvement.

### Development Process
- `CORE-PROJECT-001` - [Project Management System](../01-core-features/04-development-process/project-management-system.md)
  Comprehensive project lifecycle management with AI assistance and quality monitoring. Provides intelligent project tracking, milestone management, and progress insights.

- `CORE-TESTGEN-001` - [Test Generation System](../01-core-features/04-development-process/test-generation-system.md)
  AI-powered test generation that creates comprehensive test suites from code analysis. Automatically generates unit, integration, and end-to-end tests with intelligent coverage.

- `CORE-DOCMGMT-001` - [Documentation Management System](../01-core-features/04-development-process/documentation-management-system.md)
  Interactive editing, multi-format support, and AI-powered generation. Maintains synchronized documentation with automatic updates and intelligent content generation.

- `CORE-DOCGEN-001` - [AST-Based Documentation Generation System](../01-core-features/04-development-process/ast-documentation-generation-system.md)
  Automated documentation generation using AST analysis and AI. Provides context menu integration, semantic search, and priority-based generation workflows.

- `CORE-TEMPLATE-001` - [Template & Code Generation System](../01-core-features/04-development-process/template-code-generation-system.md)
  Intelligent code scaffolding and project template management. Generates contextual code templates and boilerplate with smart customization capabilities.

- `CORE-METHODOLOGY-001` - [Backwards Build Methodology](../01-core-features/04-development-process/backwards-build-methodology.md)
  KAPI's core development philosophy starting with user outcomes. Guides development process from user needs backward to technical implementation.

### Quality Analysis
- `CORE-HEALTH-001` - [Health Monitoring System](../01-core-features/05-quality-analysis/03-health-monitoring-system.md)
  Advanced multi-dimensional project health analytics with continuous monitoring. Provides comprehensive quality metrics, trend analysis, and predictive insights.

- `CORE-SCAN-001` - [Project Upload & Scanning](../01-core-features/05-quality-analysis/02-project-upload-scanning.md)
  Core functionality for project ingestion and initial analysis. Handles project import, validation, and preliminary quality assessment with intelligent categorization.

- `WIDGET-READINESS-001` - [Production Readiness Score Widget](../01-core-features/05-quality-analysis/04-production-readiness-widget.md)
  Brutally honest assessment of project's production readiness. Provides comprehensive scoring across multiple dimensions with actionable improvement recommendations.

- `WIDGET-SECURITY-001` - [Security Analysis Widget](../01-core-features/05-quality-analysis/05-security-analysis-widget.md)
  Real-time security monitoring with actionable fixes for vulnerabilities. Continuously scans for security issues and provides one-click remediation options.

- `WIDGET-QUALITY-001` - [Code Quality Reality Check Widget](../01-core-features/05-quality-analysis/06-code-quality-widget.md)
  Honest assessment of code quality with refactoring suggestions. Analyzes code complexity, maintainability, and technical debt with brutal honesty messaging.

- `WIDGET-PERFORMANCE-001` - [Performance Monitor Widget](../01-core-features/05-quality-analysis/07-performance-monitor-widget.md)
  Track performance metrics with user experience impact focus. Monitors application performance, bundle size, and optimization opportunities with real-time insights.

- `WIDGET-TESTING-001` - [Test Coverage Tracker Widget](../01-core-features/05-quality-analysis/08-test-coverage-widget.md)
  Visualize test coverage with critical path focus and quality assessment. Provides comprehensive testing analytics with AI-powered test generation recommendations.

- `WIDGET-DRIFT-001` - [Documentation Drift Detector Widget](../01-core-features/05-quality-analysis/09-documentation-drift-widget.md)
  Detect gaps between documentation claims and actual implementation. Uses LLM analysis to identify documentation inconsistencies with auto-sync capabilities.

- `ANALYSIS-VISUAL-001` - [Visual Analysis Report](../01-core-features/05-quality-analysis/10-visual-analysis-report.md)
  Automated visual analysis of web projects using Playwright and multimodal LLM. Captures screenshots and provides comprehensive UI/UX quality assessment.

### Admin System
- `CORE-ADMIN-001` - [Backend Admin System](backend-admin-system.md)
  Comprehensive platform management with real-time monitoring and AI model oversight. Provides administrative control over system performance, users, and AI resources.

### Security
- `CORE-AUTH-001` - [Authentication & Authorization System](authentication-authorization-system.md)
  Secure user identity management with Clerk integration and JWT tokens. Provides robust authentication, authorization, and session management capabilities.

### Business Logic
- `CORE-PAYMENT-001` - [Payment & Subscription System](payment-subscription-system.md)
  Comprehensive billing and subscription management with usage tracking. Handles payment processing, subscription tiers, and usage-based billing with analytics.

## Widget Sub-Features (from CORE-DASHBOARD-WIDGETS bundle)

### User Interface Widgets
- `WIDGET-ASSISTANT-001` - [AI Assistant & Smart Recommendations](../01-core-features/03-user-interface/widgets.md)
  Contextual recommendations based on project state and user intent. Provides intelligent suggestions for improvements and next steps.

- `WIDGET-DEPENDENCIES-001` - [Dependency Health Check](../01-core-features/03-user-interface/widgets.md)
  Monitor dependency health, vulnerabilities, and update recommendations. Tracks package security, updates, and compatibility issues.

- `WIDGET-TOKENS-001` - [Token Usage Optimizer](../01-core-features/03-user-interface/widgets.md)
  Optimize AI/LLM token usage for cost efficiency and performance. Provides insights and recommendations for reducing AI costs while maintaining quality.

## Feature Bundles
- `CORE-DASHBOARD-WIDGETS` - [KAPI Dashboard Widgets](../01-core-features/03-user-interface/widgets.md)
  Core widgets providing real-time project insights through LLM-powered analysis. Comprehensive dashboard components for quality monitoring and development insights.

## Feature Cross-References

### Memory System Integration
The following features have deep memory system integration:
- `CORE-MEMORY-001` - Core memory architecture
- `CORE-AUTOFIX-001` - Context-aware code fixes
- `CORE-INTERVIEW-001` - Personalized user interviews
- `CORE-PROJECT-ONBOARDING-001` - Project analysis and conversational discovery
- `CORE-DASHBOARD-001` - Personalized dashboard experience
- `CORE-PROFILE-001` - User behavior learning
- `CORE-DOCMGMT-001` - Context-aware documentation
- `CORE-TESTGEN-001` - Memory-enhanced test generation
- `CORE-HEALTH-001` - Pattern-based health analysis
- `WIDGET-MEMORY-001` - Memory system management interface

### AI Intelligence Ecosystem
Core AI features that work together:
- `CORE-AI-001` - Agent orchestration and management
- `CORE-CONVERSATION-001` - Unified AI communication layer
- `CORE-ROUTING-001` - Intelligent task routing
- `CORE-NOVA-001` - Voice-to-code integration
- `CORE-AUTOFIX-001` - AI-powered code improvements
- `CORE-INTERVIEW-001` - AI-driven user interaction
- `CORE-PROJECT-ONBOARDING-001` - AI-powered project discovery
- `ANALYSIS-VISUAL-001` - Multimodal LLM analysis

### Quality & Analysis Pipeline
Quality-focused features:
- `CORE-SCAN-001` - Initial project analysis
- `CORE-PROJECT-ONBOARDING-001` - Comprehensive project analysis and interview
- `CORE-HEALTH-001` - Comprehensive health monitoring
- `CORE-DASHBOARD-001` - Real-time quality visualization
- `WIDGET-READINESS-001` - Production readiness assessment
- `WIDGET-SECURITY-001` - Security monitoring
- `WIDGET-QUALITY-001` - Code quality analysis
- `WIDGET-PERFORMANCE-001` - Performance monitoring
- `WIDGET-TESTING-001` - Test coverage tracking
- `WIDGET-DRIFT-001` - Documentation drift detection
- `ANALYSIS-VISUAL-001` - Visual quality analysis
- `CORE-AUTOFIX-001` - Quality-driven improvements
- `CORE-TESTGEN-001` - Quality-assured test generation

### Development Workflow
Features supporting the development lifecycle:
- `CORE-METHODOLOGY-001` - Backwards build process
- `CORE-DOCMGMT-001` - Documentation generation & management
- `CORE-DOCGEN-001` - AST-based documentation generation
- `CORE-TESTGEN-001` - Automated test creation
- `CORE-TEMPLATE-001` - Code scaffolding and templates
- `CORE-DEPLOY-001` - Deployment automation
- `CORE-IDE-001` - Development environment
- `CORE-SEARCH-001` - Code discovery and navigation
- `CORE-REALTIME-001` - Real-time collaboration

### User Experience
Features focused on user interaction:
- `CORE-DASHBOARD-001` - Central user interface
- `UI-ONBOARDING-001` - User introduction & setup
- `CORE-PROJECT-ONBOARDING-001` - Project-specific onboarding & discovery
- `CORE-PROFILE-001` - Advanced user profiling
- `CORE-FEEDBACK-001` - User feedback collection
- `WIDGET-MEMORY-001` - Memory system management
- `WIDGET-ASSISTANT-001` - AI assistant recommendations
- `WIDGET-DEPENDENCIES-001` - Dependency monitoring
- `WIDGET-TOKENS-001` - Token usage optimization

## Usage Examples

### In PRs
```
PR Title: "feat: Add AI-powered code review suggestions"
PR Body: "Implements CORE-AUTOFIX-001 with integration to CORE-MEMORY-001"
```

### In Code Comments
```typescript
// CORE-AUTOFIX-001: Auto-fix system implementation
// Integrates with CORE-MEMORY-001 for context-aware fixes
class AutoFixEngine {
  // ...
}
```

### In Documentation
```markdown
The auto-fix system [CORE-AUTOFIX-001] integrates with 
the memory system [CORE-MEMORY-001] and task routing [CORE-ROUTING-001] 
to provide context-aware code improvements.
```

### In Tasks/Issues
```
Task: Implement real-time quality monitoring
Features: CORE-DASHBOARD-001, CORE-HEALTH-001, WIDGET-READINESS-001
Dependencies: CORE-MEMORY-001, CORE-SCAN-001
Priority: High
Sprint: Current
```

## Feature Dependencies

### High-Level Dependencies
- **CORE-MEMORY-001** - Foundation for most AI features
- **CORE-CONVERSATION-001** - Core AI communication layer
- **CORE-AI-001** - Core agent architecture used by multiple features
- **CORE-SCAN-001** - Initial analysis required by health monitoring
- **CORE-DASHBOARD-001** - Primary UI for most features

### Integration Patterns
- **Memory-Enhanced Features**: Features that use memory system for context
- **AI-Powered Features**: Features that leverage AI agents for intelligence
- **Real-Time Features**: Features that provide live updates and monitoring
- **User-Facing Features**: Features with direct user interaction
- **Quality Analysis Features**: Features focused on code quality assessment
- **Widget-Based Features**: Dashboard widgets for specific functionality

---

**Last Updated**: July 18, 2025  
**Total Core Features**: 36 specifications  
**Total Widget Features**: 9 specifications  
**Total Feature Bundles**: 1 bundle  
**Cross-References**: 69 integration points documented  
**Documentation Coverage**: 100% of core features