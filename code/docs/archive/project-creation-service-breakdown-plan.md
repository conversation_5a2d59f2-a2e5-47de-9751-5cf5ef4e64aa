# 🚀 Project Creation Service Breakdown Plan

_Last updated: July 19, 2025_

## 🎯 Overview

Breaking down the monolithic `project-creation.service.ts` (4000+ lines) into focused, maintainable services following the [Comprehensive Project Creation Flow](../02-products/01-core-features/04-development-process/comprehensive-project-creation-flow.md) and KAPI development standards.

---

## 📊 Current State Analysis

### **Existing Monolithic Service**
```
backend/src/services/project-creation.service.ts
├── 4006 lines of code
├── All generation methods in one file
├── Mock implementations
└── No separation of concerns
```

### **Existing Infrastructure** ✅
```
backend/src/services/
├── agent-orchestrator.service.ts (✅ exists, needs enhancement)
├── text-interview.service.ts (✅ exists, needs enhancement)  
├── template/template.service.ts (✅ exists, working)
├── conversation/strategies/ (✅ multiple strategies exist)
└── memory.service.ts (✅ exists, working)
```

---

## 🎭 Service Breakdown Strategy

### **Phase 1: Core Orchestration** (Week 1)
Replace monolithic service with clean orchestrator pattern

#### **1.1 Enhanced Project Creation Orchestrator** 
**File**: `backend/src/services/project-creation-orchestrator.service.ts`
```typescript
@injectable()
export class ProjectCreationOrchestrator {
  constructor(
    private personalOnboarding: PersonalOnboardingService,
    private projectDiscovery: ProjectDiscoveryService,
    private requirementsAssembly: RequirementsAssemblyService,
    private specificationGeneration: SpecificationGenerationService,
    private testGeneration: TestGenerationService,
    private codeGeneration: CodeGenerationService,
    private deploymentService: DeploymentService,
    private stateManagement: StateManagementService
  ) {}

  async createProject(userId: string, initialInput: string): Promise<ProjectResult> {
    // Orchestrates the entire 5-phase flow
  }

  async resumeProject(userId: string): Promise<ResumeResult> {
    // Handles resumable experience
  }
}
```

#### **1.2 State Management Service**
**File**: `backend/src/services/project-creation/state-management.service.ts`
```typescript
@injectable()
export class StateManagementService {
  async saveProjectState(state: ProjectCreationState): Promise<void>
  async loadProjectState(userId: string): Promise<ProjectCreationState | null>
  async updatePhase(projectId: string, phase: ProjectPhase): Promise<void>
  async getResumePoint(userId: string): Promise<ResumePoint>
}
```

### **Phase 2: Enhanced Existing Services** (Week 2)

#### **2.1 Enhanced Text Interview Service**
**File**: `backend/src/services/text-interview.service.ts` (enhance existing)
```typescript
@injectable()
export class TextInterviewService {
  // Remove mock implementation
  // Add real conversational intelligence
  // Integrate with memory system for context
  
  async conductProjectInterview(
    userId: string,
    personalContext: PersonalProfile,
    initialRequirements: string
  ): Promise<ProjectInterviewContext>
}
```

#### **2.2 Enhanced Agent Orchestrator**
**File**: `backend/src/services/agent-orchestrator.service.ts` (enhance existing)
```typescript
@injectable()
export class AgentOrchestrator {
  // Replace mock methods with real implementations
  // Use existing conversation strategies
  
  async generateDocumentation(context: ProjectContext): Promise<DocumentationSet>
  async generateSlides(context: ProjectContext): Promise<PresentationSet>
  async generateTests(context: ProjectContext): Promise<TestSuite>
  async generateCode(context: ProjectContext): Promise<CodeBase>
}
```

### **Phase 3: New Generation Services** (Week 3)

#### **3.1 Requirements Assembly Service**
**File**: `backend/src/services/project-creation/requirements-assembly.service.ts`
```typescript
@injectable()
export class RequirementsAssemblyService {
  async assembleRequirements(
    personalProfile: PersonalOnboardingResult,
    projectDiscovery: ProjectDiscoveryResult
  ): Promise<ProjectContext>
  
  async validateRequirements(context: ProjectContext): Promise<ValidationResult>
  async refineRequirements(context: ProjectContext, feedback: UserFeedback): Promise<ProjectContext>
}
```

#### **3.2 Specification Generation Service**
**File**: `backend/src/services/project-creation/specification-generation.service.ts`
```typescript
@injectable()
export class SpecificationGenerationService {
  constructor(
    private agentOrchestrator: AgentOrchestrator,
    private templateService: TemplateService
  ) {}

  async generateSpecifications(projectContext: ProjectContext): Promise<SpecificationResult>
  async regenerateWithFeedback(specs: SpecificationResult, feedback: UserFeedback): Promise<SpecificationResult>
}
```

#### **3.3 Test Generation Service**
**File**: `backend/src/services/project-creation/test-generation.service.ts`
```typescript
@injectable()
export class TestGenerationService {
  async generateTestSuite(
    approvedSpecs: SpecificationResult,
    projectContext: ProjectContext
  ): Promise<TestSuiteResult>
  
  async validateTestStrategy(tests: TestSuiteResult): Promise<ValidationResult>
}
```

#### **3.4 Code Generation Service**
**File**: `backend/src/services/project-creation/code-generation.service.ts`
```typescript
@injectable()
export class CodeGenerationService {
  async generateCodeImplementation(
    approvedSpecs: SpecificationResult,
    approvedTests: TestSuiteResult,
    projectContext: ProjectContext
  ): Promise<CodeGenerationResult>
}
```

### **Phase 4: Quality & Deployment Services** (Week 4)

#### **4.1 Quality Analysis Service** (Separate Service)
**File**: `backend/src/services/quality/quality-analysis.service.ts`
```typescript
@injectable()
export class QualityAnalysisService {
  async analyzeCodeQuality(generatedCode: CodeGenerationResult): Promise<QualityAnalysisResult>
  async runSecurityScan(code: CodeGenerationResult): Promise<SecurityAnalysisResult>
  async validatePerformance(code: CodeGenerationResult): Promise<PerformanceAnalysisResult>
  async calculateOverallScore(analyses: AnalysisResult[]): Promise<QualityScore>
}
```

#### **4.2 Deployment Service**
**File**: `backend/src/services/deployment/deployment.service.ts`
```typescript
@injectable()
export class DeploymentService {
  async configureDeployment(codebase: CodeGenerationResult, context: ProjectContext): Promise<DeploymentConfiguration>
  async deployToRailway(config: DeploymentConfiguration): Promise<DeploymentResult>
  async deployToVercel(config: DeploymentConfiguration): Promise<DeploymentResult>
  async checkDeploymentStatus(deploymentId: string): Promise<DeploymentStatus>
}
```

---

## 📁 New File Structure

```
backend/src/services/
├── project-creation-orchestrator.service.ts          # Main orchestrator
├── project-creation/                                 # New directory
│   ├── requirements-assembly.service.ts              # Phase 1: Requirements
│   ├── specification-generation.service.ts           # Phase 2: Specs
│   ├── test-generation.service.ts                    # Phase 3: Tests  
│   ├── code-generation.service.ts                    # Phase 4: Code
│   ├── state-management.service.ts                   # State tracking
│   └── types/                                        # Shared types
│       ├── project-context.types.ts
│       ├── generation-results.types.ts
│       └── state-management.types.ts
├── quality/                                          # Separate quality service
│   ├── quality-analysis.service.ts                   # Quality validation
│   └── types/
│       └── quality-analysis.types.ts
├── deployment/                                       # Separate deployment service
│   ├── deployment.service.ts                         # Platform deployment
│   ├── platforms/
│   │   ├── railway-deployment.service.ts
│   │   └── vercel-deployment.service.ts
│   └── types/
│       └── deployment.types.ts
├── agent-orchestrator.service.ts                     # Enhanced existing
├── text-interview.service.ts                         # Enhanced existing
└── template/template.service.ts                      # Keep existing
```

---

## 🔄 Migration Strategy

### **Step 1: Create New Structure** (Day 1-2)
```bash
# Create new directories
mkdir -p backend/src/services/project-creation/{types}
mkdir -p backend/src/services/quality/{types}
mkdir -p backend/src/services/deployment/{platforms,types}
```

### **Step 2: Extract Core Types** (Day 3)
```typescript
// Move shared interfaces to dedicated type files
export interface ProjectContext { ... }
export interface SpecificationResult { ... }
export interface TestSuiteResult { ... }
export interface CodeGenerationResult { ... }
```

### **Step 3: Create Orchestrator** (Day 4-5)
```typescript
// Create main orchestrator that coordinates all services
// Move existing logic from project-creation.service.ts
```

### **Step 4: Extract Generation Services** (Day 6-10)
```typescript
// Move generation methods to dedicated services
// - Move generateDocumentation → SpecificationGenerationService
// - Move generateTests → TestGenerationService  
// - Move generateCode → CodeGenerationService
```

### **Step 5: Create Support Services** (Day 11-14)
```typescript
// Create new support services
// - StateManagementService
// - QualityAnalysisService
// - DeploymentService
```

---

## 🧪 Testing Strategy

### **Unit Tests** (Follow conventions: `<50 lines per function`)
```
backend/src/services/project-creation/__tests__/
├── project-creation-orchestrator.test.ts
├── requirements-assembly.test.ts
├── specification-generation.test.ts
├── test-generation.test.ts
├── code-generation.test.ts
└── state-management.test.ts
```

### **Integration Tests**
```
backend/src/services/project-creation/__tests__/integration/
├── full-flow-integration.test.ts
├── resume-functionality.test.ts
└── service-coordination.test.ts
```

### **Test Requirements** (Per development standards)
- ✅ Mandatory AI-written unit tests
- ✅ `poetry run pytest` (backend)  
- ✅ `npm run test` (frontend)
- ✅ >80% code coverage target
- ✅ TypeScript strict mode

---

## 📊 Implementation Timeline

### **Week 1: Foundation**
- [ ] Create new directory structure
- [ ] Extract and organize types
- [ ] Create ProjectCreationOrchestrator
- [ ] Create StateManagementService
- [ ] Update existing services (remove mocks)

### **Week 2: Generation Services**
- [ ] Create RequirementsAssemblyService
- [ ] Create SpecificationGenerationService  
- [ ] Create TestGenerationService
- [ ] Create CodeGenerationService
- [ ] Enhance AgentOrchestrator with real implementations

### **Week 3: Quality & Deployment**
- [ ] Create QualityAnalysisService (separate)
- [ ] Create DeploymentService
- [ ] Add platform-specific deployment services
- [ ] Integrate with existing Template Service

### **Week 4: Integration & Testing**
- [ ] Full integration testing
- [ ] API endpoint updates
- [ ] Frontend integration
- [ ] Documentation updates
- [ ] Performance optimization

---

## 🎯 Success Criteria

### **Code Quality** (Per development standards)
- ✅ TypeScript strict mode compliance
- ✅ ESLint + Prettier formatting
- ✅ Functions <50 lines each
- ✅ Zero critical security vulnerabilities
- ✅ API responses <200ms for 95% of requests

### **Architecture Quality**
- ✅ Clear separation of concerns
- ✅ Single responsibility principle
- ✅ Dependency injection pattern
- ✅ Testable service interfaces
- ✅ Backwards build philosophy implementation

### **Functional Requirements**
- ✅ Complete 5-phase project creation flow
- ✅ Resumable experience at any phase
- ✅ Quality analysis and validation
- ✅ One-click deployment to Railway/Vercel
- ✅ Integration with existing Memory and Template systems

---

## 🔗 Dependencies & Integration Points

### **Existing Services** (Keep & Enhance)
```typescript
// Enhanced, not replaced
agent-orchestrator.service.ts
text-interview.service.ts
template/template.service.ts
memory.service.ts
conversation/strategies/*
```

### **New Service Dependencies**
```typescript
// New services depend on existing infrastructure
ProjectCreationOrchestrator → AgentOrchestrator
SpecificationGenerationService → TemplateService
All services → MemoryService
QualityAnalysisService → Independent (separate concern)
```

### **API Updates Required**
```typescript
// Update existing endpoints
POST /api/projects/create
GET /api/projects/{id}/resume
PUT /api/projects/{id}/phase
GET /api/projects/{id}/status
```

---

## 📋 Development Checklist

### **Pre-Development**
- [ ] Review comprehensive flow specification
- [ ] Understand existing service architecture
- [ ] Plan TypeScript interfaces and types
- [ ] Set up testing framework

### **During Development**
- [ ] Follow KAPI conventions (TypeScript, <50 line functions)
- [ ] Write tests first (backwards build principle)
- [ ] Document all public methods (Google-style docstrings)
- [ ] Use dependency injection pattern
- [ ] Maintain ESLint compliance

### **Post-Development**
- [ ] Run full test suite (`npm run test`)
- [ ] Verify TypeScript compliance
- [ ] Update API documentation
- [ ] Integration testing with existing flow
- [ ] Performance testing

---

## 🚀 Next Actions

1. **Create new directory structure** (Day 1)
2. **Extract shared types** (Day 2) 
3. **Build ProjectCreationOrchestrator** (Day 3-4)
4. **Enhance existing AgentOrchestrator** (Day 5)
5. **Create generation services sequentially** (Week 2)

---

*This plan transforms a 4000-line monolithic service into focused, testable, maintainable services following KAPI's backwards build philosophy and development standards* 🚀