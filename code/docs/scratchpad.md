
Next: Make <EMAIL> into admin: kapihq.com/admin [added to the db]
Finish implementing auth for ide, backend and terminal client
-- Check the Pm2 implementation status
-- Distribute the .dmg and the node package

In the terminal let status command show user account and summary of the user's memory. Also update the appropriate spec here: /Users/<USER>/Code/KAPI/code/docs/02-products/01-core-features/07-terminal-client 

-- In project onboarding implement followup questions (clear and humours) when the user selects an existing project so that we can continuously learn just like in user onboarding. This is part of fully imploementing the brutal honest spec: /Users/<USER>/Code/KAPI/code/docs/02-products/01-core-features/01-ai-intelligence/brutal-honesty-analysis-spec.md

-- In the IDE and dashboard show offline or online [connected to server or not] in the bottom bar.




-- Hook dashboard widgets to real functionality. 





Later:
-- Refactor App.tsx

Use that as a template for the rest. 
## Credentials related:
- kapiadmin / Fu@sK+frN2m~
I have 2 different versions of postgres 16 and 17. I should use only 17 from homebrew. 16 also has kapi db, but completeoy out of date.

- <EMAIL> / admin123
- For using claude with AWS bedrock:
- export CLAUDE_CODE_USE_BEDROCK=1
- export ANTHROPIC_MODEL='us.anthropic.claude-3-7-sonnet-20250219-v1:0'
export DISABLE_PROMPT_CACHING=true




   

