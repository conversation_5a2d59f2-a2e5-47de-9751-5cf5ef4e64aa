import React, { useState, useRef, useEffect } from 'react';
import kapi<PERSON>ogo from '../assets/kapi-logo-transparent.png';
import '../styles/onboarding.css';
import '../styles/project-analysis.css';
import { useAuth } from '../contexts/AuthContext';
import { useOnboardingConversation } from '../hooks/useOnboardingConversation';
import { useAutoScroll } from '../hooks/useAutoScroll';
import MarkdownMessage from '../components/MarkdownMessage';
import SpinningBrain from '../components/SpinningBrain';
import { ApiClient } from '../services/ApiClient';
import { projectAnalysisService, AnalysisProgress } from '../services/ProjectAnalysisService';

interface ProjectOnboardingProps extends React.HTMLAttributes<HTMLDivElement> {
  onComplete: (projectConfig?: ProjectConfig) => void;
}

type OnboardingStage = 'welcome' | 'type-selection' | 'folder-selection' | 'analysis' | 'brutal-honesty-reality-check' | 'health-dashboard' | 'architecture' | 'roadmap' | 'discovery' | 'configuration' | 'summary';

// Project types for quick selection
const PROJECT_TYPES = [
  { 
    label: '🌐 Web Application', 
    value: 'web-app',
    description: 'Full-stack web application'
  },
  { 
    label: '📱 Mobile App', 
    value: 'mobile-app',
    description: 'React Native or native mobile app'
  },
  { 
    label: '🤖 AI/ML Project', 
    value: 'ai-ml',
    description: 'Machine learning or AI project'
  },
  { 
    label: '🔧 API/Backend', 
    value: 'api-backend',
    description: 'REST API or backend service'
  },
  { 
    label: '📊 Data Project', 
    value: 'data-project',
    description: 'Data analysis or visualization'
  },
  { 
    label: '🎮 Game/Interactive', 
    value: 'game',
    description: 'Game or interactive application'
  },
  { 
    label: '🛠️ Tool/Utility', 
    value: 'tool',
    description: 'Developer tool or utility'
  },
  { 
    label: '💼 Other', 
    value: 'other',
    description: 'Something else'
  }
];

// Project objectives for initial selection
const PROJECT_OBJECTIVES = [
  { 
    label: '🆕 Building a new app', 
    value: 'build-new',
    description: 'Starting fresh with a new project'
  },
  { 
    label: '⚡ Improving existing one', 
    value: 'improve-existing',
    description: 'Enhancing or adding features to existing code'
  },
  { 
    label: '🎓 Learning project', 
    value: 'learning',
    description: 'Building something to learn new skills'
  }
];

// Stage-specific content
const STAGE_CONTENT = {
  welcome: {
    title: 'Project Discovery',
    subtitle: 'Let\'s bring your project to life',
    description: "We'll help you build amazing software with AI-powered assistance.",
  },
  'type-selection': {
    title: 'Project Type',
    subtitle: 'Choose your project type',
    description: "What kind of project are you working on?",
  },
  'folder-selection': {
    title: 'Project Location',
    subtitle: 'Where should we set up your project?',
    description: "You can create a new folder or choose an existing one.",
  },
  analysis: {
    title: 'AI Analysis',
    subtitle: 'Analyzing your codebase',
    description: 'Our AI is diving deep into your project to understand its structure and health.',
  },
  'brutal-honesty-reality-check': {
    title: 'Project Reality Check',
    subtitle: 'The brutal truth about your code',
    description: 'Ready to see what your project really looks like? We\'ll tell you the truth.',
  },
  'health-dashboard': {
    title: 'Health Dashboard',
    subtitle: 'Project analysis complete',
    description: 'Ready to explore your project insights in the dashboard.',
  },
  architecture: {
    title: 'Architecture View',
    subtitle: 'System overview',
    description: 'Visualizing your project structure and relationships.',
  },
  roadmap: {
    title: 'Improvement Plan',
    subtitle: 'Your path forward',
    description: 'AI-generated roadmap for project improvements.',
  },
  discovery: {
    title: 'Project Discovery',
    subtitle: 'Tell us about your project',
    description: "I'll ask you some questions to understand what you're building.",
  },
  configuration: {
    title: 'Technical Details',
    subtitle: 'Configure your setup',
    description: "Let's set up the technical aspects of your project.",
  },
  summary: {
    title: 'Ready to Build',
    subtitle: 'Everything looks great!',
    description: "I have everything I need. Let's start building your project!",
  }
} as const;

interface ProjectConfig {
  name?: string;
  description?: string;
  languages?: string[];
  frameworks?: string[];
  timeline?: string;
  objective?: string;
  type?: string;
  folder?: string;
  analysisData?: {
    projectSummary?: string;
    gitAnalysis?: {
      userLastActivity?: {
        lastCommit?: {
          hash: string;
          date: string;
          message: string;
        };
        uncommittedChanges?: {
          modified: string[];
          added: string[];
          deleted: string[];
        };
        lastBranch?: string;
      };
      teamActivity?: {
        recentCommits: Array<{
          author: string;
          email: string;
          hash: string;
          date: string;
          message: string;
        }>;
        activeContributors: string[];
      };
    };
    aiTracking?: {
      percentage: number;
      files: Array<{
        path: string;
        aiGenerated: boolean;
        generator?: string;
        confidence: number;
      }>;
    };
    contextInfo?: {
      lastSession?: {
        lastOpenTime: string;
        lastActiveFile?: string;
        lastPosition?: { line: number; column: number };
        sessionDuration: number;
        taskInProgress?: string;
      };
    };
  };
  healthScore?: number;
}

const ProjectOnboarding: React.FC<ProjectOnboardingProps> = ({ onComplete, ...props }) => {
  const { user } = useAuth();
  const [currentStage, setCurrentStage] = useState<OnboardingStage>('welcome');
  
  // Configuration state
  const [projectConfig, setProjectConfig] = useState<ProjectConfig>({});
  const [selectedObjective, setSelectedObjective] = useState<string>('');
  const [selectedProjectType, setSelectedProjectType] = useState<string>('');
  const [selectedFolderPath, setSelectedFolderPath] = useState<string>('');
  const [showManualFolderInput, setShowManualFolderInput] = useState<boolean>(false);
  const [manualFolderPath, setManualFolderPath] = useState<string>('');
  
  // Enhanced analysis state
  const [analysisProgress, setAnalysisProgress] = useState<AnalysisProgress | null>(null);
  const [brutalHonestyReport, setBrutalHonestyReport] = useState<any>(null);
  const [progressiveImprovement, setProgressiveImprovement] = useState<any>(null);

  // Use the onboarding conversation hook with project strategy
  const {
    messages,
    isProcessing,
    isListening,
    voiceError,
    isStreaming,
    streamingContent,
    extractedNextQuestions,
    sendTextMessage,
    startVoiceInput,
    stopVoiceInput,
    clearMessages
  } = useOnboardingConversation();

  // Clear any auto-initialized messages when we enter discovery stage to prevent response ordering issues
  useEffect(() => {
    if (currentStage === 'discovery' && messages.length > 0) {
      console.log('🎯 [PROJECT-ONBOARDING] Clearing auto-initialized messages to prevent ordering issues');
      clearMessages();
    }
  }, [currentStage, clearMessages]);

  // Reference to the conversation container for scrolling
  const conversationEndRef = useRef<HTMLDivElement>(null);

  // Use auto-scroll for conversation
  useAutoScroll({
    messages: messages || [],
    isStreaming,
    streamingContent
  });

  // Calculate progress percentage based on current stage
  const getProgressPercentage = (): number => {
    const stageOrder: OnboardingStage[] = ['welcome', 'type-selection', 'folder-selection', 'analysis', 'brutal-honesty-reality-check', 'health-dashboard', 'architecture', 'roadmap', 'discovery', 'configuration', 'summary'];
    const currentIndex = stageOrder.indexOf(currentStage);
    return ((currentIndex + 1) / stageOrder.length) * 100;
  };

  const getStageNumber = (): string => {
    const stageOrder: OnboardingStage[] = ['welcome', 'type-selection', 'folder-selection', 'analysis', 'brutal-honesty-reality-check', 'health-dashboard', 'architecture', 'roadmap', 'discovery', 'configuration', 'summary'];
    const currentIndex = stageOrder.indexOf(currentStage);
    return `Step ${currentIndex + 1} of ${stageOrder.length}`;
  };

  // Load project context on component mount
  useEffect(() => {
    loadCurrentProjectContext();
  }, []);

  // Detect if folder contains an existing project
  const detectExistingProject = async (folderPath: string): Promise<boolean> => {
    try {
      const projectIndicators = [
        'package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 
        'pom.xml', 'Gemfile', '.git', 'src', 'app', 'lib', 'components'
      ];
      
      const contents = await window.electronAPI?.fileExplorer.listDirectory({ path: folderPath });
      return contents?.some(item => 
        projectIndicators.includes(item.name.toLowerCase())
      ) || false;
    } catch (error) {
      console.error('Error detecting project:', error);
      return false;
    }
  };

  // Generate brutal honesty report
  const generateBrutalHonestyReport = async (analysisResult: any, folderPath: string): Promise<any> => {
    try {
      console.log('🔥 [BRUTAL-HONESTY] Generating brutal honesty report...');
      console.log('🔥 [BRUTAL-HONESTY] Analysis result structure:', {
        hasHealth: !!analysisResult.health,
        hasStructure: !!analysisResult.structure,
        hasFileMetrics: !!analysisResult.fileMetrics,
        hasIssues: !!analysisResult.issues,
        healthKeys: analysisResult.health ? Object.keys(analysisResult.health) : [],
        analysisKeys: Object.keys(analysisResult)
      });
      
      // Call the backend brutal honesty service
      const response = await fetch('http://localhost:3000/api/api/brutal-honesty/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectPath: folderPath,
          analysisResult: analysisResult
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const responseData = await response.json();
      console.log('🔥 [BRUTAL-HONESTY] Response received:', responseData);
      
      // Extract the actual report from the response
      const brutalHonestyReport = responseData.success ? responseData.data : responseData;
      console.log('🔥 [BRUTAL-HONESTY] Report generated:', brutalHonestyReport);
      
      // Also save to .kapi folder
      const kapiResponse = await fetch('http://localhost:3000/api/api/project/save-brutal-honesty', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectPath: folderPath,
          report: brutalHonestyReport
        })
      });
      
      if (kapiResponse.ok) {
        console.log('🔥 [BRUTAL-HONESTY] Report saved to .kapi folder');
      }
      
      return brutalHonestyReport;
    } catch (error) {
      console.error('🔥 [BRUTAL-HONESTY] Failed to generate brutal honesty report:', error);
      console.error('🔥 [BRUTAL-HONESTY] Error details:', {
        message: error.message,
        stack: error.stack,
        folderPath,
        analysisResultKeys: Object.keys(analysisResult)
      });
      // Return mock data as fallback
      return {
        productionReadiness: 58,
        overallGrade: 'D',
        messages: [
          {
            category: 'security',
            severity: 'critical',
            title: 'Security',
            brutalmessage: 'Your security is about as tight as a screen door on a submarine',
            helpfulGuidance: 'Add input validation, secure API endpoints, and implement proper authentication.',
            grade: 'F',
            emoji: '🔒',
            readinessImpact: 25
          },
          {
            category: 'testing',
            severity: 'error',
            title: 'Test Coverage',
            brutalmessage: 'Your test coverage is more missing than my motivation on Monday morning',
            helpfulGuidance: 'Write unit tests, add integration tests, and aim for 80%+ coverage.',
            grade: 'F',
            emoji: '🧪',
            readinessImpact: 20
          },
          {
            category: 'performance',
            severity: 'warning',
            title: 'Performance',
            brutalmessage: 'Your app loads slower than a dial-up modem in molasses',
            helpfulGuidance: 'Optimize bundle size, lazy load components, and improve algorithmic efficiency.',
            grade: 'C',
            emoji: '⚡',
            readinessImpact: 15
          },
          {
            category: 'quality',
            severity: 'warning',
            title: 'Code Quality',
            brutalmessage: 'Your code complexity makes rocket science look like finger painting',
            helpfulGuidance: 'Refactor complex functions, improve naming conventions, and reduce nesting depth.',
            grade: 'D',
            emoji: '✨',
            readinessImpact: 15
          },
          {
            category: 'documentation',
            severity: 'warning',
            title: 'Documentation',
            brutalmessage: 'Documentation so sparse, it makes a desert look lush',
            helpfulGuidance: 'Write a proper README, add API documentation, and include code comments.',
            grade: 'D',
            emoji: '📚',
            readinessImpact: 10
          },
          {
            category: 'error_handling',
            severity: 'error',
            title: 'Error Handling',
            brutalmessage: 'Your error handling is about as reliable as a chocolate teapot',
            helpfulGuidance: 'Add try-catch blocks, handle async errors, and implement proper error boundaries.',
            grade: 'D',
            emoji: '🚨',
            readinessImpact: 15
          }
        ],
        encouragement: "Don't worry, everyone's code starts somewhere. These issues are totally fixable, and you've got this!",
        nextSteps: [
          '1️⃣ Security: Add input validation and secure endpoints',
          '2️⃣ Testing: Write unit tests for core functionality', 
          '3️⃣ Documentation: Create a comprehensive README',
          '4️⃣ Performance: Optimize bundle size and load times'
        ],
        timeToFix: '2-4 hours',
        funFacts: [
          "You wrote code. That's like writing a novel, but with more bugs.",
          "Your project shows promise - every great codebase started with room for improvement!"
        ],
        detailedMetrics: {
          complexityScore: 65,
          maintainability: 70,
          technicalDebt: 35,
          testCoverage: 12,
          securityScore: 45,
          performanceScore: 72
        }
      };
    }
  };

  // Load current project context and cached analysis on component mount
  const loadCurrentProjectContext = async (): Promise<void> => {
    try {
      console.log('📁 [PROJECT-CONTEXT] Loading current project context...');
      
      // Get last project path from localStorage
      const lastProjectPath = localStorage.getItem('lastProjectPath');
      console.log('📁 [PROJECT-CONTEXT] Last project path:', lastProjectPath);
      
      if (lastProjectPath) {
        // Check if the project still exists
        try {
          const projectExists = await window.electronAPI?.fileExplorer.checkPathExists(lastProjectPath);
          if (projectExists) {
            console.log('📁 [PROJECT-CONTEXT] Project exists, loading context...');
            
            // Load cached brutal honesty report if available
            const cachedReport = await loadCachedBrutalHonestyReport(lastProjectPath);
            if (cachedReport) {
              console.log('📊 [PROJECT-CONTEXT] Loaded cached brutal honesty report');
              setBrutalHonestyReport(cachedReport);
              setCurrentStage('brutal-honesty-reality-check');
            } else {
              console.log('📊 [PROJECT-CONTEXT] No cached report, showing project discovery');
              setCurrentStage('type-selection');
            }
            
            // Load project config
            setSelectedFolderPath(lastProjectPath);
            setProjectConfig(prev => ({ ...prev, folder: lastProjectPath }));
            
            return;
          } else {
            console.log('📁 [PROJECT-CONTEXT] Project path no longer exists, clearing cache');
            localStorage.removeItem('lastProjectPath');
          }
        } catch (error) {
          console.error('📁 [PROJECT-CONTEXT] Error checking project existence:', error);
        }
      }
      
      // No valid project found, start fresh
      console.log('📁 [PROJECT-CONTEXT] No current project, starting fresh');
      setCurrentStage('welcome');
    } catch (error) {
      console.error('📁 [PROJECT-CONTEXT] Error loading project context:', error);
      setCurrentStage('welcome');
    }
  };

  // Load cached brutal honesty report from .kapi folder
  const loadCachedBrutalHonestyReport = async (folderPath: string): Promise<any | null> => {
    try {
      console.log('📊 [CACHE] Checking for cached brutal honesty report...');
      
      const kapiPath = `${folderPath}/.kapi/brutal-honesty-report.json`;
      const reportExists = await window.electronAPI?.fileExplorer.checkPathExists(kapiPath);
      
      if (reportExists) {
        const reportContent = await window.electronAPI?.fileExplorer.readFile(kapiPath);
        if (reportContent) {
          const report = JSON.parse(reportContent);
          console.log('📊 [CACHE] Successfully loaded cached report:', {
            productionReadiness: report.productionReadiness,
            messagesCount: report.messages?.length
          });
          return report;
        }
      }
      
      console.log('📊 [CACHE] No cached report found');
      return null;
    } catch (error) {
      console.error('📊 [CACHE] Error loading cached report:', error);
      return null;
    }
  };

  // Check for progressive improvement data
  const checkProgressiveImprovement = async (folderPath: string): Promise<void> => {
    try {
      console.log('📈 [PROGRESSIVE-IMPROVEMENT] Checking for historical data...');
      
      const response = await fetch('http://localhost:3000/api/project/progressive-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          projectPath: folderPath,
          userId: 1 // Default user ID
        })
      });
      
      if (response.ok) {
        const progressiveData = await response.json();
        console.log('📈 [PROGRESSIVE-IMPROVEMENT] Data found:', progressiveData);
        setProgressiveImprovement(progressiveData.data);
      } else {
        console.log('📈 [PROGRESSIVE-IMPROVEMENT] No historical data found');
        setProgressiveImprovement(null);
      }
    } catch (error) {
      console.error('📈 [PROGRESSIVE-IMPROVEMENT] Error checking historical data:', error);
      setProgressiveImprovement(null);
    }
  };

  // Generate analysis summary using code review strategy
  const generateAnalysisWithCodeReview = async (result: any, projectType: string, folderPath: string): Promise<void> => {
    const { structure, health, issues, suggestions, projectSummary, gitAnalysis, aiTracking } = result;
    
    // Create a comprehensive analysis prompt for the code review model
    let analysisPrompt = `I've completed an automated analysis of this ${projectType} project located at ${folderPath}. Here are the key findings:

**Project Overview:**
${projectSummary ? `- ${projectSummary}` : '- Analysis in progress...'}

**Project Structure:**
- ${structure.totalFiles} files, ${(structure.totalLines / 1000).toFixed(1)}K lines of code
- Languages: ${Object.keys(structure.languages).join(', ')}
- Frameworks: ${structure.frameworks.length > 0 ? structure.frameworks.join(', ') : 'None detected'}

**Health Metrics:**
- Overall Score: ${health.overallScore}/100
- Test Coverage: ${health.testCoverage}%
- Documentation: ${health.documentation}%
- Code Complexity: ${health.complexity.average} (average)`;

    // Add git information if available
    if (gitAnalysis?.userLastActivity?.lastCommit) {
      analysisPrompt += `

**Git Activity:**
- Last commit: ${gitAnalysis.userLastActivity.lastCommit.message}
- Branch: ${gitAnalysis.userLastActivity.lastBranch || 'main'}
- Uncommitted changes: ${gitAnalysis.userLastActivity.uncommittedChanges?.modified?.length || 0} modified files`;
    }

    // Add AI tracking information
    if (aiTracking && aiTracking.percentage > 0) {
      analysisPrompt += `

**AI Code Analysis:**
- AI-generated code: ${Math.round(aiTracking.percentage)}%
- Files with AI assistance: ${aiTracking.files.length}`;
    }

    analysisPrompt += `

**Issues Found:**
- ${issues.length} total issues detected
- Critical/Error: ${issues.filter((i: any) => i.severity === 'error' || i.severity === 'critical').length}
- Performance: ${issues.filter((i: any) => i.category === 'performance').length}
- Security: ${issues.filter((i: any) => i.category === 'security').length}

**Top Recommendations:**
${suggestions.slice(0, 3).map((s: any) => `- ${s.title}: ${s.description}`).join('\n')}

Please provide a concise 2-paragraph analysis summary of this codebase, highlighting the most important findings and potential areas for improvement. Then ask ONE specific question to understand what the user wants to focus on with this project.`;

    // Send the analysis prompt using code_review strategy
    return sendTextMessage(analysisPrompt, 'code_review');
  };

  // Start the project analysis
  const startProjectAnalysis = async (folderPath: string) => {
    setAnalysisProgress(null);
    
    try {
      // Set up progress callback
      projectAnalysisService.onProgress((progress: AnalysisProgress) => {
        setAnalysisProgress(progress);
      });
      
      // Run the comprehensive analysis
      const result = await projectAnalysisService.analyzeProject(folderPath);
      
      // Update project config with analysis results
      console.log('🎯 [PROJECT-ONBOARDING] Analysis result:', result);
      console.log('🎯 [PROJECT-ONBOARDING] Git analysis:', result.gitAnalysis);
      console.log('🎯 [PROJECT-ONBOARDING] AI tracking:', result.aiTracking);
      
      setProjectConfig(prev => {
        const newConfig = {
          ...prev,
          analysisData: result,
          healthScore: result.health.overallScore,
          languages: Object.keys(result.structure.languages),
          frameworks: result.structure.frameworks
        };
        console.log('🎯 [PROJECT-ONBOARDING] Updated project config:', newConfig);
        return newConfig;
      });
      
      // Generate brutal honesty report and show reality check
      const brutalhonesty = await generateBrutalHonestyReport(result, folderPath);
      setBrutalHonestyReport(brutalhonesty);
      
      // Check for progressive improvement data
      await checkProgressiveImprovement(folderPath);
      
      setCurrentStage('brutal-honesty-reality-check');
      
    } catch (error) {
      console.error('Analysis failed:', error);
      // Start discovery conversation even if analysis fails
      setCurrentStage('discovery');
      setTimeout(() => {
        const fallbackMessage = `I had trouble analyzing your ${selectedProjectType} project automatically, but I'd love to help you work on it! What's the main goal you have for this project - are you looking to add new features, fix issues, or improve the overall architecture?`;
        sendTextMessage(fallbackMessage, 'project_onboarding');
      }, 1000);
    }
  };

  // Handle objective selection
  const handleObjectiveSelection = (objective: string) => {
    setSelectedObjective(objective);
    setProjectConfig(prev => ({ ...prev, objective }));
    
    // Send contextual follow-up question based on objective
    setTimeout(() => {
      let followUpMessage = '';
      
      switch(objective) {
        case 'build-new':
          followUpMessage = `Exciting! 🚀 Building something new from scratch. What's the spark that inspired this idea - did you see a problem that needed solving, or are you exploring a cool technology? I'd love to hear the story behind your new project!`;
          break;
        case 'improve-existing':
          followUpMessage = `Smart move! 💡 Improving existing code is where the magic happens. What's bugging you most about the current version - is it performance, user experience, or are you adding features that'll make users go "wow"?`;
          break;
        case 'learning':
          followUpMessage = `Love the learning spirit! 🎓 The best way to master new skills is by building. What technology or concept are you excited to dive into - something you've been curious about or a skill gap you want to fill?`;
          break;
        default:
          followUpMessage = `Interesting choice! Tell me more about what you're trying to accomplish with this project.`;
      }
      
      sendTextMessage(followUpMessage, 'project_onboarding');
      setCurrentStage('type-selection');
    }, 300);
  };

  // Handle project type selection
  const handleProjectTypeSelection = (projectType: string) => {
    setSelectedProjectType(projectType);
    setProjectConfig(prev => ({ ...prev, type: projectType }));
    
    // Send contextual follow-up question based on project type
    setTimeout(() => {
      let followUpMessage = '';
      
      switch(projectType) {
        case 'web-app':
          followUpMessage = `Perfect! 🌐 Web apps are my favorite - so much possibility! Are you thinking React/Vue frontend with a Node.js backend, or going full-stack with something like Next.js? Also, is this going to be a SaaS, an e-commerce site, or something totally different?`;
          break;
        case 'mobile-app':
          followUpMessage = `Mobile magic! 📱 Are you leaning towards React Native (code once, run everywhere), or going native for that buttery smooth performance? What's the core feature that'll make users never want to delete your app?`;
          break;
        case 'ai-ml':
          followUpMessage = `The future is here! 🤖 Are you diving into the OpenAI ecosystem, building your own models, or maybe working with computer vision? What kind of AI magic are you planning to unleash on the world?`;
          break;
        case 'api-backend':
          followUpMessage = `The backbone of the internet! 🔧 REST API or are you exploring GraphQL? What kind of data are you serving up, and do you expect this to handle millions of requests or is it more of a focused service?`;
          break;
        case 'data-project':
          followUpMessage = `Data storytelling! 📊 Are you wrangling messy datasets, building beautiful dashboards, or maybe doing some predictive analytics? What insights are you hoping to uncover from the data?`;
          break;
        case 'game':
          followUpMessage = `Game on! 🎮 2D pixel art indie game, 3D adventure, or maybe a clever puzzle game? Are you using Unity, building browser-based with JavaScript, or going retro with something else?`;
          break;
        case 'tool':
          followUpMessage = `Developer tools are the best! 🛠️ Are you solving your own workflow problem, building a CLI that'll save developers hours, or maybe creating a VS Code extension? What repetitive task are you about to automate away forever?`;
          break;
        case 'other':
          followUpMessage = `Intriguing! 💼 I love when projects don't fit the usual boxes. What kind of unique creation are you brewing up? Give me the elevator pitch - I'm genuinely curious! 🚀`;
          break;
        default:
          followUpMessage = `Great choice! Tell me more about the technical approach you're considering for this project.`;
      }
      
      sendTextMessage(followUpMessage, 'project_onboarding');
      setCurrentStage('folder-selection');
    }, 300);
  };

  // Handle folder selection
  const handleFolderSelection = async () => {
    try {
      console.log('🎯 [FOLDER-SELECTION] Starting folder selection...');
      const result = await window.electronAPI?.fileExplorer.selectDirectory();
      console.log('🎯 [FOLDER-SELECTION] Result:', result, typeof result);
      
      if (result && typeof result === 'string' && result.trim()) {
        const folderPath = result;
        console.log('🎯 [FOLDER-SELECTION] Selected folder:', folderPath);
        setSelectedFolderPath(folderPath);
        
        // UPDATE localStorage IMMEDIATELY - this is the primary source for project path
        localStorage.setItem('lastProjectPath', folderPath);
        console.log('🎯 [FOLDER-SELECTION] Updated localStorage lastProjectPath:', folderPath);
        
        setProjectConfig(prev => {
          const newConfig = { ...prev, folder: folderPath };
          console.log('🎯 [FOLDER-SELECTION] Updated project config:', newConfig);
          return newConfig;
        });
        
        // Check if this is an existing project
        const isExisting = await detectExistingProject(folderPath);
        console.log('🎯 [FOLDER-SELECTION] Is existing project:', isExisting);
        
        if (isExisting) {
          // Start AI analysis for existing projects
          console.log('🎯 [FOLDER-SELECTION] Starting analysis for existing project');
          setCurrentStage('analysis');
          setTimeout(() => startProjectAnalysis(folderPath), 1000);
        } else {
          // Go to discovery for new projects
          console.log('🎯 [FOLDER-SELECTION] Moving to discovery for new project');
          setCurrentStage('discovery');
          // Clear any existing messages first, then send initial project discovery message
          // Clear messages immediately to prevent any race conditions
          clearMessages();
          
          setTimeout(() => {
            // Additional clear to ensure no auto-generated messages interfere
            clearMessages();
            const initialMessage = `I've selected an empty folder at ${folderPath} for a new ${selectedProjectType} project. Help me understand what I want to build and configure the project. This is for project creation, not general onboarding.`;
            sendTextMessage(initialMessage, 'project_onboarding');
          }, 1000); // Reduced delay to improve responsiveness
        }
      } else {
        console.log('🎯 [FOLDER-SELECTION] No valid folder selected or user cancelled');
      }
    } catch (error) {
      console.error('🎯 [FOLDER-SELECTION] Error:', error);
    }
  };

  // Handle manual folder path submission
  const handleManualFolderSubmit = async () => {
    if (manualFolderPath.trim()) {
      console.log('🎯 [MANUAL-FOLDER] Selected folder:', manualFolderPath);
      setSelectedFolderPath(manualFolderPath);
      
      // UPDATE localStorage IMMEDIATELY - this is the primary source for project path
      localStorage.setItem('lastProjectPath', manualFolderPath);
      console.log('🎯 [MANUAL-FOLDER] Updated localStorage lastProjectPath:', manualFolderPath);
      
      setProjectConfig(prev => {
        const newConfig = { ...prev, folder: manualFolderPath };
        console.log('🎯 [MANUAL-FOLDER] Updated project config:', newConfig);
        return newConfig;
      });
      setShowManualFolderInput(false);
      
      // Check if this is an existing project
      const isExisting = await detectExistingProject(manualFolderPath);
      
      if (isExisting) {
        // Start AI analysis for existing projects
        setCurrentStage('analysis');
        setTimeout(() => startProjectAnalysis(manualFolderPath), 1000);
      } else {
        // Go to discovery for new projects
        setCurrentStage('discovery');
        // Send initial project discovery message
        // Clear messages immediately to prevent race conditions
        clearMessages();
        
        setTimeout(() => {
          // Additional clear to ensure no auto-generated messages interfere
          clearMessages();
          const initialMessage = `I've selected an empty folder at ${manualFolderPath} for a new ${selectedProjectType} project. Help me understand what I want to build and configure the project. This is for project creation, not general onboarding.`;
          sendTextMessage(initialMessage, 'project_onboarding');
        }, 1000); // Reduced delay to improve responsiveness
      }
    }
  };

  // Handle complete button for discovery stage
  const handleComplete = async () => {
    try {
      // Ensure project configuration includes the folder path and conversation data
      const finalProjectConfig = {
        ...projectConfig,
        folder: projectConfig.folder || selectedFolderPath,
        name: projectConfig.name || `${selectedProjectType} Project`,
        description: projectConfig.description || `A ${selectedProjectType} project`,
        type: selectedProjectType,
        objective: selectedObjective,
        conversationData: messages // Include the conversation for context
      };
      
      console.log('🎯 [PROJECT-ONBOARDING] Final project config from discovery:', finalProjectConfig);
      
      // Complete the onboarding with the gathered information
      onComplete(finalProjectConfig);
    } catch (error) {
      console.error('🎯 [PROJECT-ONBOARDING] Error completing project setup:', error);
    }
  };

  // Handle continue button
  const handleContinue = async () => {
    try {
      // Ensure project configuration includes the folder path
      const finalProjectConfig = {
        ...projectConfig,
        folder: projectConfig.folder || selectedFolderPath,
        name: projectConfig.name || `${selectedProjectType} Project`,
        description: projectConfig.description || `A ${selectedProjectType} project`,
        type: selectedProjectType,
        objective: selectedObjective
      };
      
      console.log('🎯 [PROJECT-ONBOARDING] Final project config:', finalProjectConfig);
      
      // Save project configuration
      if (user?.id) {
        const apiClient = new ApiClient();
        await apiClient.post('/projects', {
          ...finalProjectConfig,
          userId: user.id,
          status: 'active'
        });
      }
      
      onComplete(finalProjectConfig);
    } catch (error) {
      console.error('🎯 [PROJECT-ONBOARDING] Error during completion:', error);
      // Still complete onboarding even if saving fails
      const finalProjectConfig = {
        ...projectConfig,
        folder: projectConfig.folder || selectedFolderPath,
        name: projectConfig.name || `${selectedProjectType} Project`,
        description: projectConfig.description || `A ${selectedProjectType} project`,
        type: selectedProjectType,
        objective: selectedObjective
      };
      onComplete(finalProjectConfig);
    }
  };

  return (
    <div className={`enhanced-dashboard onboarding-container stage-${currentStage}`} {...props}>
      {/* Dashboard-style Header */}
      <header className="dashboard-header">
        {/* Logo and Title */}
        <div className="dashboard-header-left">
          <div className="dashboard-logo">
            <img src={kapiLogo} alt="KAPI Logo" className="kapi-logo" />
            <span className="dashboard-title">KAPI</span>
            <span className="dashboard-subtitle">Project Onboarding - {STAGE_CONTENT[currentStage].title}</span>
          </div>
        </div>

        {/* Navigation */}
        <div className="dashboard-header-nav">
          <div className="progress-section">
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{ width: `${getProgressPercentage()}%` }}
              ></div>
            </div>
            <span className="stage-label">{getStageNumber()}</span>
          </div>
        </div>

        {/* Header Actions */}
        <div className="dashboard-header-right">
          <button className="nav-button" onClick={handleContinue}>
            Skip to dashboard →
          </button>
        </div>
      </header>

      <div className="onboarding-content">
        {/* Left Sidebar - Project Configuration */}
        <div className="left-sidebar">
          <div className="sidebar-content">
            <h3>Project Profile</h3>
            <div className="profile-card">
              <div className="profile-item">
                <strong>Objective:</strong> {selectedObjective ? PROJECT_OBJECTIVES.find(o => o.value === selectedObjective)?.label : '?'}
              </div>
              <div className="profile-item">
                <strong>Type:</strong> {selectedProjectType ? PROJECT_TYPES.find(t => t.value === selectedProjectType)?.label : '?'}
              </div>
              <div className="profile-item">
                <strong>Folder:</strong> {selectedFolderPath || '?'}
              </div>
              {projectConfig.name && (
                <div className="profile-item">
                  <strong>Name:</strong> {projectConfig.name}
                </div>
              )}
              {projectConfig.description && (
                <div className="profile-item">
                  <strong>Description:</strong> {projectConfig.description}
                </div>
              )}
              {projectConfig.languages && projectConfig.languages.length > 0 && (
                <div className="profile-item">
                  <strong>Languages:</strong> {projectConfig.languages.join(', ')}
                </div>
              )}
              {projectConfig.frameworks && projectConfig.frameworks.length > 0 && (
                <div className="profile-item">
                  <strong>Frameworks:</strong> {projectConfig.frameworks.join(', ')}
                </div>
              )}
              {projectConfig.timeline && (
                <div className="profile-item">
                  <strong>Timeline:</strong> {projectConfig.timeline}
                </div>
              )}
              {projectConfig.analysisData?.projectSummary && (
                <div className="profile-item">
                  <strong>Project Summary:</strong>
                  <p style={{ fontSize: '0.9rem', marginTop: '0.5rem', color: 'var(--text-body)' }}>
                    {projectConfig.analysisData.projectSummary}
                  </p>
                </div>
              )}
              {projectConfig.analysisData?.gitAnalysis?.userLastActivity?.lastCommit && (
                <div className="profile-item">
                  <strong>Last Commit:</strong>
                  <p style={{ fontSize: '0.9rem', marginTop: '0.5rem', color: 'var(--text-body)' }}>
                    {projectConfig.analysisData.gitAnalysis.userLastActivity.lastCommit.message}
                  </p>
                  <p style={{ fontSize: '0.8rem', color: 'var(--text-subtle)' }}>
                    {new Date(projectConfig.analysisData.gitAnalysis.userLastActivity.lastCommit.date).toLocaleDateString()}
                  </p>
                </div>
              )}
              {projectConfig.analysisData?.aiTracking && projectConfig.analysisData.aiTracking.percentage > 0 && (
                <div className="profile-item">
                  <strong>AI Code:</strong> {Math.round(projectConfig.analysisData.aiTracking.percentage)}%
                </div>
              )}
            </div>
            
            <h3>KAPI Features</h3>
            <div className="memory-card">
              <div className="memory-item">✓ Smart code generation</div>
              <div className="memory-item">✓ Automated testing</div>
              <div className="memory-item">✓ Real-time collaboration</div>
              <div className="memory-item">○ Deploy to cloud</div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="main-content">
          {/* Welcome Avatar/Animation */}
          <div className="welcome-area">
            <div className="avatar-circle">
              <span className="avatar-emoji">🚀</span>
            </div>
            
            <div className="welcome-message">
              <h2>{STAGE_CONTENT[currentStage].subtitle}</h2>
              {STAGE_CONTENT[currentStage].description && (
                <p>{STAGE_CONTENT[currentStage].description}</p>
              )}
            </div>
          </div>

          {/* Objective Selection (Welcome Stage Only) */}
          {currentStage === 'welcome' && !selectedObjective && (
            <div className="role-selection">
              <h3>What's your main goal with this project?</h3>
              <div className="role-grid">
                {PROJECT_OBJECTIVES.map((objective) => (
                  <button 
                    key={objective.value}
                    className="role-option-button"
                    onClick={() => handleObjectiveSelection(objective.value)}
                  >
                    <div className="role-icon">{objective.label}</div>
                    <div className="role-description">{objective.description}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Project Type Selection (Type Selection Stage Only) */}
          {currentStage === 'type-selection' && !selectedProjectType && (
            <div className="role-selection">
              <h3>Choose your project type:</h3>
              <div className="role-grid">
                {PROJECT_TYPES.map((type) => (
                  <button 
                    key={type.value}
                    className="role-option-button"
                    onClick={() => handleProjectTypeSelection(type.value)}
                  >
                    <div className="role-icon">{type.label}</div>
                    <div className="role-description">{type.description}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Folder Selection (Folder Selection Stage Only) */}
          {currentStage === 'folder-selection' && !selectedFolderPath && (
            <div className="role-selection">
              <div className="folder-selection-area">
                <button 
                  className="folder-select-button"
                  onClick={handleFolderSelection}
                >
                  📁 Browse for Folder
                </button>
                <div className="folder-selection-divider">or</div>
                <button 
                  className="folder-manual-button"
                  onClick={() => setShowManualFolderInput(true)}
                >
                  ✏️ Enter Path Manually
                </button>
              </div>
              
              {/* Manual Folder Input Modal */}
              {showManualFolderInput && (
                <div className="manual-folder-input">
                  <h4>Enter Project Folder Path</h4>
                  <input
                    type="text"
                    value={manualFolderPath}
                    onChange={(e) => setManualFolderPath(e.target.value)}
                    placeholder="/path/to/your/project"
                    className="folder-path-input"
                    autoFocus
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleManualFolderSubmit();
                      }
                    }}
                  />
                  <div className="folder-input-actions">
                    <button 
                      className="folder-input-cancel"
                      onClick={() => {
                        setShowManualFolderInput(false);
                        setManualFolderPath('');
                      }}
                    >
                      Cancel
                    </button>
                    <button 
                      className="folder-input-submit"
                      onClick={handleManualFolderSubmit}
                      disabled={!manualFolderPath.trim()}
                    >
                      Continue
                    </button>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* AI Analysis Stage - Beautiful animations */}
          {currentStage === 'analysis' && (
            <div className="analysis-container">
              <SpinningBrain 
                size="large" 
                message={analysisProgress?.stage || 'Starting analysis...'}
                showMessage={true}
              />
              
              {analysisProgress && (
                <>
                  <div className="analysis-progress" style={{ marginTop: '2rem', width: '100%', maxWidth: '600px' }}>
                    <div className="progress-bar-container">
                      <div 
                        className="progress-bar-fill" 
                        style={{ width: `${analysisProgress.progress}%` }}
                      ></div>
                    </div>
                    <div className="progress-text">
                      {Math.round(analysisProgress.progress)}% Complete
                      {analysisProgress.currentFile && (
                        <div className="current-file">Analyzing: {analysisProgress.currentFile}</div>
                      )}
                    </div>
                  </div>
                  
                  <div className="analysis-stats" style={{ marginTop: '1rem', display: 'flex', gap: '2rem', justifyContent: 'center' }}>
                    <div className="stat">
                      <span className="stat-value">{analysisProgress.filesAnalyzed}</span>
                      <span className="stat-label">Files Analyzed</span>
                    </div>
                    <div className="stat">
                      <span className="stat-value">{analysisProgress.totalFiles}</span>
                      <span className="stat-label">Total Files</span>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}

          {/* Brutal Honesty Reality Check Stage */}
          {currentStage === 'brutal-honesty-reality-check' && brutalHonestyReport && (
            <div className="brutal-honesty-container" style={{ width: '100%', maxWidth: '800px', textAlign: 'left' }}>
              {/* Project Context Header */}
              {selectedFolderPath && (
                <div style={{ 
                  background: 'rgba(108, 92, 231, 0.1)', 
                  border: '1px solid rgba(108, 92, 231, 0.2)', 
                  borderRadius: '8px', 
                  padding: '1rem',
                  marginBottom: '2rem',
                  textAlign: 'center'
                }}>
                  <h3 style={{ margin: '0 0 0.5rem 0', color: 'var(--text-body)' }}>
                    🚀 Welcome back to your project!
                  </h3>
                  <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)', marginBottom: '0.5rem' }}>
                    📁 <strong>Current Project:</strong> {selectedFolderPath}
                  </div>
                  <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>
                    📊 <strong>Last Analysis:</strong> {new Date().toLocaleDateString()} • Production: {Math.round(brutalHonestyReport.productionReadiness)}%
                  </div>
                </div>
              )}
              
              <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                <div className="avatar-circle" style={{ margin: '0 auto 1rem', backgroundColor: '#ff6b6b' }}>
                  <span className="avatar-emoji">😬</span>
                </div>
                <h2>The brutal truth about your code</h2>
                <p style={{ color: 'var(--text-subtle)', marginBottom: '1.5rem' }}>
                  Ready to see what your project really looks like? We'll tell you the truth.
                </p>
                <div style={{ fontSize: '2rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>
                  Production Readiness: {Math.round(brutalHonestyReport.productionReadiness)}%
                </div>
                <div style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ff6b6b' }}>
                  Overall Grade: {brutalHonestyReport.overallGrade}
                </div>
              </div>

              {/* Detailed Metrics Dashboard */}
              {brutalHonestyReport.detailedMetrics && (
                <div className="detailed-metrics-dashboard" style={{ 
                  background: 'var(--background-secondary)', 
                  border: '1px solid var(--border-color)', 
                  borderRadius: '8px', 
                  padding: '1.5rem',
                  marginBottom: '2rem'
                }}>
                  <h3 style={{ marginBottom: '1.5rem', textAlign: 'center' }}>📊 Detailed Analysis Metrics</h3>
                  <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
                    gap: '1rem',
                    marginBottom: '1rem'
                  }}>
                    <div className="metric-card" style={{ 
                      background: 'var(--background-primary)', 
                      border: '1px solid var(--border-subtle)', 
                      borderRadius: '6px', 
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                        {brutalHonestyReport.detailedMetrics.testCoverage}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>Test Coverage</div>
                      <div style={{ 
                        height: '4px', 
                        background: 'var(--background-tertiary)', 
                        borderRadius: '2px',
                        marginTop: '0.5rem',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          height: '100%', 
                          width: `${brutalHonestyReport.detailedMetrics.testCoverage}%`,
                          background: brutalHonestyReport.detailedMetrics.testCoverage > 80 ? '#22c55e' : 
                                   brutalHonestyReport.detailedMetrics.testCoverage > 50 ? '#f59e0b' : '#ef4444',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                    
                    <div className="metric-card" style={{ 
                      background: 'var(--background-primary)', 
                      border: '1px solid var(--border-subtle)', 
                      borderRadius: '6px', 
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                        {brutalHonestyReport.detailedMetrics.complexityScore}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>Complexity Score</div>
                      <div style={{ 
                        height: '4px', 
                        background: 'var(--background-tertiary)', 
                        borderRadius: '2px',
                        marginTop: '0.5rem',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          height: '100%', 
                          width: `${brutalHonestyReport.detailedMetrics.complexityScore}%`,
                          background: brutalHonestyReport.detailedMetrics.complexityScore > 80 ? '#22c55e' : 
                                   brutalHonestyReport.detailedMetrics.complexityScore > 50 ? '#f59e0b' : '#ef4444',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                    
                    <div className="metric-card" style={{ 
                      background: 'var(--background-primary)', 
                      border: '1px solid var(--border-subtle)', 
                      borderRadius: '6px', 
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                        {brutalHonestyReport.detailedMetrics.maintainability}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>Maintainability</div>
                      <div style={{ 
                        height: '4px', 
                        background: 'var(--background-tertiary)', 
                        borderRadius: '2px',
                        marginTop: '0.5rem',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          height: '100%', 
                          width: `${brutalHonestyReport.detailedMetrics.maintainability}%`,
                          background: brutalHonestyReport.detailedMetrics.maintainability > 80 ? '#22c55e' : 
                                   brutalHonestyReport.detailedMetrics.maintainability > 50 ? '#f59e0b' : '#ef4444',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                    
                    <div className="metric-card" style={{ 
                      background: 'var(--background-primary)', 
                      border: '1px solid var(--border-subtle)', 
                      borderRadius: '6px', 
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                        {brutalHonestyReport.detailedMetrics.securityScore}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>Security Score</div>
                      <div style={{ 
                        height: '4px', 
                        background: 'var(--background-tertiary)', 
                        borderRadius: '2px',
                        marginTop: '0.5rem',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          height: '100%', 
                          width: `${brutalHonestyReport.detailedMetrics.securityScore}%`,
                          background: brutalHonestyReport.detailedMetrics.securityScore > 80 ? '#22c55e' : 
                                   brutalHonestyReport.detailedMetrics.securityScore > 50 ? '#f59e0b' : '#ef4444',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                    
                    <div className="metric-card" style={{ 
                      background: 'var(--background-primary)', 
                      border: '1px solid var(--border-subtle)', 
                      borderRadius: '6px', 
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                        {brutalHonestyReport.detailedMetrics.performanceScore}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>Performance Score</div>
                      <div style={{ 
                        height: '4px', 
                        background: 'var(--background-tertiary)', 
                        borderRadius: '2px',
                        marginTop: '0.5rem',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          height: '100%', 
                          width: `${brutalHonestyReport.detailedMetrics.performanceScore}%`,
                          background: brutalHonestyReport.detailedMetrics.performanceScore > 80 ? '#22c55e' : 
                                   brutalHonestyReport.detailedMetrics.performanceScore > 50 ? '#f59e0b' : '#ef4444',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                    
                    <div className="metric-card" style={{ 
                      background: 'var(--background-primary)', 
                      border: '1px solid var(--border-subtle)', 
                      borderRadius: '6px', 
                      padding: '1rem',
                      textAlign: 'center'
                    }}>
                      <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>
                        {brutalHonestyReport.detailedMetrics.technicalDebt}%
                      </div>
                      <div style={{ fontSize: '0.9rem', color: 'var(--text-subtle)' }}>Technical Debt</div>
                      <div style={{ 
                        height: '4px', 
                        background: 'var(--background-tertiary)', 
                        borderRadius: '2px',
                        marginTop: '0.5rem',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          height: '100%', 
                          width: `${brutalHonestyReport.detailedMetrics.technicalDebt}%`,
                          background: brutalHonestyReport.detailedMetrics.technicalDebt < 20 ? '#22c55e' : 
                                   brutalHonestyReport.detailedMetrics.technicalDebt < 50 ? '#f59e0b' : '#ef4444',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="brutal-honesty-messages" style={{ marginBottom: '2rem' }}>
                <h3>🔥 The Brutal Truth</h3>
                {brutalHonestyReport.messages.map((message: any, index: number) => (
                  <div key={index} className="honesty-message" style={{ 
                    background: 'var(--background-secondary)', 
                    border: '1px solid var(--border-color)', 
                    borderRadius: '8px', 
                    padding: '1rem', 
                    marginBottom: '1rem' 
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.5rem' }}>
                      <span style={{ fontSize: '1.5rem', marginRight: '0.5rem' }}>{message.emoji}</span>
                      <span style={{ fontWeight: 'bold', textTransform: 'uppercase' }}>
                        {message.title} (Grade: {message.grade})
                      </span>
                    </div>
                    <div style={{ fontStyle: 'italic', color: '#ff6b6b', marginBottom: '0.5rem' }}>
                      "{message.brutalmessage}"
                    </div>
                    <div style={{ color: 'var(--text-body)', fontSize: '0.9rem' }}>
                      <strong>How to fix it:</strong> {message.helpfulGuidance}
                    </div>
                  </div>
                ))}
              </div>

              <div className="encouragement" style={{ 
                background: 'var(--background-success)', 
                border: '1px solid var(--border-success)', 
                borderRadius: '8px', 
                padding: '1rem',
                marginBottom: '2rem'
              }}>
                <h4>💪 Don't Worry!</h4>
                <p>{brutalHonestyReport.encouragement}</p>
                <p><strong>Estimated time to fix:</strong> {brutalHonestyReport.timeToFix}</p>
              </div>

              {brutalHonestyReport.funFacts && brutalHonestyReport.funFacts.length > 0 && (
                <div className="fun-facts" style={{ marginBottom: '2rem' }}>
                  <h4>🎉 Fun Facts</h4>
                  <ul>
                    {brutalHonestyReport.funFacts.map((fact: string, index: number) => (
                      <li key={index} style={{ marginBottom: '0.5rem' }}>{fact}</li>
                    ))}
                  </ul>
                </div>
              )}

              {progressiveImprovement && (
                <div className="progressive-improvement" style={{ 
                  background: 'rgba(108, 92, 231, 0.1)', 
                  border: '1px solid rgba(108, 92, 231, 0.2)', 
                  borderRadius: '8px', 
                  padding: '1rem',
                  marginBottom: '2rem'
                }}>
                  <h4>📈 Your Improvement Journey</h4>
                  
                  {progressiveImprovement.trends && (
                    <div style={{ marginBottom: '1rem' }}>
                      <h5>Trends Detected:</h5>
                      {progressiveImprovement.trends.productionReadiness && (
                        <div style={{ marginBottom: '0.5rem' }}>
                          <strong>Production Readiness:</strong>{' '}
                          <span style={{ 
                            color: progressiveImprovement.trends.productionReadiness.direction === 'improving' ? '#22c55e' : 
                                   progressiveImprovement.trends.productionReadiness.direction === 'declining' ? '#ef4444' : '#6b7280'
                          }}>
                            {progressiveImprovement.trends.productionReadiness.direction === 'improving' ? '📈 Improving' :
                             progressiveImprovement.trends.productionReadiness.direction === 'declining' ? '📉 Declining' : '➡️ Stable'}
                            {progressiveImprovement.trends.productionReadiness.change > 0 ? 
                              ` (+${Math.round(progressiveImprovement.trends.productionReadiness.change)}%)` :
                              progressiveImprovement.trends.productionReadiness.change < 0 ?
                              ` (${Math.round(progressiveImprovement.trends.productionReadiness.change)}%)` : ''}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {progressiveImprovement.recommendations && progressiveImprovement.recommendations.length > 0 && (
                    <div style={{ marginBottom: '1rem' }}>
                      <h5>💡 Smart Next Steps:</h5>
                      <ul style={{ marginLeft: '1rem' }}>
                        {progressiveImprovement.recommendations.map((rec: string, index: number) => (
                          <li key={index} style={{ marginBottom: '0.3rem', fontSize: '0.9rem' }}>{rec}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {progressiveImprovement.historicalData && progressiveImprovement.historicalData.length > 1 && (
                    <div style={{ fontSize: '0.9rem', color: 'var(--text-body)' }}>
                      📊 Analyzed {progressiveImprovement.historicalData.length} historical reports from your .kapi folder
                    </div>
                  )}
                </div>
              )}

              {/* Interactive Actions Section */}
              <div className="interactive-actions" style={{ 
                background: 'rgba(108, 92, 231, 0.1)', 
                border: '1px solid rgba(108, 92, 231, 0.2)', 
                borderRadius: '8px', 
                padding: '1.5rem',
                marginBottom: '2rem'
              }}>
                <h4 style={{ textAlign: 'center', marginBottom: '1rem' }}>🤝 Let's Collaborate on Improvements</h4>
                <p style={{ textAlign: 'center', marginBottom: '1.5rem', color: 'var(--text-body)' }}>
                  What would you like to focus on first? Click on any area below to start working together:
                </p>
                
                <div style={{ 
                  display: 'grid', 
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
                  gap: '1rem',
                  marginBottom: '1.5rem'
                }}>
                  {brutalHonestyReport.messages.map((message: any, index: number) => (
                    <button
                      key={index}
                      className="secondary-button"
                      onClick={() => {
                        // Add message to conversation about this specific category
                        const improvementMessage = `I'd like to focus on improving ${message.category}. You mentioned: "${message.brutalmessage}". ${message.helpfulGuidance} Can you help me create a specific action plan for this?`;
                        sendTextMessage(improvementMessage, 'project_onboarding');
                        setCurrentStage('discovery');
                      }}
                      style={{ 
                        padding: '12px 16px', 
                        fontSize: '0.9rem',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: '0.5rem',
                        minHeight: '80px',
                        background: 'var(--background-primary)',
                        border: '1px solid var(--border-color)',
                        borderRadius: '6px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'var(--background-secondary)';
                        e.currentTarget.style.transform = 'translateY(-2px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'var(--background-primary)';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}
                    >
                      <span style={{ fontSize: '1.5rem' }}>{message.emoji}</span>
                      <span style={{ fontWeight: 'bold' }}>{message.title}</span>
                      <span style={{ fontSize: '0.8rem', color: 'var(--text-subtle)' }}>Grade: {message.grade}</span>
                    </button>
                  ))}
                </div>
                
                <div style={{ textAlign: 'center' }}>
                  <button 
                    className="primary-button"
                    onClick={() => {
                      const comprehensiveMessage = `I've reviewed the brutal honesty analysis. My production readiness is ${Math.round(brutalHonestyReport.productionReadiness)}% with an overall grade of ${brutalHonestyReport.overallGrade}. I'd like to work on a comprehensive improvement plan that addresses all the key issues. Can you help me prioritize and create a step-by-step roadmap?`;
                      sendTextMessage(comprehensiveMessage, 'project_onboarding');
                      setCurrentStage('discovery');
                    }}
                    style={{ padding: '12px 24px', fontSize: '1rem', marginRight: '1rem' }}
                  >
                    Create Full Improvement Plan 🚀
                  </button>
                  
                  <button 
                    className="secondary-button"
                    onClick={() => {
                      const explorationMessage = `I want to understand more about my project's current state. Can you help me explore the analysis results and explain what each metric means for my specific project?`;
                      sendTextMessage(explorationMessage, 'project_onboarding');
                      setCurrentStage('discovery');
                    }}
                    style={{ padding: '12px 24px', fontSize: '1rem', marginRight: '1rem' }}
                  >
                    Explore Analysis Details 🔍
                  </button>
                  
                  <button 
                    className="outline-button"
                    onClick={() => {
                      // Clear current project context and start fresh
                      localStorage.removeItem('lastProjectPath');
                      setSelectedFolderPath('');
                      setBrutalHonestyReport(null);
                      setProjectConfig({
                        name: '',
                        type: '',
                        objective: '',
                        folder: '',
                        languages: [],
                        frameworks: [],
                        analysisData: null,
                        healthScore: 0
                      });
                      clearMessages();
                      setCurrentStage('welcome');
                    }}
                    style={{ 
                      padding: '12px 24px', 
                      fontSize: '1rem',
                      border: '2px solid var(--border-color)',
                      background: 'transparent',
                      color: 'var(--text-body)'
                    }}
                  >
                    🆕 Start New Project
                  </button>
                </div>
                
                <p style={{ fontSize: '0.9rem', color: 'var(--text-subtle)', textAlign: 'center', marginTop: '1rem' }}>
                  Continue improving this project or start fresh with a new one!
                </p>
              </div>
            </div>
          )}

          {/* Preview Stage - Shows what will be in Dashboard */}
          {(currentStage === 'health-dashboard' || currentStage === 'architecture' || currentStage === 'roadmap') && (
            <div className="welcome-area">
              <div className="avatar-circle">
                <span className="avatar-emoji">🎉</span>
              </div>
              
              <div className="welcome-message">
                <h2>Project analysis complete</h2>
                <p>Ready to explore your project insights in the dashboard.</p>
              </div>
              
              <div className="role-selection" style={{ marginTop: '2rem' }}>
                <div className="role-grid" style={{ gridTemplateColumns: 'repeat(3, 1fr)', gap: '1rem', maxWidth: '800px' }}>
                  <div className="preview-card">
                    <div className="role-icon" style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>📊</div>
                    <div className="role-description">
                      <strong>Health Dashboard</strong><br/>
                      Interactive project health metrics, quality scores, and insights
                    </div>
                  </div>
                  
                  <div className="preview-card">
                    <div className="role-icon" style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🏗️</div>
                    <div className="role-description">
                      <strong>Architecture View</strong><br/>
                      Visual system components, patterns, and improvement suggestions
                    </div>
                  </div>
                  
                  <div className="preview-card">
                    <div className="role-icon" style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🛣️</div>
                    <div className="role-description">
                      <strong>Improvement Roadmap</strong><br/>
                      Phased plan with prioritized tasks and effort estimates
                    </div>
                  </div>
                </div>
                
                <div style={{ marginTop: '2rem' }}>
                  <button 
                    className="continue-button"
                    onClick={handleContinue}
                  >
                    Open Dashboard →
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Discovery Stage Info - Show guidance when in discovery */}
          {currentStage === 'discovery' && (
            <div className="discovery-guidance">
              <div className="discovery-info">
                <h3>🎯 Project Discovery</h3>
                <p>Tell me about your AI/ML project ideas. I'll help you define requirements and create a comprehensive project plan.</p>
                
                {messages.length === 0 && !isProcessing && (
                  <div className="discovery-prompt">
                    <div className="prompt-card">
                      <h4>💡 Let's start with your project vision</h4>
                      <p>What kind of AI/ML tool do you want to build? Some ideas:</p>
                      <ul>
                        <li>📊 Text analysis and summarization tool</li>
                        <li>🖼️ Image processing application</li>
                        <li>⚡ Productivity automation system</li>
                        <li>🎯 Custom ML model for your domain</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Conversation History - Show after discovery info, inside main content area */}
          {(currentStage === 'discovery' || currentStage === 'configuration') && messages.length > 0 && (
            <div className="conversation-history">
              {messages.map((message) => (
                <div 
                  key={message.id} 
                  className={`message ${message.type === 'ai' ? 'ai-message' : 'user-message'}`}
                >
                  {message.type === 'ai' ? (
                    <MarkdownMessage content={message.content} />
                  ) : (
                    message.content
                  )}
                  {message.mode === 'voice' && (
                    <span className="message-mode-indicator">🎤</span>
                  )}
                </div>
              ))}
              {/* Show streaming content */}
              {isStreaming && streamingContent && (
                <div className="message ai-message streaming">
                  <MarkdownMessage content={streamingContent} />
                </div>
              )}
              {/* Show processing indicator */}
              {isProcessing && !isStreaming && (
                <div className="message ai-message processing">
                  <div className="typing-indicator">
                    <span></span><span></span><span></span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right Sidebar - Discovery Progress */}
        <div className="right-sidebar">
          <div className="sidebar-content">
            <h3>Project Discovery</h3>
            <div className="discovery-card">
              <div className="discovery-stage">
                <strong>Current Focus:</strong>
                {currentStage === 'welcome' && !selectedObjective && ' Understanding your goal'}
                {currentStage === 'welcome' && selectedObjective && ' Goal selected'}
                {currentStage === 'type-selection' && ' Choosing project type'}
                {currentStage === 'folder-selection' && ' Selecting project location'}
                {currentStage === 'analysis' && ' Analyzing project'}
                {currentStage === 'brutal-honesty-reality-check' && ' Reality check complete'}
                {currentStage === 'health-dashboard' && ' Analysis complete'}
                {currentStage === 'architecture' && ' Architecture review'}
                {currentStage === 'roadmap' && ' Improvement planning'}
                {currentStage === 'discovery' && ' Understanding requirements'}
                {currentStage === 'configuration' && ' Technical configuration'}
                {currentStage === 'summary' && ' Final review'}
              </div>
            </div>
            
            <h3>What We're Learning</h3>
            <div className="learning-card">
              {extractedNextQuestions.length > 0 ? (
                extractedNextQuestions.map((question, index) => (
                  <div key={index} className="learning-item">
                    • {question}
                  </div>
                ))
              ) : (
                <>
                  <div className="learning-item">• Your technical goals</div>
                  <div className="learning-item">• Project architecture needs</div>
                  <div className="learning-item">• Development preferences</div>
                </>
              )}
            </div>
            
            <div className="privacy-disclaimer">
              <h4>🔒 Privacy Notice</h4>
              <p>All project memory is stored locally on your computer. Your code and data never leave your machine unless you explicitly choose to sync.</p>
            </div>
            
            {/* Completion Section */}
            {(currentStage === 'discovery' && messages.length >= 3) && (
              <div className="completion-section">
                <button 
                  className="complete-onboarding-button"
                  onClick={handleComplete}
                >
                  Complete Project Setup 🚀
                </button>
                <div className="completion-note">
                  <small>Your project configuration will be saved locally</small>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Text Input Section - Fixed at bottom like user onboarding */}
      {(currentStage === 'discovery' || currentStage === 'configuration') && (
        <div className="text-input-section">
          <div className="input-container">
            <textarea
              className="text-input"
              placeholder="Tell me about your project..."
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  const target = e.target as HTMLTextAreaElement;
                  if (target.value.trim()) {
                    sendTextMessage(target.value, 'project_onboarding');
                    target.value = '';
                  }
                }
              }}
              rows={2}
            />
            <div className="input-actions">
              <button 
                className="send-button"
                onClick={() => {
                  const textarea = document.querySelector('.text-input') as HTMLTextAreaElement;
                  if (textarea && textarea.value.trim()) {
                    sendTextMessage(textarea.value, 'project_onboarding');
                    textarea.value = '';
                  }
                }}
              >
                Send
              </button>
            </div>
          </div>
          <div className="input-hint">(Press Enter to send, Shift+Enter for new line)</div>
        </div>
      )}
      
      <div ref={conversationEndRef} />
    </div>
  );
};

export default ProjectOnboarding;
