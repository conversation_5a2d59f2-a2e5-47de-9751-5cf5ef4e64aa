/* Enhanced Dashboard Styles - Matching SVG Mockup */
.enhanced-dashboard {
  min-height: 100vh;
  background-color: #0d1117;
  color: #f0f6fc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-x: hidden;
}

/* Brutal Honesty Widget Styles */
.brutal-honesty {
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.brutal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.brutal-title {
  font-weight: 600;
  color: #ff6b6b;
  font-size: 14px;
}

.readiness-score {
  font-size: 12px;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: rgba(255, 107, 107, 0.1);
}

.brutal-message {
  font-style: italic;
  color: #f0f6fc;
  margin: 0.5rem 0;
  padding: 0.5rem;
  background-color: rgba(255, 107, 107, 0.05);
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
}

.brutal-guidance {
  color: #7d8590;
  font-size: 12px;
  margin-top: 0.5rem;
}

.metrics-overview {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-top: 1rem;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background-color: #21262d;
  border-radius: 4px;
}

.metric-label {
  font-size: 11px;
  color: #7d8590;
}

.metric-value {
  font-size: 12px;
  font-weight: 600;
  color: #f0f6fc;
}

.next-steps {
  margin-top: 1rem;
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 6px;
  padding: 0.75rem;
}

.steps-header {
  margin-bottom: 0.5rem;
}

.steps-label {
  font-size: 12px;
  font-weight: 600;
  color: #58a6ff;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.step-item {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  font-size: 11px;
  color: #e6edf3;
}

.step-number {
  background-color: #238636;
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  line-height: 1.3;
}

.no-project-message,
.loading-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #7d8590;
  font-size: 14px;
  text-align: center;
}

/* Project Analysis Widget - Brutal Honesty View */
.brutal-honesty-view {
  padding: 1rem;
}

.brutality-header {
  margin-bottom: 1.5rem;
}

.production-readiness {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.readiness-score {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.score-label {
  font-size: 14px;
  color: #e6edf3;
}

.score-value {
  font-size: 18px;
  font-weight: 700;
}

.score-emoji {
  font-size: 20px;
}

.overall-grade {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.grade-label {
  font-size: 14px;
  color: #e6edf3;
}

.grade-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
}

.data-source-indicator {
  margin-top: 0.5rem;
}

.data-badge.real {
  background-color: rgba(255, 107, 107, 0.1);
  color: #ff6b6b;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.brutality-messages {
  margin-bottom: 2rem;
}

.brutal-message {
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.message-emoji {
  font-size: 16px;
}

.message-title {
  font-weight: 600;
  color: #e6edf3;
}

.message-brutal {
  font-style: italic;
  color: #f0f6fc;
  margin: 0.75rem 0;
  line-height: 1.5;
}

.message-guidance {
  color: #7d8590;
  font-size: 14px;
}

.encouragement-section {
  margin-bottom: 2rem;
  padding: 1rem;
  background-color: rgba(56, 139, 253, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(56, 139, 253, 0.2);
}

.encouragement-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.encouragement-emoji {
  font-size: 20px;
}

.encouragement-text {
  color: #e6edf3;
  line-height: 1.5;
}

.next-steps-section {
  margin-bottom: 2rem;
}

.next-steps-section h4 {
  color: #f0f6fc;
  margin-bottom: 1rem;
  font-size: 16px;
}

.next-steps-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.next-step {
  background-color: #21262d;
  padding: 0.75rem;
  border-radius: 6px;
  border-left: 3px solid #238636;
  color: #e6edf3;
  font-size: 14px;
}

.fun-facts-section,
.detailed-metrics-section {
  margin-bottom: 2rem;
}

.fun-facts-section h4,
.detailed-metrics-section h4 {
  color: #f0f6fc;
  margin-bottom: 1rem;
  font-size: 16px;
}

.fun-facts-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fun-fact {
  color: #7d8590;
  font-size: 14px;
  line-height: 1.4;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.metric-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.metric-card .metric-label {
  display: block;
  color: #7d8590;
  font-size: 12px;
  margin-bottom: 0.5rem;
}

.metric-card .metric-value {
  display: block;
  color: #f0f6fc;
  font-size: 18px;
  font-weight: 600;
}

.no-brutal-honesty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.prompt-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.prompt-text h4 {
  color: #f0f6fc;
  margin-bottom: 0.5rem;
}

.prompt-text p {
  color: #7d8590;
  margin-bottom: 1.5rem;
}

.analyze-button {
  background-color: #238636;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.analyze-button:hover {
  background-color: #2ea043;
}

.analyze-button:disabled {
  background-color: #30363d;
  color: #7d8590;
  cursor: not-allowed;
}

/* Memory Dashboard Widget Specific Styles */

/* Loading states */
.memory-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  min-height: 200px;
}

.loading-spinner {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 14px;
  color: #7d8590;
  margin-top: 1rem;
}

/* Memory layer cards */
.memory-layers-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.memory-layer-card {
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 12px;
  padding: 1.5rem;
}

.memory-layer-card.user-memory {
  border-left: 4px solid #ffa657;
}

.memory-layer-card.project-memory {
  border-left: 4px solid #58a6ff;
}

.layer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.layer-icon {
  font-size: 1.5rem;
  margin-right: 0.5rem;
}

.layer-location {
  font-size: 11px;
  color: #7d8590;
  background-color: #21262d;
  padding: 2px 6px;
  border-radius: 4px;
}

/* Memory sources indicators */
.memory-sources {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.source-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 12px;
  color: #7d8590;
}

.source-icon {
  font-size: 14px;
}

/* Profile sections */
.profile-sections-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.profile-section {
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 12px;
  padding: 1.5rem;
}

.profile-section h4 {
  margin: 0 0 1rem 0;
  color: #f0f6fc;
  font-size: 16px;
  font-weight: 600;
}

.profile-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detail-label {
  font-size: 12px;
  color: #7d8590;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #f0f6fc;
}

/* Insights sections */
.insights-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1.5rem;
}

.insights-section h4 {
  margin: 0 0 1rem 0;
  color: #f0f6fc;
  font-size: 16px;
  font-weight: 600;
}

.insights-list, .predictions-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.insight-item, .prediction-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 8px;
}

.insight-icon, .prediction-icon {
  font-size: 16px;
  color: #58a6ff;
}

.insight-text, .prediction-text {
  flex: 1;
  font-size: 14px;
  color: #f0f6fc;
}

.insight-confidence {
  font-size: 12px;
  color: #7d8590;
  background-color: #21262d;
  padding: 2px 6px;
  border-radius: 4px;
}

/* YAML Editor Styles */
.yaml-editor-section {
  max-width: 100%;
}

.yaml-editor-container {
  background-color: #161b22;
  border: 1px solid #30363d;
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.yaml-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #30363d;
}

.editor-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.editor-icon {
  font-size: 16px;
}

.editor-warning {
  font-size: 14px;
  color: #ffa657;
}

.editor-controls {
  display: flex;
  gap: 0.5rem;
}

.yaml-button {
  padding: 6px 12px;
  border: 1px solid #30363d;
  border-radius: 6px;
  background-color: #21262d;
  color: #f0f6fc;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.yaml-button:hover {
  background-color: #30363d;
  border-color: #656d76;
}

.yaml-button.primary {
  background-color: #238636;
  border-color: #238636;
  color: white;
}

.yaml-button.primary:hover {
  background-color: #2ea043;
  border-color: #2ea043;
}

.yaml-editor {
  margin: 1rem 0;
}

.yaml-textarea {
  width: 100%;
  min-height: 400px;
  background-color: #0d1117;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 1rem;
  color: #f0f6fc;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  resize: vertical;
}

.yaml-textarea:focus {
  outline: none;
  border-color: #58a6ff;
  box-shadow: 0 0 0 3px rgba(88, 166, 255, 0.1);
}

.yaml-textarea:read-only {
  background-color: #161b22;
  border-color: #21262d;
  color: #8b949e;
}

.yaml-error {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #da3633;
  border-radius: 6px;
  margin-top: 1rem;
}

.error-icon {
  font-size: 16px;
  color: white;
}

.error-text {
  color: white;
  font-size: 14px;
}

.yaml-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

/* Responsive adjustments for memory widget */
@media (max-width: 768px) {
  .memory-layers-grid,
  .profile-sections-grid,
  .insights-container {
    grid-template-columns: 1fr;
  }
  
  .yaml-editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .yaml-actions {
    justify-content: flex-start;
    flex-wrap: wrap;
  }
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  background-color: #21262d;
  border-bottom: 1px solid #30363d;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 100;
}

/* Widget Header with Expand Button */
.widget-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.expand-button {
  background: none;
  border: 1px solid #30363d;
  color: #7d8590;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.expand-button:hover {
  background-color: #21262d;
  border-color: #656d76;
  color: #f0f6fc;
}

.expand-button:active {
  background-color: #30363d;
  transform: scale(0.95);
}

.dashboard-header-left {
  display: flex;
  align-items: center;
}

.dashboard-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kapi-logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.dashboard-title {
  font-size: 16px;
  font-weight: 600;
  color: #f0f6fc;
  margin: 0;
}

.dashboard-header-nav {
  display: flex;
  align-items: center;
}

.nav-buttons {
  display: flex;
  gap: 10px;
}

.nav-button {
  padding: 8px 16px;
  border: 1px solid #30363d;
  border-radius: 15px;
  background-color: #21262d;
  color: #f0f6fc;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background-color: #30363d;
}

.nav-button-active {
  background-color: rgba(136, 105, 186, 0.3);
  color: #8869ba;
  border-color: #8869ba;
}

.dashboard-header-right {
  display: flex;
  align-items: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #30363d;
  border-radius: 15px;
  background-color: #21262d;
}

.user-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #238636;
}

.user-name {
  font-size: 11px;
  color: #f0f6fc;
}

.user-karma {
  font-size: 11px;
  color: #7d8590;
}

/* Project Context Header */
.project-context-header {
  background-color: #161b22;
  border: 1px solid #21262d;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
}

.project-path {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.folder-icon {
  font-size: 12px;
  color: #7d8590;
}

.project-path-text {
  font-size: 13px;
  color: #f0f6fc;
  font-weight: 500;
}

.project-description {
  font-size: 13px;
  color: #c9d1d9;
  line-height: 1.6;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #30363d;
  font-weight: 400;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 20px;
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  height: calc(100vh - 240px);
}

.widget-container {
  min-height: 280px;
  max-height: 350px;
}

/* Widget Base Styles */
.dashboard-widget {
  background-color: #161b22;
  border: 1px solid #21262d;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.widget-title {
  font-size: 14px;
  font-weight: 600;
  color: #f0f6fc;
  margin: 0 0 15px 0;
}

.widget-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  min-height: 0;
}

.widget-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
  padding-top: 15px;
}

.action-button {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-button.primary {
  background-color: #238636;
  color: #fff;
}

.action-button.primary:hover {
  background-color: #2ea043;
}

.action-button.secondary {
  background-color: #21262d;
  color: #58a6ff;
  border: 1px solid #58a6ff;
}

.action-button.secondary:hover {
  background-color: #30363d;
}

.action-button.critical {
  background-color: #da3633;
  color: #fff;
}

.action-button.critical:hover {
  background-color: #e5534b;
}

/* Project Health Overview */
.project-health-overview .health-score-section {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  margin-bottom: 20px;
}

.health-score-circle {
  position: relative;
  width: 70px;
  height: 70px;
}

.score-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.score-number {
  display: block;
  font-size: 20px;
  font-weight: 600;
  color: #238636;
}

.score-label {
  font-size: 10px;
  color: #7d8590;
}

.health-metrics {
  flex: 1;
}

.production-readiness {
  margin-bottom: 15px;
}

.metric-label {
  font-size: 12px;
  color: #f0f6fc;
  display: block;
  margin-bottom: 5px;
}

.progress-bar {
  width: 100%;
  max-width: 200px;
  height: 8px;
  background-color: #21262d;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background-color: #ffa657;
  border-radius: 4px;
}

.metric-value {
  font-size: 11px;
  color: #ffa657;
}

.key-metrics {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.metric {
  font-size: 11px;
}

.metric.good {
  color: #238636;
}

.metric.warning {
  color: #f78166;
}

.trend-section {
  margin-bottom: 15px;
}

.trend-label {
  font-size: 10px;
  color: #7d8590;
  display: block;
  margin-bottom: 5px;
}

.trend-text {
  font-size: 10px;
  color: #238636;
}

/* Security Analysis */
.security-analysis .security-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.grade-badge {
  width: 40px;
  height: 30px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
}

.grade-c {
  background-color: rgba(247, 129, 102, 0.2);
  color: #f78166;
}

.security-info {
  display: flex;
  flex-direction: column;
}

.security-label {
  font-size: 12px;
  color: #f0f6fc;
}

.vulnerability-count {
  font-size: 10px;
  color: #7d8590;
}

.vulnerability-breakdown {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.vulnerability-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.severity-indicator {
  width: 15px;
  height: 15px;
  border-radius: 2px;
}

.severity-indicator.critical {
  background-color: #da3633;
}

.severity-indicator.high {
  background-color: #f78166;
}

.severity-indicator.medium {
  background-color: #ffa657;
}

.vulnerability-type {
  font-size: 11px;
  color: #f0f6fc;
  width: auto;
  min-width: 70px;
}

.vulnerability-description {
  font-size: 10px;
  color: #7d8590;
  flex: 1;
}

.security-timeline {
  margin-bottom: 20px;
}

.timeline-label {
  font-size: 10px;
  color: #7d8590;
  display: block;
  margin-bottom: 5px;
}

.timeline-chart {
  display: flex;
  align-items: center;
  gap: 5px;
}

.timeline-item {
  width: 20px;
  height: 20px;
  border-radius: 2px;
}

.timeline-item.critical {
  background-color: #da3633;
}

.timeline-item.high {
  background-color: #f78166;
}

.timeline-item.good {
  background-color: #238636;
}

.timeline-text {
  font-size: 9px;
  color: #7d8590;
  margin-left: 10px;
}

/* AI Assistant */
.ai-assistant .ai-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.ai-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #8869ba;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-circle {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
}

.ai-info {
  display: flex;
  flex-direction: column;
}

.ai-model {
  font-size: 12px;
  color: #f0f6fc;
}

.ai-confidence {
  font-size: 10px;
  color: #7d8590;
}

.recommendations {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.recommendation-item {
  background-color: #0d1117;
  border-radius: 5px;
  padding: 10px;
}

.recommendation-item.priority {
  border-left: 3px solid #58a6ff;
}

.recommendation-header {
  margin-bottom: 5px;
}

.recommendation-type {
  font-size: 10px;
  color: #58a6ff;
}

.recommendation-text {
  font-size: 10px;
  color: #f0f6fc;
  line-height: 1.4;
}

/* Documentation Sync */
.documentation-sync .sync-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.sync-label {
  font-size: 12px;
  color: #f0f6fc;
  width: auto;
  flex: 1;
}

.sync-percentage {
  font-size: 11px;
  color: #ffa657;
  width: 30px;
}

.drift-analysis {
  margin-bottom: 20px;
}

.drift-header {
  margin-bottom: 10px;
}

.drift-label {
  font-size: 11px;
  color: #f0f6fc;
}

.drift-items {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.drift-item {
  font-size: 10px;
  color: #f78166;
}

.auto-fix-section {
  margin-bottom: 20px;
}

.auto-fix-header {
  margin-bottom: 10px;
}

.auto-fix-label {
  font-size: 11px;
  color: #238636;
}

.auto-fix-items {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.auto-fix-item {
  font-size: 10px;
  color: #238636;
}

.coverage-heatmap {
  margin-bottom: 20px;
}

.heatmap-label {
  font-size: 10px;
  color: #7d8590;
  display: block;
  margin-bottom: 5px;
}

.heatmap-grid {
  display: flex;
  gap: 5px;
  margin-bottom: 5px;
}

.heatmap-cell {
  width: 20px;
  height: 20px;
  border-radius: 2px;
}

.heatmap-cell.good {
  background-color: #238636;
}

.heatmap-cell.warning {
  background-color: #ffa657;
}

.heatmap-cell.critical {
  background-color: #da3633;
}

.heatmap-labels {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.heatmap-label-item {
  font-size: 9px;
  color: #7d8590;
}

/* Git Activity */
.git-activity .branch-status {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.branch-info {
  font-size: 11px;
  color: #f0f6fc;
}

.branch-status-clean {
  font-size: 11px;
  color: #238636;
}

.branch-changes {
  font-size: 11px;
  color: #7d8590;
}

.recent-commits {
  margin-bottom: 20px;
}

.commits-header {
  margin-bottom: 10px;
}

.commits-label {
  font-size: 11px;
  color: #f0f6fc;
}

.commit-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.commit-item {
  background-color: #0d1117;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.commit-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.commit-indicator.good {
  background-color: #238636;
}

.commit-indicator.warning {
  background-color: #ffa657;
}

.commit-message {
  font-size: 9px;
  color: #f0f6fc;
  flex: 1;
}

.commit-time {
  font-size: 9px;
  color: #7d8590;
}

.activity-chart {
  margin-bottom: 20px;
}

.chart-label {
  font-size: 10px;
  color: #7d8590;
  display: block;
  margin-bottom: 5px;
}

.chart-bars {
  display: flex;
  align-items: end;
  gap: 5px;
}

.activity-bar {
  width: 10px;
  background-color: #238636;
  border-radius: 2px;
}

.team-activity {
  margin-bottom: 20px;
}

.team-label {
  font-size: 10px;
  color: #7d8590;
  display: block;
  margin-bottom: 5px;
}

.team-stats {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.team-stat {
  font-size: 9px;
  color: #f0f6fc;
}

/* Code Quality Analysis */
.code-quality-analysis .brutal-honesty {
  background-color: rgba(218, 54, 51, 0.1);
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 20px;
}

.brutal-header {
  margin-bottom: 5px;
}

.brutal-title {
  font-size: 11px;
  font-weight: 600;
  color: #f78166;
}

.brutal-message {
  font-size: 10px;
  color: #f0f6fc;
  margin-bottom: 5px;
}

.brutal-guidance {
  font-size: 9px;
  color: #7d8590;
}

.complexity-hotspots {
  margin-bottom: 20px;
}

.hotspots-header {
  margin-bottom: 10px;
}

.hotspots-label {
  font-size: 11px;
  color: #f0f6fc;
}

.hotspot-list {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.hotspot-item {
  background-color: #0d1117;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.complexity-indicator {
  width: 15px;
  height: 10px;
  border-radius: 2px;
}

.complexity-indicator.critical {
  background-color: #da3633;
}

.complexity-indicator.warning {
  background-color: #ffa657;
}

.complexity-indicator.good {
  background-color: #238636;
}

.file-name {
  font-size: 9px;
  color: #f0f6fc;
  flex: 1;
}

.complexity-score {
  font-size: 9px;
  color: #da3633;
}

.technical-debt {
  margin-bottom: 20px;
}

.debt-label {
  font-size: 10px;
  color: #7d8590;
}

/* Performance Optimization */
.performance-optimization .core-web-vitals {
  margin-bottom: 20px;
}

.vitals-header {
  margin-bottom: 10px;
}

.vitals-label {
  font-size: 11px;
  color: #f0f6fc;
}

.vitals-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.vital-metric {
  display: flex;
  align-items: center;
  gap: 10px;
}

.metric-name {
  font-size: 10px;
  color: #f0f6fc;
  width: 80px;
}

.metric-bar {
  width: 100%;
  max-width: 100px;
  height: 8px;
  background-color: #21262d;
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  border-radius: 4px;
}

.metric-fill.good {
  background-color: #238636;
}

.metric-fill.warning {
  background-color: #ffa657;
}

.optimizations {
  margin-bottom: 20px;
}

.optimizations-header {
  margin-bottom: 10px;
}

.optimizations-label {
  font-size: 11px;
  color: #f0f6fc;
}

.optimization-list {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.optimization-item {
  font-size: 10px;
  color: #238636;
}

.bundle-analysis {
  margin-bottom: 20px;
}

.bundle-header {
  margin-bottom: 5px;
}

.bundle-label {
  font-size: 10px;
  color: #7d8590;
}

.bundle-bar {
  width: 100%;
  max-width: 200px;
  height: 8px;
  background-color: #21262d;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.bundle-fill {
  height: 100%;
  background-color: #ffa657;
  border-radius: 4px;
}

.bundle-details {
  margin-top: 5px;
}

.bundle-text {
  font-size: 9px;
  color: #7d8590;
}

/* Test Quality */
.test-quality .coverage-overview {
  margin-bottom: 20px;
}

.coverage-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.coverage-icon {
  font-size: 14px;
}

.coverage-label {
  font-size: 12px;
  color: #f0f6fc;
}

.coverage-main {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.coverage-percentage {
  font-size: 11px;
  color: #f0f6fc;
}

.coverage-breakdown {
  margin-bottom: 20px;
}

.breakdown-header {
  margin-bottom: 10px;
}

.breakdown-label {
  font-size: 11px;
  color: #f0f6fc;
}

.breakdown-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.breakdown-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.breakdown-type {
  font-size: 10px;
  color: #f0f6fc;
}

.breakdown-value {
  font-size: 10px;
  font-weight: 500;
}

.breakdown-value.good {
  color: #238636;
}

.breakdown-value.warning {
  color: #f78166;
}

.breakdown-value.critical {
  color: #da3633;
}

.quality-metrics {
  margin-bottom: 20px;
}

.metrics-header {
  margin-bottom: 10px;
}

.metrics-label {
  font-size: 11px;
  color: #f0f6fc;
}

.metrics-items {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.metric-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.metric-name {
  font-size: 10px;
  color: #f0f6fc;
}

.metric-value {
  font-size: 10px;
  font-weight: 500;
}

.metric-value.good {
  color: #238636;
}

.achievement-progress {
  margin-bottom: 20px;
}

.achievement-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.achievement-icon {
  font-size: 12px;
}

.achievement-text {
  font-size: 10px;
  color: #58a6ff;
}

.achievement-bar {
  width: 100%;
  height: 6px;
  background-color: #21262d;
  border-radius: 3px;
  overflow: hidden;
}

.achievement-fill {
  height: 100%;
  background-color: #58a6ff;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* Fullscreen Modal */
.fullscreen-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.fullscreen-modal {
  background-color: #0d1117;
  border-radius: 12px;
  width: 95vw;
  height: 95vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #30363d;
}

.fullscreen-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #21262d;
  background-color: #161b22;
}

.fullscreen-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #f0f6fc;
  margin: 0;
}

.fullscreen-modal-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.fullscreen-modal-close {
  background: none;
  border: none;
  color: #7d8590;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.fullscreen-modal-close:hover {
  color: #f0f6fc;
  background-color: #21262d;
}

.fullscreen-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Expanded Views Common Styles */
.expanded-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background-color: #161b22;
  border-radius: 12px;
  border: 1px solid #21262d;
}

.expanded-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #f0f6fc;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Code Quality Expanded */
.code-quality-expanded {
  color: #f0f6fc;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.overall-grade {
  display: flex;
  align-items: center;
  gap: 24px;
}

.grade-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: rgba(247, 129, 102, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 3px solid #f78166;
}

.grade-letter {
  font-size: 36px;
  font-weight: 700;
  color: #f78166;
}

.grade-label {
  font-size: 12px;
  color: #7d8590;
  margin-top: 4px;
}

.grade-description h3 {
  font-size: 24px;
  margin: 0 0 8px 0;
  color: #f0f6fc;
}

.grade-description p {
  font-size: 14px;
  color: #7d8590;
  margin: 0;
}

.brutal-honesty-expanded {
  background-color: #161b22;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #21262d;
}

.main-message {
  margin-bottom: 24px;
}

.brutal-text {
  font-size: 16px;
  color: #f78166;
  margin-bottom: 12px;
  font-style: italic;
}

.guidance-text {
  font-size: 14px;
  color: #238636;
  margin: 0;
}

.detailed-issues h4 {
  font-size: 16px;
  color: #f0f6fc;
  margin-bottom: 16px;
}

.issue-card {
  background-color: #0d1117;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid #21262d;
}

.issue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.issue-category {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.issue-category.critical {
  background-color: rgba(218, 54, 51, 0.2);
  color: #da3633;
}

.issue-category.high {
  background-color: rgba(247, 129, 102, 0.2);
  color: #f78166;
}

.issue-location {
  font-size: 12px;
  color: #7d8590;
  font-family: monospace;
}

.issue-message {
  font-size: 14px;
  color: #f0f6fc;
  margin-bottom: 8px;
}

.issue-fix {
  font-size: 13px;
  color: #238636;
}

.hotspots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 16px;
}

.hotspot-card {
  background-color: #161b22;
  border: 1px solid #21262d;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hotspot-card:hover {
  border-color: #30363d;
  background-color: #21262d;
}

.hotspot-card.selected {
  border-color: #8869ba;
  background-color: rgba(136, 105, 186, 0.1);
}

.hotspot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.file-name {
  font-size: 14px;
  color: #f0f6fc;
  font-weight: 500;
}

.complexity-score {
  font-size: 18px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
}

.complexity-score.critical {
  background-color: rgba(218, 54, 51, 0.2);
  color: #da3633;
}

.complexity-score.warning {
  background-color: rgba(255, 166, 87, 0.2);
  color: #ffa657;
}

.complexity-score.good {
  background-color: rgba(35, 134, 54, 0.2);
  color: #238636;
}

.hotspot-metrics {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
}

.metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.metric-label {
  font-size: 12px;
  color: #7d8590;
}

.metric-value {
  font-size: 14px;
  color: #f0f6fc;
  font-weight: 500;
}

.hotspot-issues h5 {
  font-size: 13px;
  color: #f0f6fc;
  margin-bottom: 8px;
}

.hotspot-issues ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.hotspot-issues li {
  font-size: 12px;
  color: #7d8590;
  margin-bottom: 4px;
  padding-left: 16px;
  position: relative;
}

.hotspot-issues li::before {
  content: "•";
  position: absolute;
  left: 0;
  color: #8869ba;
}

.hotspot-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-weight: 500;
}

.action-btn.primary {
  background-color: #238636;
  color: #fff;
}

.action-btn.primary:hover {
  background-color: #2ea043;
}

.action-btn.secondary {
  background-color: #21262d;
  color: #58a6ff;
  border: 1px solid #58a6ff;
}

.action-btn.secondary:hover {
  background-color: #30363d;
}

.action-btn.large {
  padding: 12px 24px;
  font-size: 14px;
}

.debt-overview {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  background-color: #161b22;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #21262d;
}

.debt-total {
  text-align: center;
}

.debt-total h4 {
  font-size: 16px;
  color: #f0f6fc;
  margin-bottom: 12px;
}

.debt-amount {
  font-size: 36px;
  font-weight: 700;
  color: #ffa657;
  margin-bottom: 8px;
}

.debt-time {
  font-size: 14px;
  color: #7d8590;
}

.debt-breakdown h4 {
  font-size: 16px;
  color: #f0f6fc;
  margin-bottom: 16px;
}

.debt-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.debt-category {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-name {
  font-size: 14px;
  color: #f0f6fc;
}

.category-percentage {
  font-size: 12px;
  color: #7d8590;
}

.debt-bar {
  width: 100px;
  height: 6px;
  background-color: #21262d;
  border-radius: 3px;
  overflow: hidden;
}

.debt-fill {
  height: 100%;
  background-color: #ffa657;
  border-radius: 3px;
}

/* Roadmap Timeline - Specific styling to avoid conflicts */
.code-quality-expanded .roadmap-timeline {
  background-color: #161b22;
  border-radius: 12px;
  padding: 40px;
  border: 1px solid #21262d;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-bottom: 40px;
}

.code-quality-expanded .roadmap-timeline .timeline-item {
  display: flex;
  flex-direction: column;
  background-color: #0d1117;
  border-radius: 8px;
  padding: 24px;
  border: 1px solid #30363d;
  position: relative;
  min-height: 220px;
  width: 100%;
}

.code-quality-expanded .roadmap-timeline .timeline-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  position: absolute;
  top: 12px;
  right: 12px;
}

.code-quality-expanded .roadmap-timeline .timeline-marker.critical {
  background-color: #da3633;
}

.code-quality-expanded .roadmap-timeline .timeline-marker.warning {
  background-color: #ffa657;
}

.code-quality-expanded .roadmap-timeline .timeline-marker.good {
  background-color: #238636;
}

.code-quality-expanded .roadmap-timeline .timeline-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.code-quality-expanded .roadmap-timeline .timeline-content h4 {
  font-size: 14px;
  color: #f0f6fc;
  margin: 0 0 12px 0;
  font-weight: 600;
  padding-right: 20px;
  line-height: 1.3;
}

.code-quality-expanded .roadmap-timeline .timeline-content ul {
  list-style: none;
  padding: 0;
  margin: 0 0 12px 0;
  flex: 1;
}

.code-quality-expanded .roadmap-timeline .timeline-content li {
  font-size: 12px;
  color: #7d8590;
  margin-bottom: 6px;
  padding-left: 16px;
  position: relative;
  line-height: 1.4;
}

.code-quality-expanded .roadmap-timeline .timeline-content li::before {
  content: "•";
  position: absolute;
  left: 0;
  top: 2px;
  color: #8869ba;
  font-weight: bold;
}

.code-quality-expanded .roadmap-timeline .timeline-impact {
  font-size: 11px;
  color: #238636;
  font-weight: 600;
  background-color: rgba(35, 134, 54, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-top: auto;
}

.code-quality-expanded .expanded-actions {
  margin-top: 60px;
  position: relative;
  z-index: 10;
}

.expanded-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  padding: 24px;
  background-color: #161b22;
  border-radius: 12px;
  border: 1px solid #21262d;
  margin-top: 40px;
  flex-shrink: 0;
  clear: both;
}

/* Recent Activity */
.recent-activity .timeline-header {
  margin-bottom: 15px;
}

.timeline-label {
  font-size: 11px;
  color: #f0f6fc;
}

.timeline-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.timeline-item {
  background-color: #0d1117;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.timeline-indicator {
  width: 15px;
  height: 15px;
  border-radius: 2px;
}

.timeline-indicator.good {
  background-color: #238636;
}

.timeline-indicator.info {
  background-color: #58a6ff;
}

.timeline-indicator.warning {
  background-color: #ffa657;
}

.timeline-text {
  font-size: 9px;
  color: #f0f6fc;
  flex: 1;
}

.timeline-time {
  font-size: 9px;
  color: #7d8590;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 15px;
  }
  
  .dashboard-header {
    padding: 0 15px;
  }
  
  .project-context-header {
    margin: 15px;
  }
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    padding: 10px;
  }
  
  .dashboard-header {
    flex-direction: column;
    height: auto;
    padding: 15px 10px;
    gap: 10px;
  }
  
  .dashboard-header-nav {
    order: -1;
  }
  
  .nav-buttons {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .nav-button {
    padding: 6px 12px;
    font-size: 10px;
  }
  
  .project-context-header {
    margin: 10px;
    padding: 15px;
  }
  
  .widget-container {
    min-height: 200px;
  }
  
  .dashboard-widget {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .dashboard-grid {
    gap: 10px;
    padding: 10px;
  }
  
  .dashboard-header {
    padding: 10px;
  }
  
  .project-context-header {
    margin: 10px;
    padding: 10px;
  }
  
  .dashboard-widget {
    padding: 10px;
  }
  
  .widget-title {
    font-size: 12px;
  }
}

/* Security Expanded Styles */
.security-expanded {
  color: #f0f6fc;
}

.security-overview {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.security-grade-large {
  display: flex;
  align-items: center;
  gap: 20px;
}

.grade-letter-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #da3633;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  font-weight: bold;
}

.grade-description {
  font-size: 14px;
  color: #7d8590;
  line-height: 1.5;
}

.severity-breakdown {
  display: flex;
  gap: 24px;
}

.severity-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.severity-count .count {
  font-size: 24px;
  font-weight: bold;
}

.severity-count .label {
  font-size: 12px;
  color: #7d8590;
  text-transform: uppercase;
}

.severity-count.critical .count {
  color: #da3633;
}

.severity-count.high .count {
  color: #f78166;
}

.severity-count.medium .count {
  color: #ffa657;
}

.severity-count.low .count {
  color: #7d8590;
}

/* Tabs */
.expanded-tabs {
  display: flex;
  gap: 2px;
  margin: 20px 0;
  border-bottom: 1px solid #30363d;
}

.tab-button {
  background: none;
  border: none;
  color: #7d8590;
  padding: 12px 16px;
  cursor: pointer;
  font-size: 14px;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #f0f6fc;
  background-color: #21262d;
}

.tab-button.active {
  color: #f0f6fc;
  border-bottom-color: #238636;
}

/* Vulnerabilities */
.vulnerabilities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vulnerability-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
}

.vulnerability-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.vulnerability-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.vulnerability-id {
  font-size: 12px;
  color: #7d8590;
  font-family: monospace;
}

.vulnerability-title h4 {
  margin: 0;
  font-size: 16px;
  color: #f0f6fc;
}

.severity-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.severity-badge.critical {
  background-color: #da3633;
  color: white;
}

.severity-badge.high {
  background-color: #f78166;
  color: white;
}

.severity-badge.medium {
  background-color: #ffa657;
  color: white;
}

.vulnerability-description {
  color: #7d8590;
  margin-bottom: 16px;
  line-height: 1.5;
}

.vulnerability-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  gap: 12px;
}

.detail-label {
  font-weight: 600;
  color: #f0f6fc;
  min-width: 80px;
}

.detail-value {
  color: #7d8590;
  font-family: monospace;
  font-size: 13px;
}

.vulnerability-actions {
  display: flex;
  gap: 8px;
}

/* Compliance */
.compliance-frameworks {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.compliance-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
}

.compliance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.compliance-header h4 {
  margin: 0;
  color: #f0f6fc;
}

.compliance-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.compliance-status.passing {
  background-color: #238636;
  color: white;
}

.compliance-status.warning {
  background-color: #ffa657;
  color: white;
}

.compliance-status.failing {
  background-color: #da3633;
  color: white;
}

.compliance-score {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.score-bar {
  flex: 1;
  height: 8px;
  background-color: #30363d;
  border-radius: 4px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.score-fill.passing {
  background-color: #238636;
}

.score-fill.warning {
  background-color: #ffa657;
}

.score-fill.failing {
  background-color: #da3633;
}

.score-text {
  font-size: 14px;
  color: #f0f6fc;
  font-weight: 600;
}

.compliance-issues h5 {
  margin: 0 0 8px 0;
  color: #f0f6fc;
  font-size: 14px;
}

.compliance-issues ul {
  margin: 0;
  padding-left: 20px;
  color: #7d8590;
}

/* Recommendations */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
}

.recommendation-card.critical {
  border-left: 4px solid #da3633;
}

.recommendation-card.high {
  border-left: 4px solid #f78166;
}

.recommendation-card.medium {
  border-left: 4px solid #ffa657;
}

.recommendation-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.recommendation-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recommendation-category {
  font-size: 12px;
  color: #7d8590;
  text-transform: uppercase;
}

.recommendation-title h4 {
  margin: 0;
  color: #f0f6fc;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.critical {
  background-color: #da3633;
  color: white;
}

.priority-badge.high {
  background-color: #f78166;
  color: white;
}

.priority-badge.medium {
  background-color: #ffa657;
  color: white;
}

.recommendation-description {
  color: #7d8590;
  margin-bottom: 16px;
  line-height: 1.5;
}

.recommendation-metrics {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
}

.recommendation-metrics .metric {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.recommendation-metrics .metric-label {
  font-size: 12px;
  color: #7d8590;
}

.recommendation-metrics .metric-value {
  font-size: 14px;
  color: #f0f6fc;
  font-weight: 600;
}

.recommendation-actions {
  display: flex;
  gap: 8px;
}

.action-btn.critical {
  background-color: #da3633;
  color: white;
}

.action-btn.critical:hover {
  background-color: #f85149;
}

/* Project Health Expanded Styles */
.project-health-expanded {
  color: #f0f6fc;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.health-score-large {
  display: flex;
  align-items: center;
  gap: 32px;
}

.score-circle-large {
  position: relative;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-text-large {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.score-number-large {
  font-size: 32px;
  font-weight: 700;
  color: #238636;
  line-height: 1;
}

.score-label-large {
  font-size: 12px;
  color: #7d8590;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 2px;
}

.health-summary h2 {
  font-size: 24px;
  color: #f0f6fc;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.health-description {
  font-size: 14px;
  color: #7d8590;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.milestone-progress {
  margin-top: 16px;
}

.milestone-bar {
  width: 100%;
  height: 8px;
  background-color: #21262d;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  margin-bottom: 8px;
}

.milestone-fill {
  height: 100%;
  background: linear-gradient(90deg, #238636 0%, #2ea043 100%);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.milestone-markers {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.milestone-marker {
  position: absolute;
  top: -4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateX(-50%);
}

.milestone-marker.current {
  background-color: #238636;
  border: 2px solid #0d1117;
}

.milestone-marker.next {
  background-color: #21262d;
  border: 2px solid #58a6ff;
}

.milestone-marker span {
  position: absolute;
  top: 20px;
  font-size: 10px;
  color: #7d8590;
  white-space: nowrap;
}

.milestone-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #7d8590;
  margin-top: 8px;
}

/* Categories Grid */
.categories-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.category-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.category-card:hover {
  border-color: #656d76;
  background-color: #30363d;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-header h4 {
  font-size: 14px;
  color: #f0f6fc;
  margin: 0;
  font-weight: 600;
}

.category-score {
  font-size: 16px;
  font-weight: 700;
  padding: 2px 6px;
  border-radius: 4px;
}

.category-score.good {
  color: #238636;
  background-color: rgba(35, 134, 54, 0.1);
}

.category-score.warning {
  color: #ffa657;
  background-color: rgba(255, 166, 87, 0.1);
}

.category-score.critical {
  color: #da3633;
  background-color: rgba(218, 54, 51, 0.1);
}

.category-bar {
  width: 100%;
  height: 4px;
  background-color: #161b22;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 8px;
}

.category-fill {
  height: 100%;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.category-fill.good {
  background-color: #238636;
}

.category-fill.warning {
  background-color: #ffa657;
}

.category-fill.critical {
  background-color: #da3633;
}

.category-status {
  font-size: 12px;
  color: #7d8590;
}

/* Trend Analysis */
.trend-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.timeframe-selector {
  display: flex;
  gap: 4px;
  background-color: #21262d;
  border-radius: 8px;
  padding: 4px;
}

.timeframe-btn {
  background: none;
  border: none;
  color: #7d8590;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.timeframe-btn:hover {
  color: #f0f6fc;
  background-color: #30363d;
}

.timeframe-btn.active {
  color: #f0f6fc;
  background-color: #238636;
}

.trend-chart {
  background-color: #161b22;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #21262d;
  margin-bottom: 20px;
}

/* Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.achievement-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.achievement-card.unlocked {
  border-color: #238636;
  background-color: rgba(35, 134, 54, 0.05);
}

.achievement-card.unlocked::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #238636;
}

.achievement-card.locked {
  opacity: 0.6;
}

.achievement-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.achievement-info h4 {
  font-size: 14px;
  color: #f0f6fc;
  margin: 0 0 4px 0;
  font-weight: 600;
}

.achievement-info p {
  font-size: 12px;
  color: #7d8590;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.achievement-unlocked {
  font-size: 11px;
  color: #238636;
  font-weight: 500;
}

.achievement-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #7d8590;
}

.achievement-bar {
  flex: 1;
  height: 4px;
  background-color: #161b22;
  border-radius: 2px;
  overflow: hidden;
}

.achievement-fill {
  height: 100%;
  background-color: #58a6ff;
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Journey Timeline */
.journey-timeline {
  background-color: #161b22;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #21262d;
  position: relative;
}

.journey-timeline::before {
  content: "";
  position: absolute;
  left: 32px;
  top: 24px;
  bottom: 24px;
  width: 2px;
  background-color: #30363d;
}

.journey-step {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
}

.journey-step:last-child {
  margin-bottom: 0;
}

.journey-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #58a6ff;
  flex-shrink: 0;
  margin-top: 4px;
  position: relative;
  z-index: 1;
}

.journey-content {
  flex: 1;
  background-color: #21262d;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #30363d;
}

.journey-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.journey-action {
  font-size: 13px;
  color: #f0f6fc;
  font-weight: 500;
}

.journey-impact {
  font-size: 12px;
  color: #238636;
  font-weight: 600;
  background-color: rgba(35, 134, 54, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.journey-details {
  display: flex;
  gap: 12px;
  margin-bottom: 4px;
}

.journey-category {
  font-size: 11px;
  color: #58a6ff;
  background-color: rgba(88, 166, 255, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.journey-time {
  font-size: 11px;
  color: #7d8590;
}

.journey-readiness {
  font-size: 11px;
  color: #7d8590;
}

/* Next Steps */
.next-steps {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.next-step-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  position: relative;
}

.next-step-card.high::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #f78166;
  border-radius: 8px 8px 0 0;
}

.next-step-card.medium::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #ffa657;
  border-radius: 8px 8px 0 0;
}

.next-step-card.low::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #7d8590;
  border-radius: 8px 8px 0 0;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.step-header h4 {
  font-size: 14px;
  color: #f0f6fc;
  margin: 0;
  font-weight: 600;
}

.step-impact {
  font-size: 12px;
  color: #238636;
  font-weight: 600;
  background-color: rgba(35, 134, 54, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.step-description {
  font-size: 12px;
  color: #7d8590;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.step-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.step-time {
  font-size: 11px;
  color: #7d8590;
}

.step-priority {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.step-priority.high {
  color: #f78166;
  background-color: rgba(247, 129, 102, 0.1);
}

.step-priority.medium {
  color: #ffa657;
  background-color: rgba(255, 166, 87, 0.1);
}

.step-priority.low {
  color: #7d8590;
  background-color: rgba(125, 133, 144, 0.1);
}

.step-action {
  width: 100%;
  background-color: #238636;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.step-action:hover {
  background-color: #2ea043;
  transform: translateY(-1px);
}

/* Project Memory Widget Styles */
.project-memory {
  color: #f0f6fc;
}

.memory-health {
  margin-bottom: 16px;
}

.memory-health-bar {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.memory-health-label {
  font-size: 12px;
  color: #f0f6fc;
  font-weight: 500;
}

.memory-health-stats {
  font-size: 11px;
  color: #7d8590;
  text-align: right;
}

.active-context {
  margin-bottom: 16px;
}

.context-header {
  margin-bottom: 10px;
}

.context-label {
  font-size: 12px;
  color: #f0f6fc;
  font-weight: 500;
}

.context-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.context-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

.context-icon {
  font-size: 12px;
  width: 16px;
  display: flex;
  justify-content: center;
}

.context-text {
  font-size: 11px;
  color: #7d8590;
}

.current-focus {
  margin-bottom: 16px;
}

.focus-header {
  margin-bottom: 8px;
}

.focus-label {
  font-size: 12px;
  color: #f0f6fc;
  font-weight: 500;
}

.focus-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.focus-task {
  font-size: 11px;
  color: #58a6ff;
  font-style: italic;
}

.focus-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.focus-progress-text {
  font-size: 10px;
  color: #238636;
}

.focus-blockers {
  font-size: 10px;
  color: #f78166;
}

/* Project Memory Expanded Styles */
.project-memory-expanded {
  color: #f0f6fc;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.memory-overview {
  display: flex;
  gap: 32px;
  align-items: flex-start;
}

.memory-score-large {
  display: flex;
  align-items: center;
  gap: 20px;
}

.memory-summary h2 {
  font-size: 24px;
  color: #f0f6fc;
  margin: 0 0 8px 0;
  font-weight: 600;
}

.memory-description {
  font-size: 14px;
  color: #7d8590;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.memory-metrics {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  font-size: 12px;
  color: #f0f6fc;
  min-width: 80px;
}

.metric-bar {
  flex: 1;
  height: 6px;
  background-color: #21262d;
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background-color: #8869ba;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.metric-value {
  font-size: 12px;
  color: #7d8590;
  font-family: monospace;
}

/* Memory Layers Grid */
.memory-layers-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 16px;
}

.memory-layer-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.memory-layer-card:hover {
  border-color: #656d76;
  background-color: #30363d;
}

.memory-layer-card.personal {
  border-left: 4px solid #58a6ff;
}

.memory-layer-card.business {
  border-left: 4px solid #8869ba;
}

.memory-layer-card.technical {
  border-left: 4px solid #238636;
}

.layer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.layer-icon {
  font-size: 16px;
  margin-right: 8px;
}

.layer-header h4 {
  font-size: 14px;
  color: #f0f6fc;
  margin: 0;
  font-weight: 600;
  flex: 1;
}

.layer-tokens {
  font-size: 11px;
  color: #7d8590;
  background-color: #161b22;
  padding: 2px 6px;
  border-radius: 4px;
}

.layer-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.layer-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.item-label {
  font-size: 11px;
  color: #7d8590;
  font-weight: 500;
}

.item-value {
  font-size: 12px;
  color: #f0f6fc;
}

/* Context Layer Detail */
.context-layer-detail {
  margin-top: 16px;
}

.context-section {
  margin-bottom: 24px;
}

.context-section h4 {
  font-size: 16px;
  color: #f0f6fc;
  margin-bottom: 12px;
  font-weight: 600;
}

.task-context-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
}

.task-objective {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.task-label {
  font-size: 12px;
  color: #7d8590;
  font-weight: 600;
  min-width: 80px;
}

.task-value {
  font-size: 14px;
  color: #58a6ff;
  font-weight: 500;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.task-progress-bar {
  flex: 1;
  height: 6px;
  background-color: #161b22;
  border-radius: 3px;
  overflow: hidden;
}

.task-progress-fill {
  height: 100%;
  background-color: #238636;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.task-progress-text {
  font-size: 12px;
  color: #238636;
  font-weight: 600;
}

.task-blockers,
.task-changes {
  margin-bottom: 16px;
}

.blockers-list,
.changes-list {
  margin: 8px 0 0 0;
  padding-left: 16px;
  color: #7d8590;
}

.blocker-item,
.change-item {
  font-size: 12px;
  margin-bottom: 4px;
  line-height: 1.4;
}

.blocker-item {
  color: #f78166;
}

.change-item {
  color: #238636;
}

/* Learning Progress */
.learning-metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 16px;
  margin-bottom: 24px;
}

.learning-metric-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
}

.learning-metric-card:hover {
  border-color: #656d76;
  background-color: #30363d;
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-icon {
  font-size: 16px;
}

.metric-header h4 {
  font-size: 14px;
  color: #f0f6fc;
  margin: 0;
  font-weight: 600;
}

.metric-value-large {
  font-size: 32px;
  font-weight: 700;
  color: #8869ba;
  margin-bottom: 8px;
}

.metric-description {
  font-size: 12px;
  color: #7d8590;
}

/* Recent Updates Timeline */
.recent-updates {
  margin-top: 24px;
}

.recent-updates h4 {
  font-size: 16px;
  color: #f0f6fc;
  margin-bottom: 16px;
  font-weight: 600;
}

.updates-timeline {
  position: relative;
  padding-left: 24px;
}

.updates-timeline::before {
  content: "";
  position: absolute;
  left: 8px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #30363d;
}

.update-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
  position: relative;
}

.update-item:last-child {
  margin-bottom: 0;
}

.update-marker {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #8869ba;
  flex-shrink: 0;
  margin-top: 4px;
  position: absolute;
  left: -20px;
  z-index: 1;
}

.update-content {
  flex: 1;
  background-color: #21262d;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #30363d;
}

.update-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.update-type {
  font-size: 12px;
  color: #8869ba;
  font-weight: 600;
  background-color: rgba(136, 105, 186, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.update-time {
  font-size: 11px;
  color: #7d8590;
}

.update-description {
  font-size: 13px;
  color: #f0f6fc;
  margin-bottom: 6px;
}

.update-impact {
  font-size: 12px;
  color: #238636;
  font-style: italic;
}

/* Health Metrics */
.health-metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 16px;
}

.health-metric-card {
  background-color: #21262d;
  border: 1px solid #30363d;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
}

.health-metric-card:hover {
  border-color: #656d76;
  background-color: #30363d;
}

.metric-circle {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.metric-circle-text {
  position: absolute;
  font-size: 14px;
  font-weight: 600;
  color: #238636;
}