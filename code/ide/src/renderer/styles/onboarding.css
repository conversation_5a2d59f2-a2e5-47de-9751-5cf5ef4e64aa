/* Onboarding Styles - Matching Welcome Screen Aesthetic */

/* <PERSON><PERSON><PERSON><PERSON> Styles */
.brutal-honesty-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.honesty-message {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.honesty-message:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.honesty-message .brutal-text {
  font-style: italic;
  color: #ff6b6b;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.honesty-message .helpful-text {
  color: var(--text-body);
  font-size: 0.9rem;
  line-height: 1.4;
}

.encouragement {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.encouragement h4 {
  margin: 0 0 0.5rem 0;
  color: #22c55e;
}

.fun-facts {
  background: rgba(108, 92, 231, 0.1);
  border: 1px solid rgba(108, 92, 231, 0.2);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 2rem;
}

.fun-facts h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-header);
}

.fun-facts ul {
  margin: 0;
  padding-left: 1.5rem;
}

.fun-facts li {
  margin-bottom: 0.5rem;
  color: var(--text-body);
}

.readiness-score {
  font-size: 3rem;
  font-weight: bold;
  color: #ff6b6b;
  text-align: center;
  margin: 1rem 0;
}

.grade-display {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ff6b6b;
  text-align: center;
}

/* Drag and Drop Styles */
.drag-drop-area {
  border: 2px dashed var(--border-color);
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  background: var(--background-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
}

.drag-drop-area:hover {
  border-color: var(--accent-primary);
  background: rgba(108, 92, 231, 0.05);
}

.drag-drop-area.drag-active {
  border-color: var(--accent-primary);
  background: rgba(108, 92, 231, 0.1);
  transform: scale(1.02);
}

.drop-zone-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

.drop-zone-text {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--text-header);
}

.drop-zone-subtitle {
  color: var(--text-body);
  margin-bottom: 1rem;
}

.warning-message {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-top: 1rem;
  color: #f59e0b;
  font-weight: 500;
}

.warning-message .warning-icon {
  margin-right: 0.5rem;
}

/* Analysis Question Styles */
.analysis-questions {
  background: rgba(108, 92, 231, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(108, 92, 231, 0.2);
}

.analysis-questions h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-header);
}

.question-item {
  margin-bottom: 1rem;
}

.question-item label {
  font-weight: 500;
  color: var(--text-body);
}

.question-item input {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid rgba(108, 92, 231, 0.3);
  background: var(--bg-dashboard);
  color: var(--text-body);
  font-size: 14px;
}

.question-item input:focus {
  outline: none;
  border-color: rgba(108, 92, 231, 0.6);
  box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.1);
}

/* Analysis Progress Styles */
.analysis-stats {
  display: flex;
  gap: 2rem;
  justify-content: center;
  margin-top: 1rem;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--text-header);
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-body);
  margin-top: 0.25rem;
}

.current-file {
  font-size: 0.875rem;
  color: var(--text-body);
  margin-top: 0.5rem;
  font-style: italic;
}

/* Conversation placeholder */
.conversation-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  padding: 2rem;
}

/* Progress bar styles */
.progress-bar-container {
  width: 100%;
  height: 8px;
  background-color: rgba(108, 92, 231, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #6c5ce7, #a29bfe);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  margin-top: 1rem;
  font-size: 0.9rem;
  color: var(--text-body);
  text-align: center;
}
/* Onboarding inherits dashboard styling when enhanced-dashboard class is applied */
.onboarding-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  position: relative;
}

/* Use dashboard colors when enhanced-dashboard class is present */
.enhanced-dashboard.onboarding-container {
  background-color: #0d1117;
  color: #f0f6fc;
}

/* Dashboard header subtitle styling */
.enhanced-dashboard .dashboard-subtitle {
  font-size: 14px;
  font-weight: 400;
  color: #7d8590;
  margin-left: 12px;
}

/* Progress section styling to match dashboard */
.enhanced-dashboard .progress-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.enhanced-dashboard .progress-bar {
  width: 200px;
  height: 8px;
  background-color: #21262d;
  border-radius: 4px;
  overflow: hidden;
}

.enhanced-dashboard .progress-fill {
  height: 100%;
  background-color: #ffa657;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.enhanced-dashboard .stage-label {
  font-size: 11px;
  color: #7d8590;
  white-space: nowrap;
}

/* Legacy header styles for backwards compatibility */
.onboarding-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--bg-dashboard);
  border-bottom: 1px solid rgba(108, 92, 231, 0.2);
  min-height: 60px;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.kapi-logo {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.header-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-header);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.skip-button-header {
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: 1px solid rgba(108, 92, 231, 0.5);
  color: #8A7FA8;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.skip-button-header:hover {
  background-color: rgba(108, 92, 231, 0.1);
  border-color: var(--accent-purple);
  color: var(--accent-purple);
}

/* Main Content Layout */
.onboarding-content {
  flex: 1;
  display: grid;
  grid-template-columns: 200px 1fr 250px;
  gap: 0;
  overflow: hidden;
  min-height: 0;
}

/* Left Sidebar */
.left-sidebar {
  background-color: rgba(108, 92, 231, 0.05);
  border-right: 1px solid rgba(108, 92, 231, 0.1);
  padding: 1rem;
  overflow-y: scroll;
  overflow-x: hidden;
  height: calc(100vh - 60px);
  max-height: calc(100vh - 60px);
  min-height: 0;
  scrollbar-width: auto;
  scrollbar-color: rgba(108, 92, 231, 0.8) rgba(108, 92, 231, 0.3);
}

/* Dashboard-style sidebar when enhanced-dashboard class is present */
.enhanced-dashboard .left-sidebar {
  background-color: #0d1117;
  border-right: 1px solid #21262d;
  scrollbar-color: #30363d #21262d;
}

.enhanced-dashboard .right-sidebar {
  background-color: #0d1117;
  border-left: 1px solid #21262d;
  scrollbar-color: #30363d #21262d;
}

.sidebar-content {
  padding-bottom: 2rem; /* Add bottom padding for scrolling space */
}

.sidebar-content h3 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-header);
  margin: 0 0 0.5rem 0;
}

.sidebar-note {
  font-size: 0.8rem;
  color: #8A7FA8;
  text-align: center;
  margin-top: 2rem;
  font-style: italic;
}

.profile-card, .personalization-card, .building-profile-card, .next-questions-card, .memory-card, .discovery-card, .learning-card {
  background-color: rgba(108, 92, 231, 0.1);
  border: 1px solid rgba(108, 92, 231, 0.2);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.8rem;
}

/* Dashboard-style cards when enhanced-dashboard class is present */
.enhanced-dashboard .profile-card,
.enhanced-dashboard .personalization-card,
.enhanced-dashboard .building-profile-card,
.enhanced-dashboard .next-questions-card,
.enhanced-dashboard .memory-card,
.enhanced-dashboard .discovery-card,
.enhanced-dashboard .learning-card {
  background-color: #161b22;
  border: 1px solid #21262d;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.8rem;
}

.profile-item {
  margin-bottom: 0.4rem;
  font-size: 0.75rem;
  line-height: 1.3;
  color: #CCCCCC;
}

.profile-item strong {
  font-weight: 600;
  color: var(--text-header);
}

.profile-item.analyzing {
  color: var(--accent-orange);
  font-style: italic;
}

.pref-item {
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
}

.pref-item.enabled {
  color: var(--accent-purple);
}

.pref-item.pending {
  color: #8A7FA8;
}

.question-item {
  margin-bottom: 0.25rem;
  font-size: 0.75rem;
  padding-left: 0.5rem;
}

.question-item.completed {
  color: var(--accent-purple);
}

.question-item.current {
  color: var(--text-header);
  font-weight: 600;
}

.question-item.pending {
  color: #8A7FA8;
}

/* AI-generated questions note */
.ai-generated-note {
  margin-top: 10px;
  padding: 8px;
  background: rgba(0, 196, 180, 0.1);
  border-radius: 4px;
  text-align: center;
}

.ai-generated-note small {
  color: #00c4b4;
  font-style: italic;
}

/* Right Sidebar */
.right-sidebar {
  background-color: rgba(108, 92, 231, 0.05);
  border-left: 1px solid rgba(108, 92, 231, 0.1);
  padding: 1rem;
  overflow-y: scroll;
  overflow-x: hidden;
  height: calc(100vh - 60px);
  max-height: calc(100vh - 60px);
  min-height: 0;
  scrollbar-width: auto;
  scrollbar-color: rgba(108, 92, 231, 0.8) rgba(108, 92, 231, 0.3);
}

/* Webkit scrollbar styling for sidebars - More visible */
.left-sidebar::-webkit-scrollbar,
.right-sidebar::-webkit-scrollbar {
  width: 12px;
}

.left-sidebar::-webkit-scrollbar-track,
.right-sidebar::-webkit-scrollbar-track {
  background: rgba(108, 92, 231, 0.2);
  border-radius: 6px;
}

.left-sidebar::-webkit-scrollbar-thumb,
.right-sidebar::-webkit-scrollbar-thumb {
  background: rgba(108, 92, 231, 0.7);
  border-radius: 6px;
  border: 2px solid rgba(108, 92, 231, 0.2);
}

.left-sidebar::-webkit-scrollbar-thumb:hover,
.right-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(108, 92, 231, 0.9);
}

/* Main Content Area - Centered like Welcome Screen with Scrollbar */
.main-content {
  background-color: var(--bg-dashboard);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  overflow-y: auto;
  text-align: center;
  height: calc(100vh - 60px - 120px); /* Account for header and fixed input */
  scrollbar-width: thin;
  scrollbar-color: rgba(108, 92, 231, 0.8) rgba(108, 92, 231, 0.2);
  padding-bottom: 2rem;
  min-height: 400px;
}

/* Dashboard-style main content when enhanced-dashboard class is present */
.enhanced-dashboard .main-content {
  background-color: #0d1117;
}

/* Main Content Area for stage 2+ (with input section) */
.onboarding-container:not(.stage-welcome) .main-content {
  height: calc(100vh - 60px - 140px); /* Account for header and larger input section */
  max-height: calc(100vh - 60px - 140px);
  overflow-y: auto;
}

/* Main Content Area when no input section (stage 1) */
.onboarding-container.stage-welcome .main-content {
  height: calc(100vh - 60px); /* Without input section - full height */
  justify-content: flex-start; /* Allow scrolling from top */
  padding-top: 2rem; /* Add some top padding */
}

/* Welcome Area - Matching Welcome Screen */
.welcome-area {
  margin-bottom: 2rem;
  max-width: 650px;
  width: 100%;
}

.avatar-circle {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background-color: rgba(108, 92, 231, 0.1);
  border: 2px solid var(--accent-purple);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
}

/* Dashboard-style avatar circle when enhanced-dashboard class is present */
.enhanced-dashboard .avatar-circle {
  background-color: #21262d;
  border: 2px solid #30363d;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.avatar-emoji {
  font-size: 2.5rem;
}

.welcome-message h2 {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--text-header);
  margin: 0 0 1rem 0;
}

.welcome-message p {
  font-size: 1rem;
  color: #CCCCCC;
  margin: 0;
  line-height: 1.6;
  white-space: pre-line;
}

/* Voice input is now integrated into the text input - old styles removed */

/* Mode Selection - Horizontal Layout like Welcome Screen */
.mode-selection {
  display: flex;
  gap: 1.5rem;
  margin: 2rem 0;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  max-width: 650px;
  width: 100%;
}

.mode-option-button {
  padding: 1rem 2rem;
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 8px;
  background-color: rgba(108, 92, 231, 0.1);
  color: var(--text-header);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  text-align: center;
}

.mode-option-button:hover {
  background-color: rgba(108, 92, 231, 0.2);
  border-color: rgba(108, 92, 231, 0.5);
  transform: translateY(-2px);
}

.mode-option-button:active {
  transform: translateY(0);
}

/* Role Selection - Grid Layout for User Roles */
.role-selection {
  width: 100%;
  max-width: 650px;
  margin: 2rem 0;
}

/* Dashboard-style role selection buttons when enhanced-dashboard class is present */
.enhanced-dashboard .role-option-button {
  background-color: #21262d;
  border: 1px solid #30363d;
  color: #f0f6fc;
}

.enhanced-dashboard .role-option-button:hover {
  background-color: #30363d;
  border-color: #656d76;
  transform: translateY(-2px);
}

.enhanced-dashboard .folder-select-button,
.enhanced-dashboard .folder-manual-button {
  background-color: #21262d;
  border: 1px solid #30363d;
  color: #f0f6fc;
}

.enhanced-dashboard .folder-select-button:hover,
.enhanced-dashboard .folder-manual-button:hover {
  background-color: #30363d;
  border-color: #656d76;
}

.role-selection h3 {
  text-align: center;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  color: var(--text-header);
}

.role-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.role-option-button {
  padding: 1rem;
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 8px;
  background-color: rgba(108, 92, 231, 0.05);
  color: var(--text-header);
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 80px;
  justify-content: center;
}

.role-option-button:hover {
  background-color: rgba(108, 92, 231, 0.15);
  border-color: rgba(108, 92, 231, 0.5);
  transform: translateY(-2px);
}

.role-option-button:active {
  transform: translateY(0);
}

.role-option-button.selected {
  background-color: rgba(108, 92, 231, 0.2);
  border-color: rgba(108, 92, 231, 0.7);
  border-width: 2px;
}

.role-option-button.selected .role-icon::after {
  content: ' ✅';
  font-size: 0.8rem;
}

.role-icon {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.role-description {
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.2;
}

/* Welcome Collection Styling */
.welcome-collection {
  width: 100%;
  max-width: 650px;
}

.name-collection {
  text-align: center;
  margin-bottom: 2rem;
}

.name-collection h3 {
  color: var(--text-header);
  margin-bottom: 0.5rem;
}

.instruction-text {
  color: rgba(var(--text-header-rgb), 0.7);
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.stage-progress-hint {
  text-align: center;
  margin-top: 1rem;
  padding: 1rem;
  background-color: rgba(108, 92, 231, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(108, 92, 231, 0.2);
}

.stage-progress-hint p {
  margin: 0;
  color: rgba(108, 92, 231, 0.8);
  font-size: 0.9rem;
}

/* Additional styling for user-focused elements */
.memory-card, .discovery-card, .learning-card {
  background-color: rgba(108, 92, 231, 0.05);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.memory-item, .learning-item {
  padding: 0.25rem 0;
  font-size: 0.9rem;
}

.learning-item.completed {
  color: rgba(108, 92, 231, 0.8);
  font-weight: 500;
}

.discovery-stage {
  font-size: 0.9rem;
}

.completion-section {
  margin-top: 2rem;
  text-align: center;
}

.early-completion-section {
  margin-top: 1.5rem;
  text-align: center;
  padding: 1rem;
  background-color: rgba(108, 92, 231, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(108, 92, 231, 0.2);
}

.complete-onboarding-button {
  background: linear-gradient(135deg, rgba(108, 92, 231, 0.8), rgba(108, 92, 231, 1));
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.complete-onboarding-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 92, 231, 0.3);
}

.completion-note {
  margin-top: 0.5rem;
  opacity: 0.7;
}

.early-complete-button {
  background: rgba(108, 92, 231, 0.15);
  color: rgba(108, 92, 231, 0.9);
  border: 1px solid rgba(108, 92, 231, 0.3);
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.early-complete-button:hover {
  background: rgba(108, 92, 231, 0.25);
  border-color: rgba(108, 92, 231, 0.5);
  transform: translateY(-1px);
}

/* Quick option buttons inherit all styles from global .button.primaryButton */

/* Conversation History - Simplified without separate scrolling */
.conversation-history {
  width: 100%;
  max-width: 650px;
  margin: 1.5rem 0;
}

.message {
  margin-bottom: 1rem;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  font-size: 1rem;
  line-height: 1.5;
}

.ai-message {
  background: none;
  border: none;
  color: #CCCCCC;
  text-align: left;
  padding: 1rem 0;
  max-width: 650px;
  width: 100%;
}

.user-message {
  background-color: #2a2a3e;
  border: 1px solid #3a3a4e;
  color: #FFFFFF;
  text-align: left;
  margin: 0;
  border-radius: 8px;
  position: relative;
}

.message-mode-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.8rem;
  opacity: 0.7;
}

.message.streaming {
  background-color: rgba(108, 92, 231, 0.1);
  border-left: 3px solid var(--accent-purple);
}

/* Markdown formatting for AI messages */
.markdown-message {
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

.markdown-message p {
  margin: 0 0 0.5em 0;
}

.markdown-message p:last-child {
  margin-bottom: 0;
}

.markdown-message strong {
  font-weight: 600;
  color: var(--text-header);
}

.markdown-message em {
  font-style: italic;
  color: #e6e6e6;
}

.markdown-message code,
.markdown-message .inline-code {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  background-color: rgba(108, 92, 231, 0.15);
  color: var(--accent-purple);
  padding: 0.1em 0.3em;
  border-radius: 3px;
  font-size: 0.9em;
}

.markdown-message ul,
.markdown-message ol {
  margin: 0.5em 0;
  padding-left: 1.2em;
}

.markdown-message li {
  margin-bottom: 0.25em;
}

.markdown-message h1,
.markdown-message h2,
.markdown-message h3,
.markdown-message h4,
.markdown-message h5,
.markdown-message h6 {
  margin: 0.8em 0 0.3em 0;
  font-weight: 600;
  color: var(--text-header);
}

.markdown-message h1 { font-size: 1.2em; }
.markdown-message h2 { font-size: 1.1em; }
.markdown-message h3 { font-size: 1.05em; }
.markdown-message h4,
.markdown-message h5,
.markdown-message h6 { font-size: 1em; }

.message.processing {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
  background-color: rgba(108, 92, 231, 0.05);
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: var(--accent-purple);
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: 0s; }
.typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
.typing-indicator span:nth-child(3) { animation-delay: 0.4s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
  40% { transform: scale(1.2); opacity: 1; }
}

/* Text Input Section - Fixed Bottom */
.text-input-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  margin: 0;
  padding: 1.5rem 2rem;
  background-color: var(--bg-dashboard);
  border-top: 1px solid rgba(108, 92, 231, 0.2);
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
  min-height: 120px;
}

/* Dashboard-style text input section when enhanced-dashboard class is present */
.enhanced-dashboard .text-input-section {
  background-color: #21262d;
  border-top: 1px solid #30363d;
}

.text-input {
  width: 100%;
  padding: 1rem 5rem 1rem 1.5rem;
  border: 2px solid rgba(108, 92, 231, 0.3);
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  background-color: rgba(108, 92, 231, 0.05);
  color: var(--text-header);
  resize: none;
  min-height: 50px;
  max-height: 150px;
  transition: border-color 0.2s ease;
  position: relative;
}

/* Dashboard-style text input when enhanced-dashboard class is present */
.enhanced-dashboard .text-input {
  border: 1px solid #30363d;
  background-color: #0d1117;
  color: #f0f6fc;
}

.enhanced-dashboard .text-input:focus {
  border-color: #58a6ff;
  background-color: #0d1117;
}

.enhanced-dashboard .text-input::placeholder {
  color: #7d8590;
}

.text-input:focus {
  outline: none;
  border-color: var(--accent-purple);
  background-color: rgba(108, 92, 231, 0.1);
}

.text-input:disabled {
  background-color: rgba(108, 92, 231, 0.02);
  color: #8A7FA8;
  cursor: not-allowed;
}

.text-input::placeholder {
  color: #8A7FA8;
}

.input-container {
  position: relative;
  width: 100%;
  max-width: 650px;
  margin: 0 auto;
}

.input-actions {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.input-hint {
  font-size: 0.9rem;
  color: #8A7FA8;
  margin-top: 0.5rem;
  text-align: center;
}

/* Voice Button - Inline Style */
.voice-button-inline {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: transparent;
  border: 1px solid rgba(108, 92, 231, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--accent-purple);
  font-size: 14px;
}

.voice-button-inline:hover {
  background-color: rgba(108, 92, 231, 0.1);
  border-color: var(--accent-purple);
}

.voice-button-inline.listening {
  background-color: var(--accent-purple);
  color: white;
  animation: pulse 2s infinite;
}

.voice-button-inline.error {
  background-color: rgba(231, 76, 60, 0.1);
  border-color: #e74c3c;
  color: #e74c3c;
}

.voice-button-inline.error:hover {
  background-color: rgba(231, 76, 60, 0.2);
}

/* Send Button - Inline Style */
.send-button {
  padding: 0.4rem 0.8rem;
  background-color: var(--accent-purple);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: auto;
  height: 32px;
}

.send-button:hover:not(:disabled) {
  background-color: #8A7CE9;
}

.send-button:disabled {
  background-color: rgba(108, 92, 231, 0.3);
  color: #8A7FA8;
  cursor: not-allowed;
}

/* Skip Section - Removed from bottom */
.skip-section {
  display: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .onboarding-content {
    grid-template-columns: 100px 1fr 100px;
  }
  
  .left-sidebar, .right-sidebar {
    font-size: 0.8rem;
    padding: 0.75rem;
  }
}

@media (max-width: 768px) {
  .onboarding-header {
    padding: 1rem;
  }
  
  .header-title {
    font-size: 1rem;
  }
  
  .progress-bar {
    width: 120px;
  }
  
  .onboarding-content {
    grid-template-columns: 1fr;
  }
  
  .left-sidebar, .right-sidebar {
    display: none;
  }
  
  .main-content {
    padding: 1.5rem 1rem;
  }
  
  .welcome-message h2 {
    font-size: 2rem;
  }
  
  .voice-input-area {
    max-width: 100%;
    padding: 1rem;
  }
  
  .quick-options {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
  
  .conversation-history {
    max-width: 100%;
  }
  
  .text-input-section {
    padding: 1rem;
  }
  
  .input-container {
    max-width: 100%;
  }
}

/* Focus styles for accessibility */
.quick-option-button:focus,
.send-button:focus,
.skip-button-header:focus,
.voice-button-inline:focus,
.button.primaryButton:focus,
.secondaryButton:focus {
  outline: 2px solid var(--accent-purple);
  outline-offset: 2px;
}

.text-input:focus {
  outline: none;
  border-color: var(--accent-purple);
}

/* Folder Selection Styles */
.folder-selection-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  max-width: 650px;
  width: 100%;
  margin: 2rem 0;
}

.folder-info {
  text-align: center;
  margin-bottom: 1rem;
}

.folder-info p {
  font-size: 1rem;
  color: #CCCCCC;
  line-height: 1.6;
  margin: 0;
}

.folder-select-button {
  padding: 1.5rem 3rem;
  background: linear-gradient(135deg, rgba(108, 92, 231, 0.8), rgba(108, 92, 231, 1));
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.folder-select-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 92, 231, 0.4);
  background: linear-gradient(135deg, rgba(108, 92, 231, 0.9), rgba(108, 92, 231, 1));
}

.folder-select-button:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px rgba(108, 92, 231, 0.3);
}

.folder-hint {
  text-align: center;
}

.folder-hint small {
  color: #8A7FA8;
  font-size: 0.9rem;
  font-style: italic;
}

/* Custom scrollbars - More Visible */
.left-sidebar::-webkit-scrollbar,
.right-sidebar::-webkit-scrollbar,
.main-content::-webkit-scrollbar {
  width: 10px;
}

.left-sidebar::-webkit-scrollbar-track,
.right-sidebar::-webkit-scrollbar-track,
.main-content::-webkit-scrollbar-track {
  background: rgba(108, 92, 231, 0.15);
  border-radius: 5px;
}

.left-sidebar::-webkit-scrollbar-thumb,
.right-sidebar::-webkit-scrollbar-thumb,
.main-content::-webkit-scrollbar-thumb {
  background-color: rgba(108, 92, 231, 0.7);
  border-radius: 5px;
  border: 1px solid rgba(108, 92, 231, 0.9);
}

.left-sidebar::-webkit-scrollbar-thumb:hover,
.right-sidebar::-webkit-scrollbar-thumb:hover,
.main-content::-webkit-scrollbar-thumb:hover {
  background-color: rgba(108, 92, 231, 0.9);
}

/* Folder Selection Styles */
.folder-selection-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.folder-select-button,
.folder-manual-button {
  padding: 1rem 2rem;
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 8px;
  background-color: rgba(108, 92, 231, 0.1);
  color: var(--text-header);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
  text-align: center;
}

.folder-select-button:hover,
.folder-manual-button:hover {
  background-color: rgba(108, 92, 231, 0.2);
  border-color: rgba(108, 92, 231, 0.5);
  transform: translateY(-2px);
}

.folder-selection-divider {
  color: #8A7FA8;
  font-size: 0.9rem;
  margin: 0.5rem 0;
}

/* Manual Folder Input Modal */
.manual-folder-input {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 12px;
  background-color: rgba(108, 92, 231, 0.05);
  width: 100%;
  max-width: 400px;
}

.manual-folder-input h4 {
  margin: 0 0 1rem 0;
  color: var(--text-header);
  font-size: 1.1rem;
  text-align: center;
}

.folder-path-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 6px;
  background-color: rgba(26, 27, 38, 0.8);
  color: var(--text-primary);
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
  margin-bottom: 1rem;
}

.folder-path-input:focus {
  outline: none;
  border-color: rgba(108, 92, 231, 0.6);
  box-shadow: 0 0 0 2px rgba(108, 92, 231, 0.2);
}

.folder-input-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.folder-input-cancel,
.folder-input-submit {
  padding: 0.5rem 1rem;
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.folder-input-cancel {
  background-color: transparent;
  color: #8A7FA8;
}

.folder-input-cancel:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
}

.folder-input-submit {
  background-color: rgba(108, 92, 231, 0.2);
  color: var(--text-header);
}

.folder-input-submit:hover:not(:disabled) {
  background-color: rgba(108, 92, 231, 0.3);
  transform: translateY(-1px);
}

.folder-input-submit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Discovery Stage Styles */
.discovery-guidance {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 0;
}

.discovery-info {
  background: var(--background-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
}

.discovery-info h3 {
  margin: 0 0 1rem 0;
  color: var(--text-header);
  font-size: 1.5rem;
}

.discovery-info p {
  color: var(--text-body);
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.discovery-prompt {
  margin-top: 2rem;
}

.prompt-card {
  background: rgba(108, 92, 231, 0.1);
  border: 1px solid rgba(108, 92, 231, 0.3);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: left;
}

.prompt-card h4 {
  margin: 0 0 1rem 0;
  color: var(--text-header);
  font-size: 1.1rem;
}

.prompt-card p {
  margin-bottom: 1rem;
  color: var(--text-body);
  font-size: 0.95rem;
}

.prompt-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.prompt-card li {
  padding: 0.5rem 0;
  color: var(--text-body);
  font-size: 0.9rem;
  opacity: 0.9;
}

.prompt-card li:hover {
  opacity: 1;
  color: var(--text-header);
}

/* Enhanced conversation placeholder */
.conversation-placeholder {
  background: var(--background-secondary);
  border: 1px dashed var(--border-color);
  border-radius: 8px;
  margin: 1rem 0;
}

/* Improved conversation layout */
.conversation-history {
  max-height: 400px;
  overflow-y: auto;
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 1rem;
  min-height: 120px;
}

.conversation-history:empty {
  display: flex;
  align-items: center;
  justify-content: center;
}