import React, { useState, useEffect } from 'react';
import '../../../styles/dashboard.css';
import '../../../styles/project-analysis.css';
import { useProject } from '../../../contexts/ProjectContext';
import { projectAnalysisService, ProjectAnalysisResult } from '../../../services/ProjectAnalysisService';
import { brutalHonestyService, BrutalHonestyReport } from '../../../services/BrutalHonestyService';
import SpinningBrain from '../../../components/SpinningBrain';
import analysisCacheService from '../../../services/AnalysisCacheService';

interface ProjectAnalysisProps {
  className?: string;
}

export interface BrutalHonestyMessage {
  category: 'security' | 'performance' | 'quality' | 'documentation' | 'error_handling' | 'complexity';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  brutalmessage: string;
  helpfulGuidance: string;
  grade: 'A' | 'B' | 'C' | 'D' | 'F';
  emoji: string;
  readinessImpact: number;
}

export interface BrutalHonestyData {
  productionReadiness: number;
  overallGrade: string;
  messages: BrutalHonestyMessage[];
  encouragement: string;
  nextSteps: string[];
  timeToFix: string;
  funFacts: string[];
}

export interface ProjectAnalysisData {
  totalFiles?: number;
  linesOfCode?: number;
  testCoverage?: number;
  complexity?: {
    average: number;
    high: string[];
    recommendations: string[];
  };
  dependencies?: {
    total: number;
    outdated: number;
    vulnerable: number;
    list: Array<{name: string; version: string; status: 'current' | 'outdated' | 'vulnerable'}>;
  };
  architecture?: {
    pattern: string;
    components: Array<{name: string; type: string; connections: string[]}>;
    suggestions: string[];
  };
  quality?: {
    documentation: number;
    testCoverage: number;
    codeStyle: number;
    maintainability: number;
  };
  roadmap?: Array<{
    phase: string;
    duration: string;
    tasks: Array<{
      title: string;
      description: string;
      priority: 'high' | 'medium' | 'low';
      effort: string;
    }>;
  }>;
  healthScore?: number;
  brutalHonesty?: BrutalHonestyData;
}

const ProjectAnalysisWidget: React.FC<ProjectAnalysisProps> = ({ className }) => {
  const { projectState, isProjectOpen } = useProject();
  const [analysisData, setAnalysisData] = useState<ProjectAnalysisData | null>(null);
  const [realAnalysisData, setRealAnalysisData] = useState<ProjectAnalysisResult | null>(null);
  const [realBrutalHonesty, setRealBrutalHonesty] = useState<BrutalHonestyReport | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentView, setCurrentView] = useState<'overview' | 'health' | 'architecture' | 'roadmap' | 'brutal-honesty'>('overview');
  const [lastAnalysisTime, setLastAnalysisTime] = useState<string | null>(null);

  useEffect(() => {
    if (isProjectOpen && projectState?.path) {
      loadProjectAnalysis();
    }
  }, [isProjectOpen, projectState?.path]);

  const loadProjectAnalysis = async () => {
    if (!projectState?.path) return;
    
    setIsAnalyzing(true);
    
    try {
      // First try to load from cache
      const cachedResult = await analysisCacheService.getCachedProjectAnalysis(projectState.path);
      
      if (cachedResult) {
        console.log('ProjectAnalysisWidget: Loading cached analysis results');
        setAnalysisData(cachedResult.data);
        setLastAnalysisTime(cachedResult.timestamp);
      }
      
      // Try to load real brutal honesty data (independent of cached results)
      try {
        const brutalHonestyReport = await brutalHonestyService.getOrGenerateAnalysis(projectState.path);
        setRealBrutalHonesty(brutalHonestyReport);
        console.log('ProjectAnalysisWidget: Real brutal honesty data loaded');
        
        // If we have real brutal honesty but no cached analysis, update analysis data
        if (!cachedResult && brutalHonestyReport) {
          const enhancedAnalysis = {
            ...analysisData,
            brutalHonesty: brutalHonestyReport
          };
          setAnalysisData(enhancedAnalysis);
        }
      } catch (brutalError) {
        console.warn('Failed to load real brutal honesty data:', brutalError);
      }
      
      // If no cache, try to run real analysis
      if (!cachedResult) {
        try {
          const result = await projectAnalysisService.analyzeProject(projectState.path);
          setRealAnalysisData(result);
          
          // Convert to widget format
          const widgetData: ProjectAnalysisData = {
            totalFiles: result.structure.totalFiles,
            linesOfCode: result.structure.totalLines,
            testCoverage: result.health.testCoverage,
            healthScore: result.health.overallScore,
            complexity: result.health.complexity,
            quality: {
              documentation: result.health.documentation,
              testCoverage: result.health.testCoverage,
              codeStyle: result.health.maintainability,
              maintainability: result.health.maintainability
            },
            roadmap: result.roadmap.map(phase => ({
              phase: phase.phase,
              duration: phase.duration,
              tasks: phase.tasks
            })),
            brutalHonesty: realBrutalHonesty || undefined
          };
          
          setAnalysisData(widgetData);
          const timestamp = new Date().toISOString();
          setLastAnalysisTime(timestamp);
          
          // Cache the result
          await analysisCacheService.setCachedProjectAnalysis(projectState.path, widgetData);
          console.log('ProjectAnalysisWidget: Analysis completed and cached');
          
        } catch (error) {
          console.error('Real analysis failed, falling back to mock data:', error);
          loadMockAnalysis();
        }
      }
    } catch (cacheError) {
      console.error('Cache error, falling back to mock data:', cacheError);
      loadMockAnalysis();
    } finally {
      setIsAnalyzing(false);
    }
  };

  const loadMockAnalysis = async () => {
    // For now, load mock analysis data
    // In the future, this will call the real analysis API
    const mockData: ProjectAnalysisData = {
      totalFiles: 247,
      linesOfCode: 15420,
      testCoverage: 68,
      healthScore: 78,
      complexity: {
        average: 3.2,
        high: ['src/components/DataProcessor.tsx', 'src/utils/algorithms.ts'],
        recommendations: [
          'Break down DataProcessor into smaller components',
          'Extract complex logic into separate utility functions',
          'Add unit tests for complex algorithms'
        ]
      },
      dependencies: {
        total: 45,
        outdated: 8,
        vulnerable: 2,
        list: [
          { name: 'lodash', version: '4.17.19', status: 'vulnerable' },
          { name: 'express', version: '4.17.1', status: 'outdated' },
          { name: 'react', version: '18.2.0', status: 'current' },
          { name: 'typescript', version: '4.9.5', status: 'current' },
          { name: 'axios', version: '0.27.2', status: 'outdated' }
        ]
      },
      architecture: {
        pattern: 'Component-based React with Express Backend',
        components: [
          { name: 'Frontend', type: 'React App', connections: ['API Gateway'] },
          { name: 'API Gateway', type: 'Express Server', connections: ['Database', 'Auth Service'] },
          { name: 'Database', type: 'PostgreSQL', connections: [] },
          { name: 'Auth Service', type: 'JWT/OAuth', connections: ['Database'] }
        ],
        suggestions: [
          'Consider adding Redis caching layer',
          'Implement API rate limiting',
          'Add monitoring and logging',
          'Set up CI/CD pipeline'
        ]
      },
      quality: {
        documentation: 45,
        testCoverage: 68,
        codeStyle: 82,
        maintainability: 76
      },
      brutalHonesty: {
        productionReadiness: 23,
        overallGrade: 'F',
        messages: [
          {
            category: 'security',
            severity: 'critical',
            title: 'Security',
            brutalmessage: "Your API keys are more exposed than a nudist at a polar bear convention",
            helpfulGuidance: "Move sensitive data to environment variables, add input validation, and implement proper authentication.",
            grade: 'F',
            emoji: '🔴',
            readinessImpact: 30
          },
          {
            category: 'error_handling',
            severity: 'error',
            title: 'Error Handling',
            brutalmessage: "23 unhandled promise rejections. That's not a bug, that's a feature",
            helpfulGuidance: "Add try-catch blocks, implement error boundaries, and handle promise rejections.",
            grade: 'F',
            emoji: '🔴',
            readinessImpact: 35
          },
          {
            category: 'performance',
            severity: 'warning',
            title: 'Performance',
            brutalmessage: "Loading 50MB for a todo app? Did you include the entire internet?",
            helpfulGuidance: "Optimize your imports, reduce bundle size, and simplify complex algorithms.",
            grade: 'D',
            emoji: '🟡',
            readinessImpact: 20
          },
          {
            category: 'documentation',
            severity: 'warning',
            title: 'Documentation',
            brutalmessage: "Your README promises OAuth, but you built basic auth. Truth in advertising much?",
            helpfulGuidance: "Add function documentation, update your README, and keep docs in sync with code.",
            grade: 'D',
            emoji: '🟠',
            readinessImpact: 15
          },
          {
            category: 'quality',
            severity: 'info',
            title: 'Code Quality',
            brutalmessage: "Hey, at least this part works! You've got a solid foundation here.",
            helpfulGuidance: "Your code is in good shape. Consider adding some advanced patterns or optimization.",
            grade: 'B',
            emoji: '🟢',
            readinessImpact: 0
          }
        ],
        encouragement: "Don't worry, we'll fix this together in about 15 minutes",
        nextSteps: [
          "1️⃣ Security: Move sensitive data to environment variables",
          "2️⃣ Error Handling: Add try-catch blocks and error boundaries",
          "3️⃣ Performance: Optimize imports and reduce bundle size",
          "4️⃣ Documentation: Update README and keep docs in sync"
        ],
        timeToFix: "2.5 hours",
        funFacts: [
          "You've written 15,420 lines of code. That's 308 pages of a book!",
          "247 functions! You could start your own function library.",
          "Your code complexity is better than 40% of developers. Room for improvement!"
        ]
      },
      roadmap: [
        {
          phase: 'Quick Wins (1-2 weeks)',
          duration: '2 weeks',
          tasks: [
            {
              title: 'Update vulnerable dependencies',
              description: 'Update lodash and other vulnerable packages to secure versions',
              priority: 'high',
              effort: '4 hours'
            },
            {
              title: 'Add missing unit tests',
              description: 'Increase test coverage for critical components',
              priority: 'high',
              effort: '12 hours'
            },
            {
              title: 'Improve documentation',
              description: 'Add README and API documentation',
              priority: 'medium',
              effort: '8 hours'
            }
          ]
        },
        {
          phase: 'Architecture Improvements (3-4 weeks)',
          duration: '3 weeks',
          tasks: [
            {
              title: 'Refactor complex components',
              description: 'Break down large components into smaller, reusable pieces',
              priority: 'medium',
              effort: '20 hours'
            },
            {
              title: 'Add caching layer',
              description: 'Implement Redis caching for improved performance',
              priority: 'medium',
              effort: '16 hours'
            }
          ]
        },
        {
          phase: 'Advanced Features (5-8 weeks)',
          duration: '4 weeks',
          tasks: [
            {
              title: 'Implement monitoring',
              description: 'Add application monitoring and alerting',
              priority: 'low',
              effort: '24 hours'
            },
            {
              title: 'Set up CI/CD',
              description: 'Automate testing and deployment pipeline',
              priority: 'low',
              effort: '16 hours'
            }
          ]
        }
      ]
    };
    
    setAnalysisData(mockData);
    const timestamp = new Date().toISOString();
    setLastAnalysisTime(timestamp);
    
    // Cache the mock data
    if (projectState?.path) {
      await analysisCacheService.setCachedProjectAnalysis(projectState.path, mockData);
    }
  };

  const startAnalysis = async () => {
    if (!projectState?.path) return;
    
    setIsAnalyzing(true);
    
    try {
      // Force a new brutal honesty analysis
      const brutalHonestyReport = await brutalHonestyService.generateAnalysis(projectState.path);
      setRealBrutalHonesty(brutalHonestyReport);
      console.log('ProjectAnalysisWidget: New brutal honesty analysis completed');
      
      // Force a new project analysis (bypass cache)
      const result = await projectAnalysisService.analyzeProject(projectState.path);
      setRealAnalysisData(result);
      
      // Convert to widget format
      const widgetData: ProjectAnalysisData = {
        totalFiles: result.structure.totalFiles,
        linesOfCode: result.structure.totalLines,
        testCoverage: result.health.testCoverage,
        healthScore: result.health.overallScore,
        complexity: result.health.complexity,
        quality: {
          documentation: result.health.documentation,
          testCoverage: result.health.testCoverage,
          codeStyle: result.health.maintainability,
          maintainability: result.health.maintainability
        },
        roadmap: result.roadmap.map(phase => ({
          phase: phase.phase,
          duration: phase.duration,
          tasks: phase.tasks
        })),
        brutalHonesty: brutalHonestyReport
      };
      
      setAnalysisData(widgetData);
      const timestamp = new Date().toISOString();
      setLastAnalysisTime(timestamp);
      
      // Cache the new result
      await analysisCacheService.setCachedProjectAnalysis(projectState.path, widgetData);
      console.log('ProjectAnalysisWidget: New analysis completed and cached');
      
    } catch (error) {
      console.error('Real analysis failed, falling back to mock data:', error);
      
      // Simulate analysis progress for mock data
      setTimeout(async () => {
        await loadMockAnalysis();
      }, 1000);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getHealthColor = (score: number): string => {
    if (score >= 80) return '#4caf50';
    if (score >= 60) return '#ff9800';
    return '#f44336';
  };

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return '#f44336';
      case 'medium': return '#ff9800';
      case 'low': return '#4caf50';
      default: return '#757575';
    }
  };

  const renderOverviewView = () => (
    <div className="analysis-overview">
      {analysisData && (
        <div className="metrics-summary">
          <div className="metric-card">
            <div className="metric-value">{analysisData.totalFiles}</div>
            <div className="metric-label">Files</div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{(analysisData.linesOfCode! / 1000).toFixed(1)}K</div>
            <div className="metric-label">Lines</div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{analysisData.testCoverage?.toFixed(1)}%</div>
            <div className="metric-label">Coverage</div>
          </div>
          <div className="metric-card">
            <div className="metric-value" style={{ color: getHealthColor(analysisData.healthScore!) }}>
              {analysisData.healthScore}
            </div>
            <div className="metric-label">Health</div>
          </div>
        </div>
      )}
    </div>
  );

  const renderHealthView = () => (
    <div className="health-dashboard">
      {analysisData && (
        <>
          <div className="dashboard-header">
            <div className="health-score">
              <div className="score-circle" style={{
                background: `conic-gradient(${getHealthColor(analysisData.healthScore!)} 0deg ${analysisData.healthScore! * 3.6}deg, #E0E0E0 ${analysisData.healthScore! * 3.6}deg 360deg)`
              }}>
                <div className="score-content">
                  <div className="score-number">{analysisData.healthScore}</div>
                  <div className="score-label">Health</div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="quality-metrics">
            <div className="quality-item">
              <span>Documentation</span>
              <div className="quality-bar">
                <div className="quality-fill" style={{ width: `${analysisData.quality!.documentation}%` }}></div>
              </div>
              <span>{analysisData.quality!.documentation?.toFixed(1)}%</span>
            </div>
            <div className="quality-item">
              <span>Test Coverage</span>
              <div className="quality-bar">
                <div className="quality-fill" style={{ width: `${analysisData.quality!.testCoverage}%` }}></div>
              </div>
              <span>{analysisData.quality!.testCoverage?.toFixed(1)}%</span>
            </div>
            <div className="quality-item">
              <span>Code Style</span>
              <div className="quality-bar">
                <div className="quality-fill" style={{ width: `${analysisData.quality!.codeStyle}%` }}></div>
              </div>
              <span>{analysisData.quality!.codeStyle?.toFixed(1)}%</span>
            </div>
            <div className="quality-item">
              <span>Maintainability</span>
              <div className="quality-bar">
                <div className="quality-fill" style={{ width: `${analysisData.quality!.maintainability}%` }}></div>
              </div>
              <span>{analysisData.quality!.maintainability?.toFixed(1)}%</span>
            </div>
          </div>

          <div className="dependencies-section">
            <h4>Dependencies</h4>
            <div className="dependency-stats">
              <span className="stat-item">
                <span className="stat-number">{analysisData.dependencies!.total}</span> Total
              </span>
              <span className="stat-item warning">
                <span className="stat-number">{analysisData.dependencies!.outdated}</span> Outdated
              </span>
              <span className="stat-item critical">
                <span className="stat-number">{analysisData.dependencies!.vulnerable}</span> Vulnerable
              </span>
            </div>
          </div>
        </>
      )}
    </div>
  );

  const renderArchitectureView = () => (
    <div className="architecture-view">
      {analysisData && (
        <>
          <div className="architecture-pattern">
            <h4>Architecture Pattern</h4>
            <div className="pattern-badge">{analysisData.architecture!.pattern}</div>
          </div>
          
          <div className="components-grid">
            {analysisData.architecture!.components.map((component, index) => (
              <div key={index} className="component-node">
                <div className="component-name">{component.name}</div>
                <div className="component-type">{component.type}</div>
                {component.connections.length > 0 && (
                  <div className="component-connections">
                    → {component.connections.join(', ')}
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="architecture-suggestions">
            <h4>Suggestions</h4>
            {analysisData.architecture!.suggestions.map((suggestion, index) => (
              <div key={index} className="suggestion-item">
                💡 {suggestion}
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );

  const renderRoadmapView = () => (
    <div className="roadmap-view">
      {analysisData && (
        <div className="roadmap-phases">
          {analysisData.roadmap!.map((phase, index) => (
            <div key={index} className="roadmap-phase">
              <div className="phase-header">
                <h4>{phase.phase}</h4>
                <span className="phase-duration">{phase.duration}</span>
              </div>
              <div className="phase-tasks">
                {phase.tasks.map((task, taskIndex) => (
                  <div key={taskIndex} className="task-card" style={{
                    borderLeft: `3px solid ${getPriorityColor(task.priority)}`
                  }}>
                    <div className="task-header">
                      <span className="task-title">{task.title}</span>
                      <span className="task-effort">{task.effort}</span>
                    </div>
                    <div className="task-description">{task.description}</div>
                    <div className="task-priority">
                      <span className={`priority-badge priority-${task.priority}`}>
                        {task.priority.toUpperCase()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const getSeverityColor = (severity: string): string => {
    switch (severity) {
      case 'critical': return '#f44336';
      case 'error': return '#f44336';
      case 'warning': return '#ff9800';
      case 'info': return '#4caf50';
      default: return '#757575';
    }
  };

  const getGradeColor = (grade: string): string => {
    switch (grade) {
      case 'A': return '#4caf50';
      case 'B': return '#8bc34a';
      case 'C': return '#ffeb3b';
      case 'D': return '#ff9800';
      case 'F': return '#f44336';
      default: return '#757575';
    }
  };

  const renderBrutalHonestyView = () => {
    // Use real brutal honesty data if available, otherwise fall back to analysis data
    const brutalHonestyData = realBrutalHonesty || analysisData?.brutalHonesty;
    
    return (
      <div className="brutal-honesty-view">
        {brutalHonestyData ? (
          <>
            <div className="brutality-header">
              <div className="production-readiness">
                <div className="readiness-score">
                  <span className="score-label">Production Readiness:</span>
                  <span className="score-value" style={{ color: getGradeColor(brutalHonestyData.overallGrade) }}>
                    {brutalHonestyData.productionReadiness}%
                  </span>
                  <span className="score-emoji">
                    {brutalHonestyData.productionReadiness > 80 ? '🚀' : 
                     brutalHonestyData.productionReadiness > 60 ? '😬' : 
                     brutalHonestyData.productionReadiness > 40 ? '😰' : '💀'}
                  </span>
                </div>
                <div className="overall-grade">
                  <span className="grade-label">Overall Grade:</span>
                  <span className="grade-badge" style={{ 
                    backgroundColor: getGradeColor(brutalHonestyData.overallGrade),
                    color: 'white'
                  }}>
                    {brutalHonestyData.overallGrade}
                  </span>
                </div>
              </div>
              {realBrutalHonesty && (
                <div className="data-source-indicator">
                  <span className="data-badge real">🔥 Live Analysis</span>
                </div>
              )}
            </div>

            <div className="brutality-messages">
              {brutalHonestyData.messages.map((message, index) => (
                <div key={index} className="brutal-message" style={{
                  borderLeft: `4px solid ${getSeverityColor(message.severity)}`
                }}>
                  <div className="message-header">
                    <span className="message-emoji">{message.emoji}</span>
                    <span className="message-title">{message.title}: {message.grade}</span>
                  </div>
                  <div className="message-brutal" style={{ 
                    fontStyle: 'italic',
                    color: '#333',
                    margin: '8px 0'
                  }}>
                    "{message.brutalmessage}"
                  </div>
                  <div className="message-guidance" style={{ 
                    color: '#666',
                    fontSize: '14px'
                  }}>
                    💡 {message.helpfulGuidance}
                  </div>
                </div>
              ))}
            </div>

            <div className="encouragement-section">
              <div className="encouragement-message">
                <span className="encouragement-emoji">🤝</span>
                <span className="encouragement-text">{brutalHonestyData.encouragement}</span>
              </div>
            </div>

            <div className="next-steps-section">
              <h4>Your Path to Production ({brutalHonestyData.timeToFix})</h4>
              <div className="next-steps-list">
                {brutalHonestyData.nextSteps.map((step, index) => (
                  <div key={index} className="next-step">
                    {step}
                  </div>
                ))}
              </div>
            </div>

            {brutalHonestyData.funFacts && brutalHonestyData.funFacts.length > 0 && (
              <div className="fun-facts-section">
                <h4>📊 Fun Facts</h4>
                <div className="fun-facts-list">
                  {brutalHonestyData.funFacts.map((fact, index) => (
                    <div key={index} className="fun-fact">
                      • {fact}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {brutalHonestyData.detailedMetrics && (
              <div className="detailed-metrics-section">
                <h4>📊 Detailed Metrics</h4>
                <div className="metrics-grid">
                  <div className="metric-card">
                    <span className="metric-label">Complexity</span>
                    <span className="metric-value">{brutalHonestyData.detailedMetrics.complexityScore}%</span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Maintainability</span>
                    <span className="metric-value">{brutalHonestyData.detailedMetrics.maintainability}%</span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Test Coverage</span>
                    <span className="metric-value">{brutalHonestyData.detailedMetrics.testCoverage}%</span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Security</span>
                    <span className="metric-value">{brutalHonestyData.detailedMetrics.securityScore}%</span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Performance</span>
                    <span className="metric-value">{brutalHonestyData.detailedMetrics.performanceScore}%</span>
                  </div>
                  <div className="metric-card">
                    <span className="metric-label">Tech Debt</span>
                    <span className="metric-value">{brutalHonestyData.detailedMetrics.technicalDebt}%</span>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="no-brutal-honesty">
            <div className="prompt-icon">💬</div>
            <div className="prompt-text">
              <h4>Ready for some brutal honesty?</h4>
              <p>Run analysis to get real feedback about your code quality and production readiness.</p>
            </div>
            <button className="analyze-button" onClick={startAnalysis}>
              Get Brutal Feedback
            </button>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`dashboardCard project-analysis-widget ${className || ''}`}>
      <div className="card-header">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
          <h3 className="cardTitle">Project Analysis</h3>
          {lastAnalysisTime && (
            <div style={{ fontSize: '11px', color: '#888' }}>
              {analysisCacheService.formatTimestamp(lastAnalysisTime)}
            </div>
          )}
        </div>
        <div className="analysis-tabs">
          <button 
            className={`tab-button ${currentView === 'overview' ? 'active' : ''}`}
            onClick={() => setCurrentView('overview')}
          >
            Overview
          </button>
          <button 
            className={`tab-button ${currentView === 'health' ? 'active' : ''}`}
            onClick={() => setCurrentView('health')}
          >
            Health
          </button>
          <button 
            className={`tab-button ${currentView === 'architecture' ? 'active' : ''}`}
            onClick={() => setCurrentView('architecture')}
          >
            Architecture
          </button>
          <button 
            className={`tab-button ${currentView === 'roadmap' ? 'active' : ''}`}
            onClick={() => setCurrentView('roadmap')}
          >
            Roadmap
          </button>
          <button 
            className={`tab-button ${currentView === 'brutal-honesty' ? 'active' : ''}`}
            onClick={() => setCurrentView('brutal-honesty')}
          >
            Brutal Honesty
          </button>
        </div>
      </div>
      
      <div className="cardContent">
        {!analysisData && !isAnalyzing && (
          <div className="analysis-prompt">
            <div className="prompt-icon">🔍</div>
            <div className="prompt-text">
              <h4>Ready to analyze your project?</h4>
              <p>Get insights into code quality, architecture, and improvement opportunities.</p>
            </div>
            <button className="analyze-button" onClick={startAnalysis}>
              Start Analysis
            </button>
          </div>
        )}
        
        {isAnalyzing && (
          <div className="analysis-in-progress">
            <SpinningBrain 
              size="large" 
              message="Analyzing your project..."
            />
            <div className="analysis-status">
              <p>This may take a few moments while we examine your code structure, quality, and dependencies.</p>
            </div>
          </div>
        )}

        {analysisData && (
          <div className="analysis-content">
            {currentView === 'overview' && renderOverviewView()}
            {currentView === 'health' && renderHealthView()}
            {currentView === 'architecture' && renderArchitectureView()}
            {currentView === 'roadmap' && renderRoadmapView()}
            {currentView === 'brutal-honesty' && renderBrutalHonestyView()}
          </div>
        )}
      </div>

      {analysisData && (
        <div className="taskActions">
          <button 
            onClick={startAnalysis}
            disabled={isAnalyzing}
            style={{
              background: 'none',
              border: 'none',
              color: '#007acc',
              cursor: isAnalyzing ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              textDecoration: 'none',
              opacity: isAnalyzing ? 0.6 : 1,
              marginRight: '8px'
            }}
          >
            {isAnalyzing ? '🔄 Analyzing...' : '🔄 Re-analyze'}
          </button>
          <a href="#" className="cardLink" style={{ fontSize: '12px' }}>
            Export Report →
          </a>
        </div>
      )}
    </div>
  );
};

export default ProjectAnalysisWidget;