/**
 * ClerkAuthService.ts
 * 
 * Service for handling Clerk authentication in the IDE.
 * Uses real Clerk authentication with fallback to development auth.
 */

import { Clerk } from '@clerk/clerk-js';
import { CLERK_PUBLISHABLE_KEY } from '../../config';
import { apiClient } from './ApiClient';

export interface ClerkUser {
  id: string;
  emailAddresses: Array<{ emailAddress: string }>;
  firstName?: string;
  lastName?: string;
  username?: string;
}

// Development mock user that matches backend expectations
const MOCK_CLERK_USER: ClerkUser = {
  id: 'user_test_123456789',
  emailAddresses: [{ emailAddress: '<EMAIL>' }],
  firstName: 'Admin',
  lastName: 'User',
  username: 'admin'
};

// Mock JWT token payload that matches what Clerk would send
const createMockClerkToken = (userId: string): string => {
  const header = {
    alg: 'RS256',
    typ: 'JWT',
    kid: 'ins_test_key'
  };

  const payload = {
    sub: '1', // Use numeric user ID that matches backend expectation
    iss: 'https://leading-monkfish-43.clerk.accounts.dev',
    aud: 'kapi-ide',
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
    iat: Math.floor(Date.now() / 1000),
    azp: 'kapi-ide',
    session_id: 'sess_test_123456789',
    at_hash: 'test_hash'
  };

  // For development, we'll create a proper JWT structure
  // The backend will decode this in development mode
  const base64Header = btoa(JSON.stringify(header));
  const base64Payload = btoa(JSON.stringify(payload));
  const signature = 'mock_signature_for_development';
  
  return `${base64Header}.${base64Payload}.${signature}`;
};

class ClerkAuthService {
  private user: ClerkUser | null = null;
  private token: string | null = null;

  /**
   * Initialize with development mock authentication
   */
  async initializeDevelopmentAuth(): Promise<void> {
    // console.log('ClerkAuthService: Initializing development authentication');
    
    // Set mock user and simple development token
    this.user = MOCK_CLERK_USER;
    this.token = 'kapi_dev_ide_token_2024'; // Development bypass token that matches backend
    
    // Store token for API calls
    localStorage.setItem('token', this.token);
    localStorage.setItem('clerk_user', JSON.stringify(this.user));
    
    // console.log('ClerkAuthService: Development authentication initialized with token:', this.token);
    // console.log('ClerkAuthService: Token stored in localStorage:', localStorage.getItem('token'));
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.token || !!localStorage.getItem('token');
  }

  /**
   * Get current user
   */
  getUser(): ClerkUser | null {
    if (this.user) {
      return this.user;
    }
    
    const storedUser = localStorage.getItem('clerk_user');
    if (storedUser) {
      try {
        this.user = JSON.parse(storedUser);
        return this.user;
      } catch (e) {
        console.error('Error parsing stored user:', e);
      }
    }
    
    return null;
  }

  /**
   * Get authentication token
   */
  getToken(): string | null {
    if (this.token) {
      return this.token;
    }
    
    this.token = localStorage.getItem('token');
    return this.token;
  }

  /**
   * Sign out
   */
  async signOut(): Promise<void> {
    this.user = null;
    this.token = null;
    localStorage.removeItem('token');
    localStorage.removeItem('clerk_user');
    console.log('ClerkAuthService: User signed out');
  }

  /**
   * Test the authentication with the backend
   */
  async testAuthentication(): Promise<boolean> {
    try {
      // Try to make an authenticated request
      const response = await apiClient.get('/conversations');
      console.log('ClerkAuthService: Authentication test successful');
      return true;
    } catch (error) {
      console.error('ClerkAuthService: Authentication test failed:', error);
      return false;
    }
  }

  /**
   * Initialize the service
   */
  async initialize(): Promise<void> {
    // For now, just use development auth
    // In the future, this would initialize real Clerk authentication
    await this.initializeDevelopmentAuth();
  }
}

// Export singleton instance
export const clerkAuthService = new ClerkAuthService();
export default clerkAuthService;