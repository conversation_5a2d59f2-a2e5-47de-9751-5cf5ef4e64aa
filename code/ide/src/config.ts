// Environment Mode - simplified approach
export const isDev = import.meta.env.MODE === 'development';

// Feature flags - direct approach without environment dependencies
export const useMockAuth = import.meta.env.VITE_USE_MOCK_AUTH === 'true';
export const useDebugProviders = import.meta.env.VITE_USE_DEBUG_PROVIDERS === 'true';

// Optional development-only logging
if (isDev) {
  console.log('Development configuration:', {
    mode: import.meta.env.MODE,
    useMockAuth,
    useDebugProviders,
    apiUrl: import.meta.env.VITE_API_URL,
    llmBaseUrl: import.meta.env.VITE_LLM_BASE_URL
  });
}

// Base URL for API requests - determined by environment
// In production, this should be set to the actual backend URL
// In development, it defaults to localhost:3000
export const API_BASE_URL = import.meta.env.VITE_API_URL || (() => {
  // For production builds, we should not default to localhost
  if (import.meta.env.PROD) {
    throw new Error('VITE_API_URL environment variable must be set for production builds');
  }
  return 'http://localhost:3000';
})();

// API version prefix - NodeJS backend uses /api (not /api/v1)
export const API_VERSION = '/api';

// Versioned API URL
export const VERSIONED_API_URL = `${API_BASE_URL}${API_VERSION}`;

// Debug logging
console.log('[Config] API Configuration:', {
  API_BASE_URL,
  API_VERSION,
  VERSIONED_API_URL,
  VITE_API_URL: import.meta.env.VITE_API_URL
});

// Development IDE token used for bypassing Clerk auth in development
export const MOCK_USER_TOKEN = 'kapi_dev_ide_token_2024';

// Clerk configuration for authentication - only publishable key (safe for client-side)
export const CLERK_PUBLISHABLE_KEY = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

// Constants for inactivity detection
export const INACTIVITY_TIMEOUT = 15 * 60 * 1000; // 15 minutes in milliseconds

// Feature flags for controlling IDE functionality
export const ENABLE_CODE_ANALYSIS = false; // Disabled due to HTTP 429 rate limiting