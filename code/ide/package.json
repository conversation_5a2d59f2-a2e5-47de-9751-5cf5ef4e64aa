{"name": "kapi-ide", "version": "0.1.0", "main": "dist/main/main.js", "description": "Let's set the world on fire!", "author": "<PERSON><PERSON><PERSON>", "license": "Proprietary", "private": true, "keywords": ["kapi", "ide", "electron"], "debug": {"env": {"VITE_DEV_SERVER_URL": "http://127.0.0.1:9999"}}, "scripts": {"dev": "cross-env NODE_ENV=development VITE_DEV_SERVER_URL=http://localhost:5173 vite", "dev:prod": "cross-env NODE_ENV=development vite --mode production", "build": "tsc --noEmit && vite build && electron-builder", "build:main": "esbuild src/main.ts --bundle --platform=node --outfile=dist/main/main.js --external:node-pty --external:electron", "copy:main": "echo 'No copy needed'", "build:prod": "npm run build && npm run build:main && npm run copy:main && npm run build:preload", "build:test": "cross-env VITE_USE_MOCK_AUTH=true npm run build:prod", "preview": "vite preview", "build:preload": "tsc -p tsconfig.preload.json", "rebuild": "electron-rebuild -f -w node-pty", "package": "npm run build:prod && electron-builder", "postinstall": "electron-builder install-app-deps", "lint": "eslint 'src/**/*.{ts,tsx}'", "lint:fix": "eslint 'src/**/*.{ts,tsx}' --fix", "lint:check": "eslint 'src/**/*.{ts,tsx}' --max-warnings 0", "security-audit": "./security-audit.sh", "update-deps": "npm update && npm audit fix", "type-check": "tsc --noEmit", "test:unit": "jest", "test:e2e": "rm -rf test-results && playwright test --config=playwright.electron.config.ts", "test:e2e:debug": "rm -rf test-results && playwright test --config=playwright.electron.config.ts --debug", "test:e2e:ui": "rm -rf test-results && playwright test --config=playwright.electron.config.ts --ui", "run_tests": "./run_tests.sh"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@eslint/js": "^9.23.0", "@playwright/test": "^1.51.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@types/styled-components": "^5.1.34", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "@vitejs/plugin-react": "^4.3.4", "babel-jest": "^29.7.0", "buffer": "^6.0.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "electron": "^32.3.3", "electron-builder": "^24.13.3", "electron-rebuild": "^3.2.9", "eslint": "^8.57.0", "eslint-plugin-react": "^7.37.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-abi": "^3.75.0", "playwright": "^1.51.1", "postcss": "^8.5.3", "postcss-loader": "^8.1.1", "process": "^0.11.10", "react-test-renderer": "^19.1.0", "start-server-and-test": "^2.0.11", "ts-jest": "^29.3.1", "ts-node": "^10.9.2", "typescript": "^5.0.3", "typescript-eslint": "^8.29.0", "vite": "^5.4.19", "vite-plugin-electron": "^0.15.4", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-static-copy": "^2.3.1"}, "build": {"appId": "com.kapi.ide", "productName": "KAPI IDE", "files": ["dist/**/*", "node_modules/**/*", "package.json"], "directories": {"buildResources": "assets"}, "mac": {"icon": "buildResources/icon.icns", "category": "public.app-category.developer-tools", "target": "dmg"}, "win": {"icon": "buildResources/icon.ico", "target": "nsis"}, "linux": {"icon": "buildResources/icon.png"}}, "dependencies": {"@clerk/clerk-js": "^5.74.1", "@excalidraw/excalidraw": "^0.18.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "chokidar": "^4.0.3", "chromadb": "^2.4.6", "dompurify": "^3.2.5", "lodash": "^4.17.21", "marked": "^15.0.8", "mermaid": "^11.6.0", "monaco-editor": "^0.52.2", "node-gyp": "^11.2.0", "node-pty": "^1.1.0-beta34", "react": "^19.1.0", "react-dom": "^19.1.0", "react-resizable-panels": "^2.1.7", "react-router-dom": "^7.6.1", "react-syntax-highlighter": "^15.6.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.17", "ts-morph": "^26.0.0", "uuid": "^11.1.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-web-links": "^0.9.0"}}